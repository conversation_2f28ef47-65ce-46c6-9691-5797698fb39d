import{_ as F}from"./tslib-BDyQ-Jie.js";var Fo=function(){function e(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return e}(),Bo=function(){function e(){this.browser=new Fo,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return e}(),k=new Bo;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(k.wxa=!0,k.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?k.worker=!0:!k.hasGlobalWindow||"Deno"in window?(k.node=!0,k.svgSupported=!0):Ho(navigator.userAgent,k);function Ho(e,t){var r=t.browser,i=e.match(/Firefox\/([\d.]+)/),n=e.match(/MSIE\s([\d.]+)/)||e.match(/Trident\/.+?rv:(([\d.]+))/),a=e.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(e);i&&(r.firefox=!0,r.version=i[1]),n&&(r.ie=!0,r.version=n[1]),a&&(r.edge=!0,r.version=a[1],r.newEdge=+a[1].split(".")[0]>18),o&&(r.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,t.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||r.ie&&+r.version>=9}var ln=12,zo="sans-serif",lr=ln+"px "+zo,ko=20,No=100,Wo="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Yo(e){var t={};if(typeof JSON>"u")return t;for(var r=0;r<e.length;r++){var i=String.fromCharCode(r+32),n=(e.charCodeAt(r)-ko)/No;t[i]=n}return t}var Go=Yo(Wo),vr={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var e,t;return function(r,i){if(!e){var n=vr.createCanvas();e=n&&n.getContext("2d")}if(e)return t!==i&&(t=e.font=i||lr),e.measureText(r);r=r||"",i=i||lr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||ln,s=0;if(i.indexOf("mono")>=0)s=o*r.length;else for(var h=0;h<r.length;h++){var f=Go[r[h]];s+=f==null?o:f*o}return{width:s}}}(),loadImage:function(e,t,r){var i=new Image;return i.onload=t,i.onerror=r,i.src=e,i}};function Ou(e){for(var t in vr)e[t]&&(vr[t]=e[t])}var Da=he(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(e,t){return e["[object "+t+"]"]=!0,e},{}),Aa=he(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(e,t){return e["[object "+t+"Array]"]=!0,e},{}),Fr=Object.prototype.toString,Je=Array.prototype,Xo=Je.forEach,qo=Je.filter,vn=Je.slice,$o=Je.map,Ln=(function(){}).constructor,de=Ln?Ln.prototype:null,cn="__proto__",Zo=2311;function dn(){return Zo++}function je(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];typeof console<"u"&&console.error.apply(console,e)}function cr(e){if(e==null||typeof e!="object")return e;var t=e,r=Fr.call(e);if(r==="[object Array]"){if(!Dr(e)){t=[];for(var i=0,n=e.length;i<n;i++)t[i]=cr(e[i])}}else if(Aa[r]){if(!Dr(e)){var a=e.constructor;if(a.from)t=a.from(e);else{t=new a(e.length);for(var i=0,n=e.length;i<n;i++)t[i]=e[i]}}}else if(!Da[r]&&!Dr(e)&&!Ge(e)){t={};for(var o in e)e.hasOwnProperty(o)&&o!==cn&&(t[o]=cr(e[o]))}return t}function tr(e,t,r){if(!Dt(t)||!Dt(e))return r?cr(t):e;for(var i in t)if(t.hasOwnProperty(i)&&i!==cn){var n=e[i],a=t[i];Dt(a)&&Dt(n)&&!Ir(a)&&!Ir(n)&&!Ge(a)&&!Ge(n)&&!Ni(a)&&!Ni(n)&&!Dr(a)&&!Dr(n)?tr(n,a,r):(r||!(i in e))&&(e[i]=cr(t[i]))}return e}function Uo(e,t){for(var r=e[0],i=1,n=e.length;i<n;i++)r=tr(r,e[i],t);return r}function H(e,t){if(Object.assign)Object.assign(e,t);else for(var r in t)t.hasOwnProperty(r)&&r!==cn&&(e[r]=t[r]);return e}function Ht(e,t,r){for(var i=tt(t),n=0,a=i.length;n<a;n++){var o=i[n];(r?t[o]!=null:e[o]==null)&&(e[o]=t[o])}return e}var Vo=vr.createCanvas;function ct(e,t){if(e){if(e.indexOf)return e.indexOf(t);for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r}return-1}function Qo(e,t){var r=e.prototype;function i(){}i.prototype=t.prototype,e.prototype=new i;for(var n in r)r.hasOwnProperty(n)&&(e.prototype[n]=r[n]);e.prototype.constructor=e,e.superClass=t}function pn(e,t,r){if(e="prototype"in e?e.prototype:e,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(r?t[a]!=null:e[a]==null)&&(e[a]=t[a])}else Ht(e,t,r)}function pt(e){return!e||typeof e=="string"?!1:typeof e.length=="number"}function j(e,t,r){if(e&&t)if(e.forEach&&e.forEach===Xo)e.forEach(t,r);else if(e.length===+e.length)for(var i=0,n=e.length;i<n;i++)t.call(r,e[i],i,e);else for(var a in e)e.hasOwnProperty(a)&&t.call(r,e[a],a,e)}function dr(e,t,r){if(!e)return[];if(!t)return _n(e);if(e.map&&e.map===$o)return e.map(t,r);for(var i=[],n=0,a=e.length;n<a;n++)i.push(t.call(r,e[n],n,e));return i}function he(e,t,r,i){if(e&&t){for(var n=0,a=e.length;n<a;n++)r=t.call(i,r,e[n],n,e);return r}}function ki(e,t,r){if(!e)return[];if(!t)return _n(e);if(e.filter&&e.filter===qo)return e.filter(t,r);for(var i=[],n=0,a=e.length;n<a;n++)t.call(r,e[n],n,e)&&i.push(e[n]);return i}function Ko(e,t,r){if(e&&t){for(var i=0,n=e.length;i<n;i++)if(t.call(r,e[i],i,e))return e[i]}}function tt(e){if(!e)return[];if(Object.keys)return Object.keys(e);var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}function Jo(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];return function(){return e.apply(t,r.concat(vn.call(arguments)))}}var jo=de&&fe(de.bind)?de.call.bind(de.bind):Jo;function ts(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return function(){return e.apply(this,t.concat(vn.call(arguments)))}}function Ir(e){return Array.isArray?Array.isArray(e):Fr.call(e)==="[object Array]"}function fe(e){return typeof e=="function"}function ee(e){return typeof e=="string"}function rs(e){return Fr.call(e)==="[object String]"}function Jr(e){return typeof e=="number"}function Dt(e){var t=typeof e;return t==="function"||!!e&&t==="object"}function Ni(e){return!!Da[Fr.call(e)]}function Ea(e){return!!Aa[Fr.call(e)]}function Ge(e){return typeof e=="object"&&typeof e.nodeType=="number"&&typeof e.ownerDocument=="object"}function ue(e){return e.colorStops!=null}function Ia(e){return e.image!=null}function es(e){return Fr.call(e)==="[object RegExp]"}function Oa(e){return e!==e}function is(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=0,i=e.length;r<i;r++)if(e[r]!=null)return e[r]}function J(e,t){return e??t}function jr(e,t,r){return e??t??r}function _n(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return vn.apply(e,t)}function Fa(e){if(typeof e=="number")return[e,e,e,e];var t=e.length;return t===2?[e[0],e[1],e[0],e[1]]:t===3?[e[0],e[1],e[2],e[1]]:e}function ns(e,t){if(!e)throw new Error(t)}function Pr(e){return e==null?null:typeof e.trim=="function"?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Ba="__ec_primitive__";function as(e){e[Ba]=!0}function Dr(e){return e[Ba]}var os=function(){function e(){this.data={}}return e.prototype.delete=function(t){var r=this.has(t);return r&&delete this.data[t],r},e.prototype.has=function(t){return this.data.hasOwnProperty(t)},e.prototype.get=function(t){return this.data[t]},e.prototype.set=function(t,r){return this.data[t]=r,this},e.prototype.keys=function(){return tt(this.data)},e.prototype.forEach=function(t){var r=this.data;for(var i in r)r.hasOwnProperty(i)&&t(r[i],i)},e}(),Ha=typeof Map=="function";function ss(){return Ha?new Map:new os}var za=function(){function e(t){var r=Ir(t);this.data=ss();var i=this;t instanceof e?t.each(n):t&&j(t,n);function n(a,o){r?i.set(a,o):i.set(o,a)}}return e.prototype.hasKey=function(t){return this.data.has(t)},e.prototype.get=function(t){return this.data.get(t)},e.prototype.set=function(t,r){return this.data.set(t,r),r},e.prototype.each=function(t,r){this.data.forEach(function(i,n){t.call(r,i,n)})},e.prototype.keys=function(){var t=this.data.keys();return Ha?Array.from(t):t},e.prototype.removeKey=function(t){this.data.delete(t)},e}();function hs(e){return new za(e)}function fs(e,t){for(var r=new e.constructor(e.length+t.length),i=0;i<e.length;i++)r[i]=e[i];for(var n=e.length,i=0;i<t.length;i++)r[i+n]=t[i];return r}function le(e,t){var r;if(Object.create)r=Object.create(e);else{var i=function(){};i.prototype=e,r=new i}return t&&H(r,t),r}function gn(e){var t=e.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function us(e,t){return e.hasOwnProperty(t)}function or(){}var ka=180/Math.PI;const Fu=Object.freeze(Object.defineProperty({__proto__:null,HashMap:za,RADIAN_TO_DEGREE:ka,assert:ns,bind:jo,clone:cr,concatArray:fs,createCanvas:Vo,createHashMap:hs,createObject:le,curry:ts,defaults:Ht,disableUserSelect:gn,each:j,eqNaN:Oa,extend:H,filter:ki,find:Ko,guid:dn,hasOwn:us,indexOf:ct,inherits:Qo,isArray:Ir,isArrayLike:pt,isBuiltInObject:Ni,isDom:Ge,isFunction:fe,isGradientObject:ue,isImagePatternObject:Ia,isNumber:Jr,isObject:Dt,isPrimitive:Dr,isRegExp:es,isString:ee,isStringSafe:rs,isTypedArray:Ea,keys:tt,logError:je,map:dr,merge:tr,mergeAll:Uo,mixin:pn,noop:or,normalizeCssArray:Fa,reduce:he,retrieve:is,retrieve2:J,retrieve3:jr,setAsPrimitive:as,slice:_n,trim:Pr},Symbol.toStringTag,{value:"Module"}));function pr(e,t){return e==null&&(e=0),t==null&&(t=0),[e,t]}function ls(e,t){return e[0]=t[0],e[1]=t[1],e}function Na(e){return[e[0],e[1]]}function vs(e,t,r){return e[0]=t,e[1]=r,e}function Wi(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e}function cs(e,t,r,i){return e[0]=t[0]+r[0]*i,e[1]=t[1]+r[1]*i,e}function Wa(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e}function yn(e){return Math.sqrt(mn(e))}var ds=yn;function mn(e){return e[0]*e[0]+e[1]*e[1]}var ps=mn;function _s(e,t,r){return e[0]=t[0]*r[0],e[1]=t[1]*r[1],e}function gs(e,t,r){return e[0]=t[0]/r[0],e[1]=t[1]/r[1],e}function ys(e,t){return e[0]*t[0]+e[1]*t[1]}function Fe(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e}function Ya(e,t){var r=yn(t);return r===0?(e[0]=0,e[1]=0):(e[0]=t[0]/r,e[1]=t[1]/r),e}function Xe(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}var Ga=Xe;function Xa(e,t){return(e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1])}var sr=Xa;function ms(e,t){return e[0]=-t[0],e[1]=-t[1],e}function ws(e,t,r,i){return e[0]=t[0]+i*(r[0]-t[0]),e[1]=t[1]+i*(r[1]-t[1]),e}function Ar(e,t,r){var i=t[0],n=t[1];return e[0]=r[0]*i+r[2]*n+r[4],e[1]=r[1]*i+r[3]*n+r[5],e}function rr(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e}function er(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e}const Bu=Object.freeze(Object.defineProperty({__proto__:null,add:Wi,applyTransform:Ar,clone:Na,copy:ls,create:pr,dist:Ga,distSquare:sr,distance:Xe,distanceSquare:Xa,div:gs,dot:ys,len:yn,lenSquare:mn,length:ds,lengthSquare:ps,lerp:ws,max:er,min:rr,mul:_s,negate:ms,normalize:Ya,scale:Fe,scaleAndAdd:cs,set:vs,sub:Wa},Symbol.toStringTag,{value:"Module"}));var yr=function(){function e(t,r){this.target=t,this.topTarget=r&&r.topTarget}return e}(),Ts=function(){function e(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return e.prototype._dragStart=function(t){for(var r=t.target;r&&!r.draggable;)r=r.parent||r.__hostTarget;r&&(this._draggingTarget=r,r.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new yr(r,t),"dragstart",t.event))},e.prototype._drag=function(t){var r=this._draggingTarget;if(r){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,r.drift(a,o,t),this.handler.dispatchToElement(new yr(r,t),"drag",t.event);var s=this.handler.findHover(i,n,r).target,h=this._dropTarget;this._dropTarget=s,r!==s&&(h&&s!==h&&this.handler.dispatchToElement(new yr(h,t),"dragleave",t.event),s&&s!==h&&this.handler.dispatchToElement(new yr(s,t),"dragenter",t.event))}},e.prototype._dragEnd=function(t){var r=this._draggingTarget;r&&(r.dragging=!1),this.handler.dispatchToElement(new yr(r,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new yr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},e}(),Br=function(){function e(t){t&&(this._$eventProcessor=t)}return e.prototype.on=function(t,r,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof r=="function"&&(n=i,i=r,r=null),!i||!t)return this;var o=this._$eventProcessor;r!=null&&o&&o.normalizeQuery&&(r=o.normalizeQuery(r)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var h={h:i,query:r,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},f=a[t].length-1,u=a[t][f];return u&&u.callAtLast?a[t].splice(f,0,h):a[t].push(h),this},e.prototype.isSilent=function(t){var r=this._$handlers;return!r||!r[t]||!r[t].length},e.prototype.off=function(t,r){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(r){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==r&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},e.prototype.trigger=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=r.length,s=n.length,h=0;h<s;h++){var f=n[h];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(f.ctx);break;case 1:f.h.call(f.ctx,r[0]);break;case 2:f.h.call(f.ctx,r[0],r[1]);break;default:f.h.apply(f.ctx,r);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},e.prototype.triggerWithContext=function(t){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=r.length,s=r[o-1],h=n.length,f=0;f<h;f++){var u=n[f];if(!(a&&a.filter&&u.query!=null&&!a.filter(t,u.query)))switch(o){case 0:u.h.call(s);break;case 1:u.h.call(s,r[0]);break;case 2:u.h.call(s,r[0],r[1]);break;default:u.h.apply(s,r.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},e}(),bs=Math.log(2);function Yi(e,t,r,i,n,a){var o=i+"-"+n,s=e.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var h=Math.round(Math.log((1<<s)-1&~n)/bs);return e[r][h]}for(var f=i|1<<r,u=r+1;i&1<<u;)u++;for(var l=0,v=0,c=0;v<s;v++){var _=1<<v;_&n||(l+=(c%2?-1:1)*e[r][v]*Yi(e,t-1,u,f,n|_,a),c++)}return a[o]=l,l}function Pn(e,t){var r=[[e[0],e[1],1,0,0,0,-t[0]*e[0],-t[0]*e[1]],[0,0,0,e[0],e[1],1,-t[1]*e[0],-t[1]*e[1]],[e[2],e[3],1,0,0,0,-t[2]*e[2],-t[2]*e[3]],[0,0,0,e[2],e[3],1,-t[3]*e[2],-t[3]*e[3]],[e[4],e[5],1,0,0,0,-t[4]*e[4],-t[4]*e[5]],[0,0,0,e[4],e[5],1,-t[5]*e[4],-t[5]*e[5]],[e[6],e[7],1,0,0,0,-t[6]*e[6],-t[6]*e[7]],[0,0,0,e[6],e[7],1,-t[7]*e[6],-t[7]*e[7]]],i={},n=Yi(r,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*Yi(r,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(h,f,u){var l=f*a[6]+u*a[7]+1;h[0]=(f*a[0]+u*a[1]+a[2])/l,h[1]=(f*a[3]+u*a[4]+a[5])/l}}}var Mn="___zrEVENTSAVED",ni=[];function Hu(e,t,r,i,n){return Gi(ni,t,i,n,!0)&&Gi(e,r,ni[0],ni[1])}function Gi(e,t,r,i,n){if(t.getBoundingClientRect&&k.domSupported&&!qa(t)){var a=t[Mn]||(t[Mn]={}),o=Cs(t,a),s=Ss(o,a,n);if(s)return s(e,r,i),!0}return!1}function Cs(e,t){var r=t.markers;if(r)return r;r=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,h=a%2,f=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[h]+":0",n[f]+":0",i[1-h]+":auto",n[1-f]+":auto",""].join("!important;"),e.appendChild(o),r.push(o)}return r}function Ss(e,t,r){for(var i=r?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],h=!0,f=0;f<4;f++){var u=e[f].getBoundingClientRect(),l=2*f,v=u.left,c=u.top;o.push(v,c),h=h&&a&&v===a[l]&&c===a[l+1],s.push(e[f].offsetLeft,e[f].offsetTop)}return h&&n?n:(t.srcCoords=o,t[i]=r?Pn(s,o):Pn(o,s))}function qa(e){return e.nodeName.toUpperCase()==="CANVAS"}var Ls=/([&<>"'])/g,Ps={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function zu(e){return e==null?"":(e+"").replace(Ls,function(t,r){return Ps[r]})}var Ms=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ai=[],Rs=k.browser.firefox&&+k.browser.version.split(".")[0]<39;function Xi(e,t,r,i){return r=r||{},i?Rn(e,t,r):Rs&&t.layerX!=null&&t.layerX!==t.offsetX?(r.zrX=t.layerX,r.zrY=t.layerY):t.offsetX!=null?(r.zrX=t.offsetX,r.zrY=t.offsetY):Rn(e,t,r),r}function Rn(e,t,r){if(k.domSupported&&e.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(qa(e)){var a=e.getBoundingClientRect();r.zrX=i-a.left,r.zrY=n-a.top;return}else if(Gi(ai,e,i,n)){r.zrX=ai[0],r.zrY=ai[1];return}}r.zrX=r.zrY=0}function wn(e){return e||window.event}function lt(e,t,r){if(t=wn(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&Xi(e,o,t,r)}else{Xi(e,t,t,r);var a=xs(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Ms.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function xs(e){var t=e.wheelDelta;if(t)return t;var r=e.deltaX,i=e.deltaY;if(r==null||i==null)return t;var n=Math.abs(i!==0?i:r),a=i>0?-1:i<0?1:r>0?-1:1;return 3*n*a}function Ds(e,t,r,i){e.addEventListener(t,r,i)}function As(e,t,r,i){e.removeEventListener(t,r,i)}var Es=function(e){e.preventDefault(),e.stopPropagation(),e.cancelBubble=!0},Is=function(){function e(){this._track=[]}return e.prototype.recognize=function(t,r,i){return this._doTrack(t,r,i),this._recognize(t)},e.prototype.clear=function(){return this._track.length=0,this},e.prototype._doTrack=function(t,r,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:r,event:t},o=0,s=n.length;o<s;o++){var h=n[o],f=Xi(i,h,{});a.points.push([f.zrX,f.zrY]),a.touches.push(h)}this._track.push(a)}},e.prototype._recognize=function(t){for(var r in oi)if(oi.hasOwnProperty(r)){var i=oi[r](this._track,t);if(i)return i}},e}();function xn(e){var t=e[1][0]-e[0][0],r=e[1][1]-e[0][1];return Math.sqrt(t*t+r*r)}function Os(e){return[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]}var oi={pinch:function(e,t){var r=e.length;if(r){var i=(e[r-1]||{}).points,n=(e[r-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=xn(i)/xn(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=Os(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:e[0].target,event:t}}}}};function hr(){return[1,0,0,1,0,0]}function $a(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=1,e[4]=0,e[5]=0,e}function Tn(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e}function Be(e,t,r){var i=t[0]*r[0]+t[2]*r[1],n=t[1]*r[0]+t[3]*r[1],a=t[0]*r[2]+t[2]*r[3],o=t[1]*r[2]+t[3]*r[3],s=t[0]*r[4]+t[2]*r[5]+t[4],h=t[1]*r[4]+t[3]*r[5]+t[5];return e[0]=i,e[1]=n,e[2]=a,e[3]=o,e[4]=s,e[5]=h,e}function qi(e,t,r){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4]+r[0],e[5]=t[5]+r[1],e}function Za(e,t,r,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],h=t[3],f=t[5],u=Math.sin(r),l=Math.cos(r);return e[0]=n*l+s*u,e[1]=-n*u+s*l,e[2]=a*l+h*u,e[3]=-a*u+l*h,e[4]=l*(o-i[0])+u*(f-i[1])+i[0],e[5]=l*(f-i[1])-u*(o-i[0])+i[1],e}function Ua(e,t,r){var i=r[0],n=r[1];return e[0]=t[0]*i,e[1]=t[1]*n,e[2]=t[2]*i,e[3]=t[3]*n,e[4]=t[4]*i,e[5]=t[5]*n,e}function Va(e,t){var r=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],h=r*o-a*i;return h?(h=1/h,e[0]=o*h,e[1]=-a*h,e[2]=-i*h,e[3]=r*h,e[4]=(i*s-o*n)*h,e[5]=(a*n-r*s)*h,e):null}function Fs(e){var t=hr();return Tn(t,e),t}const ku=Object.freeze(Object.defineProperty({__proto__:null,clone:Fs,copy:Tn,create:hr,identity:$a,invert:Va,mul:Be,rotate:Za,scale:Ua,translate:qi},Symbol.toStringTag,{value:"Module"}));var z=function(){function e(t,r){this.x=t||0,this.y=r||0}return e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,r){return this.x=t,this.y=r,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,r){this.x+=t.x*r,this.y+=t.y*r},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var r=this.x-t.x,i=this.y-t.y;return Math.sqrt(r*r+i*i)},e.prototype.distanceSquare=function(t){var r=this.x-t.x,i=this.y-t.y;return r*r+i*i},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){if(t){var r=this.x,i=this.y;return this.x=t[0]*r+t[2]*i+t[4],this.y=t[1]*r+t[3]*i+t[5],this}},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,r,i){t.x=r,t.y=i},e.copy=function(t,r){t.x=r.x,t.y=r.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,r){return t.x*r.x+t.y*r.y},e.add=function(t,r,i){t.x=r.x+i.x,t.y=r.y+i.y},e.sub=function(t,r,i){t.x=r.x-i.x,t.y=r.y-i.y},e.scale=function(t,r,i){t.x=r.x*i,t.y=r.y*i},e.scaleAndAdd=function(t,r,i,n){t.x=r.x+i.x*n,t.y=r.y+i.y*n},e.lerp=function(t,r,i,n){var a=1-n;t.x=a*r.x+n*i.x,t.y=a*r.y+n*i.y},e}(),pe=Math.min,_e=Math.max,zt=new z,kt=new z,Nt=new z,Wt=new z,Nr=new z,Wr=new z,N=function(){function e(t,r,i,n){i<0&&(t=t+i,i=-i),n<0&&(r=r+n,n=-n),this.x=t,this.y=r,this.width=i,this.height=n}return e.prototype.union=function(t){var r=pe(t.x,this.x),i=pe(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=_e(t.x+t.width,this.x+this.width)-r:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=_e(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=r,this.y=i},e.prototype.applyTransform=function(t){e.applyTransform(this,this,t)},e.prototype.calculateTransform=function(t){var r=this,i=t.width/r.width,n=t.height/r.height,a=hr();return qi(a,a,[-r.x,-r.y]),Ua(a,a,[i,n]),qi(a,a,[t.x,t.y]),a},e.prototype.intersect=function(t,r){if(!t)return!1;t instanceof e||(t=e.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,h=t.x,f=t.x+t.width,u=t.y,l=t.y+t.height,v=!(a<h||f<n||s<u||l<o);if(r){var c=1/0,_=0,y=Math.abs(a-h),d=Math.abs(f-n),p=Math.abs(s-u),g=Math.abs(l-o),m=Math.min(y,d),w=Math.min(p,g);a<h||f<n?m>_&&(_=m,y<d?z.set(Wr,-y,0):z.set(Wr,d,0)):m<c&&(c=m,y<d?z.set(Nr,y,0):z.set(Nr,-d,0)),s<u||l<o?w>_&&(_=w,p<g?z.set(Wr,0,-p):z.set(Wr,0,g)):m<c&&(c=m,p<g?z.set(Nr,0,p):z.set(Nr,0,-g))}return r&&z.copy(r,v?Nr:Wr),v},e.prototype.contain=function(t,r){var i=this;return t>=i.x&&t<=i.x+i.width&&r>=i.y&&r<=i.y+i.height},e.prototype.clone=function(){return new e(this.x,this.y,this.width,this.height)},e.prototype.copy=function(t){e.copy(this,t)},e.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},e.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},e.prototype.isZero=function(){return this.width===0||this.height===0},e.create=function(t){return new e(t.x,t.y,t.width,t.height)},e.copy=function(t,r){t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height},e.applyTransform=function(t,r,i){if(!i){t!==r&&e.copy(t,r);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=r.x*n+o,t.y=r.y*a+s,t.width=r.width*n,t.height=r.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}zt.x=Nt.x=r.x,zt.y=Wt.y=r.y,kt.x=Wt.x=r.x+r.width,kt.y=Nt.y=r.y+r.height,zt.transform(i),Wt.transform(i),kt.transform(i),Nt.transform(i),t.x=pe(zt.x,kt.x,Nt.x,Wt.x),t.y=pe(zt.y,kt.y,Nt.y,Wt.y);var h=_e(zt.x,kt.x,Nt.x,Wt.x),f=_e(zt.y,kt.y,Nt.y,Wt.y);t.width=h-t.x,t.height=f-t.y},e}(),Qa="silent";function Bs(e,t,r){return{type:e,event:r,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:Hs}}function Hs(){Es(this.event)}var zs=function(e){F(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.handler=null,r}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Br),Yr=function(){function e(t,r){this.x=t,this.y=r}return e}(),ks=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],si=new N(0,0,0,0),Ka=function(e){F(t,e);function t(r,i,n,a,o){var s=e.call(this)||this;return s._hovered=new Yr(0,0),s.storage=r,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new zs,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new Ts(s),s}return t.prototype.setHandlerProxy=function(r){this.proxy&&this.proxy.dispose(),r&&(j(ks,function(i){r.on&&r.on(i,this[i],this)},this),r.handler=this),this.proxy=r},t.prototype.mousemove=function(r){var i=r.zrX,n=r.zrY,a=Ja(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var h=this._hovered=a?new Yr(i,n):this.findHover(i,n),f=h.target,u=this.proxy;u.setCursor&&u.setCursor(f?f.cursor:"default"),s&&f!==s&&this.dispatchToElement(o,"mouseout",r),this.dispatchToElement(h,"mousemove",r),f&&f!==s&&this.dispatchToElement(h,"mouseover",r)},t.prototype.mouseout=function(r){var i=r.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",r),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:r})},t.prototype.resize=function(){this._hovered=new Yr(0,0)},t.prototype.dispatch=function(r,i){var n=this[r];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(r){var i=this.proxy;i.setCursor&&i.setCursor(r)},t.prototype.dispatchToElement=function(r,i,n){r=r||{};var a=r.target;if(!(a&&a.silent)){for(var o="on"+i,s=Bs(i,r,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(h){typeof h[o]=="function"&&h[o].call(h,s),h.trigger&&h.trigger(i,s)}))}},t.prototype.findHover=function(r,i,n){var a=this.storage.getDisplayList(),o=new Yr(r,i);if(Dn(a,o,r,i,n),this._pointerSize&&!o.target){for(var s=[],h=this._pointerSize,f=h/2,u=new N(r-f,i-f,h,h),l=a.length-1;l>=0;l--){var v=a[l];v!==n&&!v.ignore&&!v.ignoreCoarsePointer&&(!v.parent||!v.parent.ignoreCoarsePointer)&&(si.copy(v.getBoundingRect()),v.transform&&si.applyTransform(v.transform),si.intersect(u)&&s.push(v))}if(s.length)for(var c=4,_=Math.PI/12,y=Math.PI*2,d=0;d<f;d+=c)for(var p=0;p<y;p+=_){var g=r+d*Math.cos(p),m=i+d*Math.sin(p);if(Dn(s,o,g,m,n),o.target)return o}}return o},t.prototype.processGesture=function(r,i){this._gestureMgr||(this._gestureMgr=new Is);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(r,this.findHover(r.zrX,r.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;r.gestureEvent=o;var s=new Yr;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Br);j(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(e){Ka.prototype[e]=function(t){var r=t.zrX,i=t.zrY,n=Ja(this,r,i),a,o;if((e!=="mouseup"||!n)&&(a=this.findHover(r,i),o=a.target),e==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(e==="mouseup")this._upEl=o;else if(e==="click"){if(this._downEl!==this._upEl||!this._downPoint||Ga(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,e,t)}});function Ns(e,t,r){if(e[e.rectHover?"rectContain":"contain"](t,r)){for(var i=e,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,r))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Qa:!0}return!1}function Dn(e,t,r,i,n){for(var a=e.length-1;a>=0;a--){var o=e[a],s=void 0;if(o!==n&&!o.ignore&&(s=Ns(o,r,i))&&(!t.topTarget&&(t.topTarget=o),s!==Qa)){t.target=o;break}}}function Ja(e,t,r){var i=e.painter;return t<0||t>i.getWidth()||r<0||r>i.getHeight()}var ja=32,Gr=7;function Ws(e){for(var t=0;e>=ja;)t|=e&1,e>>=1;return e+t}function An(e,t,r,i){var n=t+1;if(n===r)return 1;if(i(e[n++],e[t])<0){for(;n<r&&i(e[n],e[n-1])<0;)n++;Ys(e,t,n)}else for(;n<r&&i(e[n],e[n-1])>=0;)n++;return n-t}function Ys(e,t,r){for(r--;t<r;){var i=e[t];e[t++]=e[r],e[r--]=i}}function En(e,t,r,i,n){for(i===t&&i++;i<r;i++){for(var a=e[i],o=t,s=i,h;o<s;)h=o+s>>>1,n(a,e[h])<0?s=h:o=h+1;var f=i-o;switch(f){case 3:e[o+3]=e[o+2];case 2:e[o+2]=e[o+1];case 1:e[o+1]=e[o];break;default:for(;f>0;)e[o+f]=e[o+f-1],f--}e[o]=a}}function hi(e,t,r,i,n,a){var o=0,s=0,h=1;if(a(e,t[r+n])>0){for(s=i-n;h<s&&a(e,t[r+n+h])>0;)o=h,h=(h<<1)+1,h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}else{for(s=n+1;h<s&&a(e,t[r+n-h])<=0;)o=h,h=(h<<1)+1,h<=0&&(h=s);h>s&&(h=s);var f=o;o=n-h,h=n-f}for(o++;o<h;){var u=o+(h-o>>>1);a(e,t[r+u])>0?o=u+1:h=u}return h}function fi(e,t,r,i,n,a){var o=0,s=0,h=1;if(a(e,t[r+n])<0){for(s=n+1;h<s&&a(e,t[r+n-h])<0;)o=h,h=(h<<1)+1,h<=0&&(h=s);h>s&&(h=s);var f=o;o=n-h,h=n-f}else{for(s=i-n;h<s&&a(e,t[r+n+h])>=0;)o=h,h=(h<<1)+1,h<=0&&(h=s);h>s&&(h=s),o+=n,h+=n}for(o++;o<h;){var u=o+(h-o>>>1);a(e,t[r+u])<0?h=u:o=u+1}return h}function Gs(e,t){var r=Gr,i,n,a=0,o=[];i=[],n=[];function s(c,_){i[a]=c,n[a]=_,a+=1}function h(){for(;a>1;){var c=a-2;if(c>=1&&n[c-1]<=n[c]+n[c+1]||c>=2&&n[c-2]<=n[c]+n[c-1])n[c-1]<n[c+1]&&c--;else if(n[c]>n[c+1])break;u(c)}}function f(){for(;a>1;){var c=a-2;c>0&&n[c-1]<n[c+1]&&c--,u(c)}}function u(c){var _=i[c],y=n[c],d=i[c+1],p=n[c+1];n[c]=y+p,c===a-3&&(i[c+1]=i[c+2],n[c+1]=n[c+2]),a--;var g=fi(e[d],e,_,y,0,t);_+=g,y-=g,y!==0&&(p=hi(e[_+y-1],e,d,p,p-1,t),p!==0&&(y<=p?l(_,y,d,p):v(_,y,d,p)))}function l(c,_,y,d){var p=0;for(p=0;p<_;p++)o[p]=e[c+p];var g=0,m=y,w=c;if(e[w++]=e[m++],--d===0){for(p=0;p<_;p++)e[w+p]=o[g+p];return}if(_===1){for(p=0;p<d;p++)e[w+p]=e[m+p];e[w+d]=o[g];return}for(var S=r,T,b,C;;){T=0,b=0,C=!1;do if(t(e[m],o[g])<0){if(e[w++]=e[m++],b++,T=0,--d===0){C=!0;break}}else if(e[w++]=o[g++],T++,b=0,--_===1){C=!0;break}while((T|b)<S);if(C)break;do{if(T=fi(e[m],o,g,_,0,t),T!==0){for(p=0;p<T;p++)e[w+p]=o[g+p];if(w+=T,g+=T,_-=T,_<=1){C=!0;break}}if(e[w++]=e[m++],--d===0){C=!0;break}if(b=hi(o[g],e,m,d,0,t),b!==0){for(p=0;p<b;p++)e[w+p]=e[m+p];if(w+=b,m+=b,d-=b,d===0){C=!0;break}}if(e[w++]=o[g++],--_===1){C=!0;break}S--}while(T>=Gr||b>=Gr);if(C)break;S<0&&(S=0),S+=2}if(r=S,r<1&&(r=1),_===1){for(p=0;p<d;p++)e[w+p]=e[m+p];e[w+d]=o[g]}else{if(_===0)throw new Error;for(p=0;p<_;p++)e[w+p]=o[g+p]}}function v(c,_,y,d){var p=0;for(p=0;p<d;p++)o[p]=e[y+p];var g=c+_-1,m=d-1,w=y+d-1,S=0,T=0;if(e[w--]=e[g--],--_===0){for(S=w-(d-1),p=0;p<d;p++)e[S+p]=o[p];return}if(d===1){for(w-=_,g-=_,T=w+1,S=g+1,p=_-1;p>=0;p--)e[T+p]=e[S+p];e[w]=o[m];return}for(var b=r;;){var C=0,L=0,M=!1;do if(t(o[m],e[g])<0){if(e[w--]=e[g--],C++,L=0,--_===0){M=!0;break}}else if(e[w--]=o[m--],L++,C=0,--d===1){M=!0;break}while((C|L)<b);if(M)break;do{if(C=_-fi(o[m],e,c,_,_-1,t),C!==0){for(w-=C,g-=C,_-=C,T=w+1,S=g+1,p=C-1;p>=0;p--)e[T+p]=e[S+p];if(_===0){M=!0;break}}if(e[w--]=o[m--],--d===1){M=!0;break}if(L=d-hi(e[g],o,0,d,d-1,t),L!==0){for(w-=L,m-=L,d-=L,T=w+1,S=m+1,p=0;p<L;p++)e[T+p]=o[S+p];if(d<=1){M=!0;break}}if(e[w--]=e[g--],--_===0){M=!0;break}b--}while(C>=Gr||L>=Gr);if(M)break;b<0&&(b=0),b+=2}if(r=b,r<1&&(r=1),d===1){for(w-=_,g-=_,T=w+1,S=g+1,p=_-1;p>=0;p--)e[T+p]=e[S+p];e[w]=o[m]}else{if(d===0)throw new Error;for(S=w-(d-1),p=0;p<d;p++)e[S+p]=o[p]}}return{mergeRuns:h,forceMergeRuns:f,pushRun:s}}function Xs(e,t,r,i){r||(r=0),i||(i=e.length);var n=i-r;if(!(n<2)){var a=0;if(n<ja){a=An(e,r,i,t),En(e,r,i,r+a,t);return}var o=Gs(e,t),s=Ws(n);do{if(a=An(e,r,i,t),a<s){var h=n;h>s&&(h=s),En(e,r,r+h,r+a,t),a=h}o.pushRun(r,a),o.mergeRuns(),n-=a,r+=a}while(n!==0);o.forceMergeRuns()}}var Lt=1,He=2,Ur=4,In=!1;function ui(){In||(In=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function On(e,t){return e.zlevel===t.zlevel?e.z===t.z?e.z2-t.z2:e.z-t.z:e.zlevel-t.zlevel}var qs=function(){function e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=On}return e.prototype.traverse=function(t,r){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,r)},e.prototype.getDisplayList=function(t,r){r=r||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(r),i},e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var r=this._roots,i=this._displayList,n=0,a=r.length;n<a;n++)this._updateAndAddDisplayable(r[n],null,t);i.length=this._displayListLen,Xs(i,On)},e.prototype._updateAndAddDisplayable=function(t,r,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)r=null;else if(n){r?r=r.slice():r=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),r.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),h=0;h<s.length;h++){var f=s[h];t.__dirty&&(f.__dirty|=Lt),this._updateAndAddDisplayable(f,r,i)}t.__dirty=0}else{var u=t;r&&r.length?u.__clipPaths=r:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(ui(),u.z=0),isNaN(u.z2)&&(ui(),u.z2=0),isNaN(u.zlevel)&&(ui(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var l=t.getDecalElement&&t.getDecalElement();l&&this._updateAndAddDisplayable(l,r,i);var v=t.getTextGuideLine();v&&this._updateAndAddDisplayable(v,r,i);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,r,i)}},e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},e.prototype.delRoot=function(t){if(t instanceof Array){for(var r=0,i=t.length;r<i;r++)this.delRoot(t[r]);return}var n=ct(this._roots,t);n>=0&&this._roots.splice(n,1)},e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},e.prototype.getRoots=function(){return this._roots},e.prototype.dispose=function(){this._displayList=null,this._roots=null},e}(),qe;qe=k.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(e){return setTimeout(e,16)};var te={linear:function(e){return e},quadraticIn:function(e){return e*e},quadraticOut:function(e){return e*(2-e)},quadraticInOut:function(e){return(e*=2)<1?.5*e*e:-.5*(--e*(e-2)-1)},cubicIn:function(e){return e*e*e},cubicOut:function(e){return--e*e*e+1},cubicInOut:function(e){return(e*=2)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},quarticIn:function(e){return e*e*e*e},quarticOut:function(e){return 1- --e*e*e*e},quarticInOut:function(e){return(e*=2)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},quinticIn:function(e){return e*e*e*e*e},quinticOut:function(e){return--e*e*e*e*e+1},quinticInOut:function(e){return(e*=2)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},sinusoidalIn:function(e){return 1-Math.cos(e*Math.PI/2)},sinusoidalOut:function(e){return Math.sin(e*Math.PI/2)},sinusoidalInOut:function(e){return .5*(1-Math.cos(Math.PI*e))},exponentialIn:function(e){return e===0?0:Math.pow(1024,e-1)},exponentialOut:function(e){return e===1?1:1-Math.pow(2,-10*e)},exponentialInOut:function(e){return e===0?0:e===1?1:(e*=2)<1?.5*Math.pow(1024,e-1):.5*(-Math.pow(2,-10*(e-1))+2)},circularIn:function(e){return 1-Math.sqrt(1-e*e)},circularOut:function(e){return Math.sqrt(1- --e*e)},circularInOut:function(e){return(e*=2)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},elasticIn:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)))},elasticOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/i)+1)},elasticInOut:function(e){var t,r=.1,i=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=i/4):t=i*Math.asin(1/r)/(2*Math.PI),(e*=2)<1?-.5*(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)):r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/i)*.5+1)},backIn:function(e){var t=1.70158;return e*e*((t+1)*e-t)},backOut:function(e){var t=1.70158;return--e*e*((t+1)*e+t)+1},backInOut:function(e){var t=2.5949095;return(e*=2)<1?.5*(e*e*((t+1)*e-t)):.5*((e-=2)*e*((t+1)*e+t)+2)},bounceIn:function(e){return 1-te.bounceOut(1-e)},bounceOut:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},bounceInOut:function(e){return e<.5?te.bounceIn(e*2)*.5:te.bounceOut(e*2-1)*.5+.5}},ge=Math.pow,Bt=Math.sqrt,to=1e-8,ro=1e-4,Fn=Bt(3),ye=1/3,St=pr(),at=pr(),Er=pr();function Ot(e){return e>-1e-8&&e<to}function eo(e){return e>to||e<-1e-8}function G(e,t,r,i,n){var a=1-n;return a*a*(a*e+3*n*t)+n*n*(n*i+3*a*r)}function Bn(e,t,r,i,n){var a=1-n;return 3*(((t-e)*a+2*(r-t)*n)*a+(i-r)*n*n)}function io(e,t,r,i,n,a){var o=i+3*(t-r)-e,s=3*(r-t*2+e),h=3*(t-e),f=e-n,u=s*s-3*o*h,l=s*h-9*o*f,v=h*h-3*s*f,c=0;if(Ot(u)&&Ot(l))if(Ot(s))a[0]=0;else{var _=-h/s;_>=0&&_<=1&&(a[c++]=_)}else{var y=l*l-4*u*v;if(Ot(y)){var d=l/u,_=-s/o+d,p=-d/2;_>=0&&_<=1&&(a[c++]=_),p>=0&&p<=1&&(a[c++]=p)}else if(y>0){var g=Bt(y),m=u*s+1.5*o*(-l+g),w=u*s+1.5*o*(-l-g);m<0?m=-ge(-m,ye):m=ge(m,ye),w<0?w=-ge(-w,ye):w=ge(w,ye);var _=(-s-(m+w))/(3*o);_>=0&&_<=1&&(a[c++]=_)}else{var S=(2*u*s-3*o*l)/(2*Bt(u*u*u)),T=Math.acos(S)/3,b=Bt(u),C=Math.cos(T),_=(-s-2*b*C)/(3*o),p=(-s+b*(C+Fn*Math.sin(T)))/(3*o),L=(-s+b*(C-Fn*Math.sin(T)))/(3*o);_>=0&&_<=1&&(a[c++]=_),p>=0&&p<=1&&(a[c++]=p),L>=0&&L<=1&&(a[c++]=L)}}return c}function no(e,t,r,i,n){var a=6*r-12*t+6*e,o=9*t+3*i-3*e-9*r,s=3*t-3*e,h=0;if(Ot(o)){if(eo(a)){var f=-s/a;f>=0&&f<=1&&(n[h++]=f)}}else{var u=a*a-4*o*s;if(Ot(u))n[0]=-a/(2*o);else if(u>0){var l=Bt(u),f=(-a+l)/(2*o),v=(-a-l)/(2*o);f>=0&&f<=1&&(n[h++]=f),v>=0&&v<=1&&(n[h++]=v)}}return h}function $e(e,t,r,i,n,a){var o=(t-e)*n+e,s=(r-t)*n+t,h=(i-r)*n+r,f=(s-o)*n+o,u=(h-s)*n+s,l=(u-f)*n+f;a[0]=e,a[1]=o,a[2]=f,a[3]=l,a[4]=l,a[5]=u,a[6]=h,a[7]=i}function $s(e,t,r,i,n,a,o,s,h,f,u){var l,v=.005,c=1/0,_,y,d,p;St[0]=h,St[1]=f;for(var g=0;g<1;g+=.05)at[0]=G(e,r,n,o,g),at[1]=G(t,i,a,s,g),d=sr(St,at),d<c&&(l=g,c=d);c=1/0;for(var m=0;m<32&&!(v<ro);m++)_=l-v,y=l+v,at[0]=G(e,r,n,o,_),at[1]=G(t,i,a,s,_),d=sr(at,St),_>=0&&d<c?(l=_,c=d):(Er[0]=G(e,r,n,o,y),Er[1]=G(t,i,a,s,y),p=sr(Er,St),y<=1&&p<c?(l=y,c=p):v*=.5);return u&&(u[0]=G(e,r,n,o,l),u[1]=G(t,i,a,s,l)),Bt(c)}function Zs(e,t,r,i,n,a,o,s,h){for(var f=e,u=t,l=0,v=1/h,c=1;c<=h;c++){var _=c*v,y=G(e,r,n,o,_),d=G(t,i,a,s,_),p=y-f,g=d-u;l+=Math.sqrt(p*p+g*g),f=y,u=d}return l}function q(e,t,r,i){var n=1-i;return n*(n*e+2*i*t)+i*i*r}function Hn(e,t,r,i){return 2*((1-i)*(t-e)+i*(r-t))}function Us(e,t,r,i,n){var a=e-2*t+r,o=2*(t-e),s=e-i,h=0;if(Ot(a)){if(eo(o)){var f=-s/o;f>=0&&f<=1&&(n[h++]=f)}}else{var u=o*o-4*a*s;if(Ot(u)){var f=-o/(2*a);f>=0&&f<=1&&(n[h++]=f)}else if(u>0){var l=Bt(u),f=(-o+l)/(2*a),v=(-o-l)/(2*a);f>=0&&f<=1&&(n[h++]=f),v>=0&&v<=1&&(n[h++]=v)}}return h}function ao(e,t,r){var i=e+r-2*t;return i===0?.5:(e-t)/i}function Ze(e,t,r,i,n){var a=(t-e)*i+e,o=(r-t)*i+t,s=(o-a)*i+a;n[0]=e,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=r}function Vs(e,t,r,i,n,a,o,s,h){var f,u=.005,l=1/0;St[0]=o,St[1]=s;for(var v=0;v<1;v+=.05){at[0]=q(e,r,n,v),at[1]=q(t,i,a,v);var c=sr(St,at);c<l&&(f=v,l=c)}l=1/0;for(var _=0;_<32&&!(u<ro);_++){var y=f-u,d=f+u;at[0]=q(e,r,n,y),at[1]=q(t,i,a,y);var c=sr(at,St);if(y>=0&&c<l)f=y,l=c;else{Er[0]=q(e,r,n,d),Er[1]=q(t,i,a,d);var p=sr(Er,St);d<=1&&p<l?(f=d,l=p):u*=.5}}return h&&(h[0]=q(e,r,n,f),h[1]=q(t,i,a,f)),Bt(l)}function Qs(e,t,r,i,n,a,o){for(var s=e,h=t,f=0,u=1/o,l=1;l<=o;l++){var v=l*u,c=q(e,r,n,v),_=q(t,i,a,v),y=c-s,d=_-h;f+=Math.sqrt(y*y+d*d),s=c,h=_}return f}var Ks=/cubic-bezier\(([0-9,\.e ]+)\)/;function oo(e){var t=e&&Ks.exec(e);if(t){var r=t[1].split(","),i=+Pr(r[0]),n=+Pr(r[1]),a=+Pr(r[2]),o=+Pr(r[3]);if(isNaN(i+n+a+o))return;var s=[];return function(h){return h<=0?0:h>=1?1:io(0,i,a,1,h,s)&&G(0,n,o,1,s[0])}}}var Js=function(){function e(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||or,this.ondestroy=t.ondestroy||or,this.onrestart=t.onrestart||or,t.easing&&this.setEasing(t.easing)}return e.prototype.step=function(t,r){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=r;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var h=n%i;this._startTime=t-h,this._pausedTime=0,this.onrestart()}else return!0;return!1},e.prototype.pause=function(){this._paused=!0},e.prototype.resume=function(){this._paused=!1},e.prototype.setEasing=function(t){this.easing=t,this.easingFunc=fe(t)?t:te[t]||oo(t)},e}(),so=function(){function e(t){this.value=t}return e}(),js=function(){function e(){this._len=0}return e.prototype.insert=function(t){var r=new so(t);return this.insertEntry(r),r},e.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},e.prototype.remove=function(t){var r=t.prev,i=t.next;r?r.next=i:this.head=i,i?i.prev=r:this.tail=r,t.next=t.prev=null,this._len--},e.prototype.len=function(){return this._len},e.prototype.clear=function(){this.head=this.tail=null,this._len=0},e}(),ti=function(){function e(t){this._list=new js,this._maxSize=10,this._map={},this._maxSize=t}return e.prototype.put=function(t,r){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var h=i.head;i.remove(h),delete n[h.key],a=h.value,this._lastRemovedEntry=h}s?s.value=r:s=new so(r),s.key=t,i.insertEntry(s),n[t]=s}return a},e.prototype.get=function(t){var r=this._map[t],i=this._list;if(r!=null)return r!==i.tail&&(i.remove(r),i.insertEntry(r)),r.value},e.prototype.clear=function(){this._list.clear(),this._map={}},e.prototype.len=function(){return this._list.len()},e}(),zn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function dt(e){return e=Math.round(e),e<0?0:e>255?255:e}function th(e){return e=Math.round(e),e<0?0:e>360?360:e}function ie(e){return e<0?0:e>1?1:e}function li(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?dt(parseFloat(t)/100*255):dt(parseInt(t,10))}function fr(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?ie(parseFloat(t)/100):ie(parseFloat(t))}function vi(e,t,r){return r<0?r+=1:r>1&&(r-=1),r*6<1?e+(t-e)*r*6:r*2<1?t:r*3<2?e+(t-e)*(2/3-r)*6:e}function Ft(e,t,r){return e+(t-e)*r}function it(e,t,r,i,n){return e[0]=t,e[1]=r,e[2]=i,e[3]=n,e}function $i(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}var ho=new ti(20),me=null;function mr(e,t){me&&$i(me,t),me=ho.put(e,me||t.slice())}function ht(e,t){if(e){t=t||[];var r=ho.get(e);if(r)return $i(t,r);e=e+"";var i=e.replace(/ /g,"").toLowerCase();if(i in zn)return $i(t,zn[i]),mr(e,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){it(t,0,0,0,1);return}return it(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),mr(e,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){it(t,0,0,0,1);return}return it(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),mr(e,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var h=i.substr(0,o),f=i.substr(o+1,s-(o+1)).split(","),u=1;switch(h){case"rgba":if(f.length!==4)return f.length===3?it(t,+f[0],+f[1],+f[2],1):it(t,0,0,0,1);u=fr(f.pop());case"rgb":if(f.length>=3)return it(t,li(f[0]),li(f[1]),li(f[2]),f.length===3?u:fr(f[3])),mr(e,t),t;it(t,0,0,0,1);return;case"hsla":if(f.length!==4){it(t,0,0,0,1);return}return f[3]=fr(f[3]),Zi(f,t),mr(e,t),t;case"hsl":if(f.length!==3){it(t,0,0,0,1);return}return Zi(f,t),mr(e,t),t;default:return}}it(t,0,0,0,1)}}function Zi(e,t){var r=(parseFloat(e[0])%360+360)%360/360,i=fr(e[1]),n=fr(e[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],it(t,dt(vi(o,a,r+1/3)*255),dt(vi(o,a,r)*255),dt(vi(o,a,r-1/3)*255),1),e.length===4&&(t[3]=e[3]),t}function rh(e){if(e){var t=e[0]/255,r=e[1]/255,i=e[2]/255,n=Math.min(t,r,i),a=Math.max(t,r,i),o=a-n,s=(a+n)/2,h,f;if(o===0)h=0,f=0;else{s<.5?f=o/(a+n):f=o/(2-a-n);var u=((a-t)/6+o/2)/o,l=((a-r)/6+o/2)/o,v=((a-i)/6+o/2)/o;t===a?h=v-l:r===a?h=1/3+u-v:i===a&&(h=2/3+l-u),h<0&&(h+=1),h>1&&(h-=1)}var c=[h*360,f,s];return e[3]!=null&&c.push(e[3]),c}}function Ui(e,t){var r=ht(e);if(r){for(var i=0;i<3;i++)t<0?r[i]=r[i]*(1-t)|0:r[i]=(255-r[i])*t+r[i]|0,r[i]>255?r[i]=255:r[i]<0&&(r[i]=0);return _r(r,r.length===4?"rgba":"rgb")}}function eh(e){var t=ht(e);if(t)return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}function fo(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){r=r||[];var i=e*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=t[n],s=t[a],h=i-n;return r[0]=dt(Ft(o[0],s[0],h)),r[1]=dt(Ft(o[1],s[1],h)),r[2]=dt(Ft(o[2],s[2],h)),r[3]=ie(Ft(o[3],s[3],h)),r}}var ih=fo;function uo(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){var i=e*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=ht(t[n]),s=ht(t[a]),h=i-n,f=_r([dt(Ft(o[0],s[0],h)),dt(Ft(o[1],s[1],h)),dt(Ft(o[2],s[2],h)),ie(Ft(o[3],s[3],h))],"rgba");return r?{color:f,leftIndex:n,rightIndex:a,value:i}:f}}var nh=uo;function ah(e,t,r,i){var n=ht(e);if(e)return n=rh(n),t!=null&&(n[0]=th(t)),r!=null&&(n[1]=fr(r)),i!=null&&(n[2]=fr(i)),_r(Zi(n),"rgba")}function oh(e,t){var r=ht(e);if(r&&t!=null)return r[3]=ie(t),_r(r,"rgba")}function _r(e,t){if(!(!e||!e.length)){var r=e[0]+","+e[1]+","+e[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(r+=","+e[3]),t+"("+r+")"}}function ne(e,t){var r=ht(e);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*t:0}function sh(){return _r([Math.round(Math.random()*255),Math.round(Math.random()*255),Math.round(Math.random()*255)],"rgb")}var kn=new ti(100);function hh(e){if(ee(e)){var t=kn.get(e);return t||(t=Ui(e,-.1),kn.put(e,t)),t}else if(ue(e)){var r=H({},e);return r.colorStops=dr(e.colorStops,function(i){return{offset:i.offset,color:Ui(i.color,-.1)}}),r}return e}const Nu=Object.freeze(Object.defineProperty({__proto__:null,fastLerp:fo,fastMapToColor:ih,lerp:uo,lift:Ui,liftColor:hh,lum:ne,mapToColor:nh,modifyAlpha:oh,modifyHSL:ah,parse:ht,random:sh,stringify:_r,toHex:eh},Symbol.toStringTag,{value:"Module"}));function fh(e){return e.type==="linear"}function uh(e){return e.type==="radial"}(function(){return k.hasGlobalWindow&&fe(window.btoa)?function(e){return window.btoa(unescape(encodeURIComponent(e)))}:typeof Buffer<"u"?function(e){return Buffer.from(e).toString("base64")}:function(e){return null}})();var Vi=Array.prototype.slice;function Rt(e,t,r){return(t-e)*r+e}function ci(e,t,r,i){for(var n=t.length,a=0;a<n;a++)e[a]=Rt(t[a],r[a],i);return e}function lh(e,t,r,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){e[o]||(e[o]=[]);for(var s=0;s<a;s++)e[o][s]=Rt(t[o][s],r[o][s],i)}return e}function we(e,t,r,i){for(var n=t.length,a=0;a<n;a++)e[a]=t[a]+r[a]*i;return e}function Nn(e,t,r,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){e[o]||(e[o]=[]);for(var s=0;s<a;s++)e[o][s]=t[o][s]+r[o][s]*i}return e}function vh(e,t){for(var r=e.length,i=t.length,n=r>i?t:e,a=Math.min(r,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(r,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function ch(e,t,r){var i=e,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var h=a;h<o;h++)i.push(r===1?n[h]:Vi.call(n[h]))}for(var f=i[0]&&i[0].length,h=0;h<i.length;h++)if(r===1)isNaN(i[h])&&(i[h]=n[h]);else for(var u=0;u<f;u++)isNaN(i[h][u])&&(i[h][u]=n[h][u])}}function ze(e){if(pt(e)){var t=e.length;if(pt(e[0])){for(var r=[],i=0;i<t;i++)r.push(Vi.call(e[i]));return r}return Vi.call(e)}return e}function ke(e){return e[0]=Math.floor(e[0])||0,e[1]=Math.floor(e[1])||0,e[2]=Math.floor(e[2])||0,e[3]=e[3]==null?1:e[3],"rgba("+e.join(",")+")"}function dh(e){return pt(e&&e[0])?2:1}var Te=0,Ne=1,lo=2,Vr=3,Qi=4,Ki=5,Wn=6;function Yn(e){return e===Qi||e===Ki}function be(e){return e===Ne||e===lo}var Xr=[0,0,0,0],ph=function(){function e(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return e.prototype.isFinished=function(){return this._finished},e.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},e.prototype.needsAnimate=function(){return this.keyframes.length>=1},e.prototype.getAdditiveTrack=function(){return this._additiveTrack},e.prototype.addKeyframe=function(t,r,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=Wn,h=r;if(pt(r)){var f=dh(r);s=f,(f===1&&!Jr(r[0])||f===2&&!Jr(r[0][0]))&&(o=!0)}else if(Jr(r)&&!Oa(r))s=Te;else if(ee(r))if(!isNaN(+r))s=Te;else{var u=ht(r);u&&(h=u,s=Vr)}else if(ue(r)){var l=H({},h);l.colorStops=dr(r.colorStops,function(c){return{offset:c.offset,color:ht(c.color)}}),fh(r)?s=Qi:uh(r)&&(s=Ki),h=l}a===0?this.valType=s:(s!==this.valType||s===Wn)&&(o=!0),this.discrete=this.discrete||o;var v={time:t,value:h,rawValue:r,percent:0};return i&&(v.easing=i,v.easingFunc=fe(i)?i:te[i]||oo(i)),n.push(v),v},e.prototype.prepare=function(t,r){var i=this.keyframes;this._needsSort&&i.sort(function(y,d){return y.time-d.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,h=be(n),f=Yn(n),u=0;u<a;u++){var l=i[u],v=l.value,c=o.value;l.percent=l.time/t,s||(h&&u!==a-1?ch(v,c,n):f&&vh(v.colorStops,c.colorStops))}if(!s&&n!==Ki&&r&&this.needsAnimate()&&r.needsAnimate()&&n===r.valType&&!r._finished){this._additiveTrack=r;for(var _=i[0].value,u=0;u<a;u++)n===Te?i[u].additiveValue=i[u].value-_:n===Vr?i[u].additiveValue=we([],i[u].value,_,-1):be(n)&&(i[u].additiveValue=n===Ne?we([],i[u].value,_,-1):Nn([],i[u].value,_,-1))}},e.prototype.step=function(t,r){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,h=this.propName,f=a===Vr,u,l=this._lastFr,v=Math.min,c,_;if(s===1)c=_=o[0];else{if(r<0)u=0;else if(r<this._lastFrP){var y=v(l+1,s-1);for(u=y;u>=0&&!(o[u].percent<=r);u--);u=v(u,s-2)}else{for(u=l;u<s&&!(o[u].percent>r);u++);u=v(u-1,s-2)}_=o[u+1],c=o[u]}if(c&&_){this._lastFr=u,this._lastFrP=r;var d=_.percent-c.percent,p=d===0?1:v((r-c.percent)/d,1);_.easingFunc&&(p=_.easingFunc(p));var g=i?this._additiveValue:f?Xr:t[h];if((be(a)||f)&&!g&&(g=this._additiveValue=[]),this.discrete)t[h]=p<1?c.rawValue:_.rawValue;else if(be(a))a===Ne?ci(g,c[n],_[n],p):lh(g,c[n],_[n],p);else if(Yn(a)){var m=c[n],w=_[n],S=a===Qi;t[h]={type:S?"linear":"radial",x:Rt(m.x,w.x,p),y:Rt(m.y,w.y,p),colorStops:dr(m.colorStops,function(b,C){var L=w.colorStops[C];return{offset:Rt(b.offset,L.offset,p),color:ke(ci([],b.color,L.color,p))}}),global:w.global},S?(t[h].x2=Rt(m.x2,w.x2,p),t[h].y2=Rt(m.y2,w.y2,p)):t[h].r=Rt(m.r,w.r,p)}else if(f)ci(g,c[n],_[n],p),i||(t[h]=ke(g));else{var T=Rt(c[n],_[n],p);i?this._additiveValue=T:t[h]=T}i&&this._addToTarget(t)}}},e.prototype._addToTarget=function(t){var r=this.valType,i=this.propName,n=this._additiveValue;r===Te?t[i]=t[i]+n:r===Vr?(ht(t[i],Xr),we(Xr,Xr,n,1),t[i]=ke(Xr)):r===Ne?we(t[i],t[i],n,1):r===lo&&Nn(t[i],t[i],n,1)},e}(),bn=function(){function e(t,r,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=r,r&&n){je("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return e.prototype.getMaxTime=function(){return this._maxTime},e.prototype.getDelay=function(){return this._delay},e.prototype.getLoop=function(){return this._loop},e.prototype.getTarget=function(){return this._target},e.prototype.changeTarget=function(t){this._target=t},e.prototype.when=function(t,r,i){return this.whenWithKeys(t,r,tt(r),i)},e.prototype.whenWithKeys=function(t,r,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],h=a[s];if(!h){h=a[s]=new ph(s);var f=void 0,u=this._getAdditiveTrack(s);if(u){var l=u.keyframes,v=l[l.length-1];f=v&&v.value,u.valType===Vr&&f&&(f=ke(f))}else f=this._target[s];if(f==null)continue;t>0&&h.addKeyframe(0,ze(f),n),this._trackKeys.push(s)}h.addKeyframe(t,ze(r[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},e.prototype.pause=function(){this._clip.pause(),this._paused=!0},e.prototype.resume=function(){this._clip.resume(),this._paused=!1},e.prototype.isPaused=function(){return!!this._paused},e.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},e.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var r=t.length,i=0;i<r;i++)t[i].call(this)},e.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,r=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,r)for(var i=0;i<r.length;i++)r[i].call(this)},e.prototype._setTracksFinished=function(){for(var t=this._tracks,r=this._trackKeys,i=0;i<r.length;i++)t[r[i]].setFinished()},e.prototype._getAdditiveTrack=function(t){var r,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(r=a)}return r},e.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var r=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],h=this._getAdditiveTrack(o),f=s.keyframes,u=f.length;if(s.prepare(n,h),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var l=f[u-1];l&&(r._target[s.propName]=l.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var v=new Js({life:n,loop:this._loop,delay:this._delay||0,onframe:function(c){r._started=2;var _=r._additiveAnimators;if(_){for(var y=!1,d=0;d<_.length;d++)if(_[d]._clip){y=!0;break}y||(r._additiveAnimators=null)}for(var d=0;d<i.length;d++)i[d].step(r._target,c);var p=r._onframeCbs;if(p)for(var d=0;d<p.length;d++)p[d](r._target,c)},ondestroy:function(){r._doneCallback()}});this._clip=v,this.animation&&this.animation.addClip(v),t&&v.setEasing(t)}else this._doneCallback();return this}},e.prototype.stop=function(t){if(this._clip){var r=this._clip;t&&r.onframe(1),this._abortedCallback()}},e.prototype.delay=function(t){return this._delay=t,this},e.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},e.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},e.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},e.prototype.getClip=function(){return this._clip},e.prototype.getTrack=function(t){return this._tracks[t]},e.prototype.getTracks=function(){var t=this;return dr(this._trackKeys,function(r){return t._tracks[r]})},e.prototype.stopTracks=function(t,r){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(r?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},e.prototype.saveTo=function(t,r,i){if(t){r=r||this._trackKeys;for(var n=0;n<r.length;n++){var a=r[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,h=s[i?0:s.length-1];h&&(t[a]=ze(h.rawValue))}}}},e.prototype.__changeFinalValue=function(t,r){r=r||tt(t);for(var i=0;i<r.length;i++){var n=r[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},e}();function Mr(){return new Date().getTime()}var _h=function(e){F(t,e);function t(r){var i=e.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,r=r||{},i.stage=r.stage||{},i}return t.prototype.addClip=function(r){r.animation&&this.removeClip(r),this._head?(this._tail.next=r,r.prev=this._tail,r.next=null,this._tail=r):this._head=this._tail=r,r.animation=this},t.prototype.addAnimator=function(r){r.animation=this;var i=r.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(r){if(r.animation){var i=r.prev,n=r.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,r.next=r.prev=r.animation=null}},t.prototype.removeAnimator=function(r){var i=r.getClip();i&&this.removeClip(i),r.animation=null},t.prototype.update=function(r){for(var i=Mr()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,r||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var r=this;this._running=!0;function i(){r._running&&(qe(i),!r._paused&&r.update())}qe(i)},t.prototype.start=function(){this._running||(this._time=Mr(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Mr(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Mr()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var r=this._head;r;){var i=r.next;r.prev=r.next=r.animation=null,r=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(r,i){i=i||{},this.start();var n=new bn(r,i.loop);return this.addAnimator(n),n},t}(Br),gh=300,di=k.domSupported,pi=function(){var e=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=dr(e,function(n){var a=n.replace("mouse","pointer");return r.hasOwnProperty(a)?a:n});return{mouse:e,touch:t,pointer:i}}(),Gn={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Xn=!1;function Ji(e){var t=e.pointerType;return t==="pen"||t==="touch"}function yh(e){e.touching=!0,e.touchTimer!=null&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)}function _i(e){e&&(e.zrByTouch=!0)}function mh(e,t){return lt(e.dom,new wh(e,t),!0)}function vo(e,t){for(var r=t,i=!1;r&&r.nodeType!==9&&!(i=r.domBelongToZr||r!==t&&r===e.painterRoot);)r=r.parentNode;return i}var wh=function(){function e(t,r){this.stopPropagation=or,this.stopImmediatePropagation=or,this.preventDefault=or,this.type=r.type,this.target=this.currentTarget=t.dom,this.pointerType=r.pointerType,this.clientX=r.clientX,this.clientY=r.clientY}return e}(),vt={mousedown:function(e){e=lt(this.dom,e),this.__mayPointerCapture=[e.zrX,e.zrY],this.trigger("mousedown",e)},mousemove:function(e){e=lt(this.dom,e);var t=this.__mayPointerCapture;t&&(e.zrX!==t[0]||e.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",e)},mouseup:function(e){e=lt(this.dom,e),this.__togglePointerCapture(!1),this.trigger("mouseup",e)},mouseout:function(e){e=lt(this.dom,e);var t=e.toElement||e.relatedTarget;vo(this,t)||(this.__pointerCapturing&&(e.zrEventControl="no_globalout"),this.trigger("mouseout",e))},wheel:function(e){Xn=!0,e=lt(this.dom,e),this.trigger("mousewheel",e)},mousewheel:function(e){Xn||(e=lt(this.dom,e),this.trigger("mousewheel",e))},touchstart:function(e){e=lt(this.dom,e),_i(e),this.__lastTouchMoment=new Date,this.handler.processGesture(e,"start"),vt.mousemove.call(this,e),vt.mousedown.call(this,e)},touchmove:function(e){e=lt(this.dom,e),_i(e),this.handler.processGesture(e,"change"),vt.mousemove.call(this,e)},touchend:function(e){e=lt(this.dom,e),_i(e),this.handler.processGesture(e,"end"),vt.mouseup.call(this,e),+new Date-+this.__lastTouchMoment<gh&&vt.click.call(this,e)},pointerdown:function(e){vt.mousedown.call(this,e)},pointermove:function(e){Ji(e)||vt.mousemove.call(this,e)},pointerup:function(e){vt.mouseup.call(this,e)},pointerout:function(e){Ji(e)||vt.mouseout.call(this,e)}};j(["click","dblclick","contextmenu"],function(e){vt[e]=function(t){t=lt(this.dom,t),this.trigger(e,t)}});var ji={pointermove:function(e){Ji(e)||ji.mousemove.call(this,e)},pointerup:function(e){ji.mouseup.call(this,e)},mousemove:function(e){this.trigger("mousemove",e)},mouseup:function(e){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",e),t&&(e.zrEventControl="only_globalout",this.trigger("mouseout",e))}};function Th(e,t){var r=t.domHandlers;k.pointerEventsSupported?j(pi.pointer,function(i){We(t,i,function(n){r[i].call(e,n)})}):(k.touchEventsSupported&&j(pi.touch,function(i){We(t,i,function(n){r[i].call(e,n),yh(t)})}),j(pi.mouse,function(i){We(t,i,function(n){n=wn(n),t.touching||r[i].call(e,n)})}))}function bh(e,t){k.pointerEventsSupported?j(Gn.pointer,r):k.touchEventsSupported||j(Gn.mouse,r);function r(i){function n(a){a=wn(a),vo(e,a.target)||(a=mh(e,a),t.domHandlers[i].call(e,a))}We(t,i,n,{capture:!0})}}function We(e,t,r,i){e.mounted[t]=r,e.listenerOpts[t]=i,Ds(e.domTarget,t,r,i)}function gi(e){var t=e.mounted;for(var r in t)t.hasOwnProperty(r)&&As(e.domTarget,r,t[r],e.listenerOpts[r]);e.mounted={}}var qn=function(){function e(t,r){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=r}return e}(),Ch=function(e){F(t,e);function t(r,i){var n=e.call(this)||this;return n.__pointerCapturing=!1,n.dom=r,n.painterRoot=i,n._localHandlerScope=new qn(r,vt),di&&(n._globalHandlerScope=new qn(document,ji)),Th(n,n._localHandlerScope),n}return t.prototype.dispose=function(){gi(this._localHandlerScope),di&&gi(this._globalHandlerScope)},t.prototype.setCursor=function(r){this.dom.style&&(this.dom.style.cursor=r||"default")},t.prototype.__togglePointerCapture=function(r){if(this.__mayPointerCapture=null,di&&+this.__pointerCapturing^+r){this.__pointerCapturing=r;var i=this._globalHandlerScope;r?bh(this,i):gi(i)}},t}(Br),co=1;k.hasGlobalWindow&&(co=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ue=co,tn=.4,rn="#333",en="#ccc",Sh="#eee",$n=$a,Lh=5e-5;function Yt(e){return e>Lh||e<-5e-5}var Gt=[],wr=[],yi=hr(),mi=Math.abs,po=function(){function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return Yt(this.rotation)||Yt(this.x)||Yt(this.y)||Yt(this.scaleX-1)||Yt(this.scaleY-1)||Yt(this.skewX)||Yt(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,r=this.needLocalTransform(),i=this.transform;if(!(r||t)){i&&($n(i),this.invTransform=null);return}i=i||hr(),r?this.getLocalTransform(i):$n(i),t&&(r?Be(i,t,i):Tn(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},e.prototype._resolveGlobalScaleRatio=function(t){var r=this.globalScaleRatio;if(r!=null&&r!==1){this.getGlobalScale(Gt);var i=Gt[0]<0?-1:1,n=Gt[1]<0?-1:1,a=((Gt[0]-i)*r+i)/Gt[0]||0,o=((Gt[1]-n)*r+n)/Gt[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||hr(),Va(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,r=[];t;)r.push(t),t=t.parent;for(;t=r.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(t){var r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),r=Math.sqrt(r),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,r=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||hr(),Be(wr,t.invTransform,r),r=wr);var i=this.originX,n=this.originY;(i||n)&&(yi[4]=i,yi[5]=n,Be(wr,r,yi),wr[4]-=i,wr[5]-=n,r=wr),this.setLocalTransform(r)}},e.prototype.getGlobalScale=function(t){var r=this.transform;return t=t||[],r?(t[0]=Math.sqrt(r[0]*r[0]+r[1]*r[1]),t[1]=Math.sqrt(r[2]*r[2]+r[3]*r[3]),r[0]<0&&(t[0]=-t[0]),r[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},e.prototype.transformCoordToLocal=function(t,r){var i=[t,r],n=this.invTransform;return n&&Ar(i,i,n),i},e.prototype.transformCoordToGlobal=function(t,r){var i=[t,r],n=this.transform;return n&&Ar(i,i,n),i},e.prototype.getLineScale=function(){var t=this.transform;return t&&mi(t[0]-1)>1e-10&&mi(t[3]-1)>1e-10?Math.sqrt(mi(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){Ph(this,t)},e.getLocalTransform=function(t,r){r=r||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,h=t.anchorY,f=t.rotation||0,u=t.x,l=t.y,v=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||h){var _=i+s,y=n+h;r[4]=-_*a-v*y*o,r[5]=-y*o-c*_*a}else r[4]=r[5]=0;return r[0]=a,r[3]=o,r[1]=c*a,r[2]=v*o,f&&Za(r,r,f),r[4]+=i+u,r[5]+=n+l,r},e.initDefaultProps=function(){var t=e.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),e}(),ae=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Ph(e,t){for(var r=0;r<ae.length;r++){var i=ae[r];e[i]=t[i]}}var Zn={};function rt(e,t){t=t||lr;var r=Zn[t];r||(r=Zn[t]=new ti(500));var i=r.get(e);return i==null&&(i=vr.measureText(e,t).width,r.put(e,i)),i}function Un(e,t,r,i){var n=rt(e,t),a=Cn(t),o=Qr(0,n,r),s=Lr(0,a,i),h=new N(o,s,n,a);return h}function Mh(e,t,r,i){var n=((e||"")+"").split(`
`),a=n.length;if(a===1)return Un(n[0],t,r,i);for(var o=new N(0,0,0,0),s=0;s<n.length;s++){var h=Un(n[s],t,r,i);s===0?o.copy(h):o.union(h)}return o}function Qr(e,t,r){return r==="right"?e-=t:r==="center"&&(e-=t/2),e}function Lr(e,t,r){return r==="middle"?e-=t/2:r==="bottom"&&(e-=t),e}function Cn(e){return rt("国",e)}function oe(e,t){return typeof e=="string"?e.lastIndexOf("%")>=0?parseFloat(e)/100*t:parseFloat(e):e}function Rh(e,t,r){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=r.height,o=r.width,s=a/2,h=r.x,f=r.y,u="left",l="top";if(i instanceof Array)h+=oe(i[0],r.width),f+=oe(i[1],r.height),u=null,l=null;else switch(i){case"left":h-=n,f+=s,u="right",l="middle";break;case"right":h+=n+o,f+=s,l="middle";break;case"top":h+=o/2,f-=n,u="center",l="bottom";break;case"bottom":h+=o/2,f+=a+n,u="center";break;case"inside":h+=o/2,f+=s,u="center",l="middle";break;case"insideLeft":h+=n,f+=s,l="middle";break;case"insideRight":h+=o-n,f+=s,u="right",l="middle";break;case"insideTop":h+=o/2,f+=n,u="center";break;case"insideBottom":h+=o/2,f+=a-n,u="center",l="bottom";break;case"insideTopLeft":h+=n,f+=n;break;case"insideTopRight":h+=o-n,f+=n,u="right";break;case"insideBottomLeft":h+=n,f+=a-n,l="bottom";break;case"insideBottomRight":h+=o-n,f+=a-n,u="right",l="bottom";break}return e=e||{},e.x=h,e.y=f,e.align=u,e.verticalAlign=l,e}var wi="__zr_normal__",Ti=ae.concat(["ignore"]),xh=he(ae,function(e,t){return e[t]=!0,e},{ignore:!1}),Tr={},Dh=new N(0,0,0,0),ri=function(){function e(t){this.id=dn(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return e.prototype._init=function(t){this.attr(t)},e.prototype.drift=function(t,r,i){switch(this.draggable){case"horizontal":r=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=r,this.decomposeTransform(),this.markRedraw()},e.prototype.beforeUpdate=function(){},e.prototype.afterUpdate=function(){},e.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},e.prototype.updateInnerText=function(t){var r=this._textContent;if(r&&(!r.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=r.innerTransformable,o=void 0,s=void 0,h=!1;a.parent=n?this:null;var f=!1;if(a.copyTransform(r),i.position!=null){var u=Dh;i.layoutRect?u.copy(i.layoutRect):u.copy(this.getBoundingRect()),n||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Tr,i,u):Rh(Tr,i,u),a.x=Tr.x,a.y=Tr.y,o=Tr.align,s=Tr.verticalAlign;var l=i.origin;if(l&&i.rotation!=null){var v=void 0,c=void 0;l==="center"?(v=u.width*.5,c=u.height*.5):(v=oe(l[0],u.width),c=oe(l[1],u.height)),f=!0,a.originX=-a.x+v+(n?0:u.x),a.originY=-a.y+c+(n?0:u.y)}}i.rotation!=null&&(a.rotation=i.rotation);var _=i.offset;_&&(a.x+=_[0],a.y+=_[1],f||(a.originX=-_[0],a.originY=-_[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),p=void 0,g=void 0,m=void 0;y&&this.canBeInsideText()?(p=i.insideFill,g=i.insideStroke,(p==null||p==="auto")&&(p=this.getInsideTextFill()),(g==null||g==="auto")&&(g=this.getInsideTextStroke(p),m=!0)):(p=i.outsideFill,g=i.outsideStroke,(p==null||p==="auto")&&(p=this.getOutsideFill()),(g==null||g==="auto")&&(g=this.getOutsideStroke(p),m=!0)),p=p||"#000",(p!==d.fill||g!==d.stroke||m!==d.autoStroke||o!==d.align||s!==d.verticalAlign)&&(h=!0,d.fill=p,d.stroke=g,d.autoStroke=m,d.align=o,d.verticalAlign=s,r.setDefaultTextStyle(d)),r.__dirty|=Lt,h&&r.dirtyStyle(!0)}},e.prototype.canBeInsideText=function(){return!0},e.prototype.getInsideTextFill=function(){return"#fff"},e.prototype.getInsideTextStroke=function(t){return"#000"},e.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?en:rn},e.prototype.getOutsideStroke=function(t){var r=this.__zr&&this.__zr.getBackgroundColor(),i=typeof r=="string"&&ht(r);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,_r(i,"rgba")},e.prototype.traverse=function(t,r){},e.prototype.attrKV=function(t,r){t==="textConfig"?this.setTextConfig(r):t==="textContent"?this.setTextContent(r):t==="clipPath"?this.setClipPath(r):t==="extra"?(this.extra=this.extra||{},H(this.extra,r)):this[t]=r},e.prototype.hide=function(){this.ignore=!0,this.markRedraw()},e.prototype.show=function(){this.ignore=!1,this.markRedraw()},e.prototype.attr=function(t,r){if(typeof t=="string")this.attrKV(t,r);else if(Dt(t))for(var i=t,n=tt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},e.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var r=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==wi)){var o=n.targetName,s=o?r[o]:r;n.saveTo(s)}}},e.prototype._innerSaveToNormal=function(t){var r=this._normalState;r||(r=this._normalState={}),t.textConfig&&!r.textConfig&&(r.textConfig=this.textConfig),this._savePrimaryToNormal(t,r,Ti)},e.prototype._savePrimaryToNormal=function(t,r,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in r)&&(r[a]=this[a])}},e.prototype.hasState=function(){return this.currentStates.length>0},e.prototype.getState=function(t){return this.states[t]},e.prototype.ensureState=function(t){var r=this.states;return r[t]||(r[t]={}),r[t]},e.prototype.clearStates=function(t){this.useState(wi,!1,t)},e.prototype.useState=function(t,r,i,n){var a=t===wi,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,h=this.stateTransition;if(!(ct(s,t)>=0&&(r||s.length===1))){var f;if(this.stateProxy&&!a&&(f=this.stateProxy(t)),f||(f=this.states&&this.states[t]),!f&&!a){je("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(f);var u=!!(f&&f.hoverLayer||n);u&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,f,this._normalState,r,!i&&!this.__inHover&&h&&h.duration>0,h);var l=this._textContent,v=this._textGuide;return l&&l.useState(t,r,i,u),v&&v.useState(t,r,i,u),a?(this.currentStates=[],this._normalState={}):r?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!u&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),f}}},e.prototype.useStates=function(t,r,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var h=0;h<o;h++)if(t[h]!==a[h]){s=!1;break}}if(s)return;for(var h=0;h<o;h++){var f=t[h],u=void 0;this.stateProxy&&(u=this.stateProxy(f,t)),u||(u=this.states[f]),u&&n.push(u)}var l=n[o-1],v=!!(l&&l.hoverLayer||i);v&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(n),_=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!r&&!this.__inHover&&_&&_.duration>0,_);var y=this._textContent,d=this._textGuide;y&&y.useStates(t,r,v),d&&d.useStates(t,r,v),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!v&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}},e.prototype.isSilent=function(){for(var t=this.silent,r=this.parent;!t&&r;){if(r.silent){t=!0;break}r=r.parent}return t},e.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var r=this.animators[t];r.targetName&&r.changeTarget(this[r.targetName])}},e.prototype.removeState=function(t){var r=ct(this.currentStates,t);if(r>=0){var i=this.currentStates.slice();i.splice(r,1),this.useStates(i)}},e.prototype.replaceState=function(t,r,i){var n=this.currentStates.slice(),a=ct(n,t),o=ct(n,r)>=0;a>=0?o?n.splice(a,1):n[a]=r:i&&!o&&n.push(r),this.useStates(n)},e.prototype.toggleState=function(t,r){r?this.useState(t,!0):this.removeState(t)},e.prototype._mergeStates=function(t){for(var r={},i,n=0;n<t.length;n++){var a=t[n];H(r,a),a.textConfig&&(i=i||{},H(i,a.textConfig))}return i&&(r.textConfig=i),r},e.prototype._applyStateObj=function(t,r,i,n,a,o){var s=!(r&&n);r&&r.textConfig?(this.textConfig=H({},n?this.textConfig:i.textConfig),H(this.textConfig,r.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var h={},f=!1,u=0;u<Ti.length;u++){var l=Ti[u],v=a&&xh[l];r&&r[l]!=null?v?(f=!0,h[l]=r[l]):this[l]=r[l]:s&&i[l]!=null&&(v?(f=!0,h[l]=i[l]):this[l]=i[l])}if(!a)for(var u=0;u<this.animators.length;u++){var c=this.animators[u],_=c.targetName;c.getLoop()||c.__changeFinalValue(_?(r||i)[_]:r||i)}f&&this._transitionState(t,h,o)},e.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var r=this.__zr;r&&t.addSelfToZr(r),t.__zr=r,t.__hostTarget=this}},e.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},e.prototype.getClipPath=function(){return this._clipPath},e.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},e.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},e.prototype.getTextContent=function(){return this._textContent},e.prototype.setTextContent=function(t){var r=this._textContent;r!==t&&(r&&r!==t&&this.removeTextContent(),t.innerTransformable=new po,this._attachComponent(t),this._textContent=t,this.markRedraw())},e.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),H(this.textConfig,t),this.markRedraw()},e.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},e.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},e.prototype.getTextGuideLine=function(){return this._textGuide},e.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},e.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},e.prototype.markRedraw=function(){this.__dirty|=Lt;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},e.prototype.dirty=function(){this.markRedraw()},e.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var r=this._textContent,i=this._textGuide;r&&(r.__inHover=t),i&&(i.__inHover=t)},e.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.addAnimator(r[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},e.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var r=this.animators;if(r)for(var i=0;i<r.length;i++)t.animation.removeAnimator(r[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},e.prototype.animate=function(t,r,i){var n=t?this[t]:this,a=new bn(n,r,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},e.prototype.addAnimator=function(t,r){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(r)}).done(function(){var a=n.animators,o=ct(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},e.prototype.updateDuringAnimation=function(t){this.markRedraw()},e.prototype.stopAnimation=function(t,r){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(r):a.push(s)}return this.animators=a,this},e.prototype.animateTo=function(t,r,i){bi(this,t,r,i)},e.prototype.animateFrom=function(t,r,i){bi(this,t,r,i,!0)},e.prototype._transitionState=function(t,r,i,n){for(var a=bi(this,r,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},e.prototype.getBoundingRect=function(){return null},e.prototype.getPaintRect=function(){return null},e.initDefaultProps=function(){var t=e.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=Lt;function r(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var h=this[n]=[];s(this,h)}return this[n]},set:function(h){this[a]=h[0],this[o]=h[1],this[n]=h,s(this,h)}});function s(h,f){Object.defineProperty(f,0,{get:function(){return h[a]},set:function(u){h[a]=u}}),Object.defineProperty(f,1,{get:function(){return h[o]},set:function(u){h[o]=u}})}}Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),e}();pn(ri,Br);pn(ri,po);function bi(e,t,r,i,n){r=r||{};var a=[];_o(e,"",e,t,r,i,a,n);var o=a.length,s=!1,h=r.done,f=r.aborted,u=function(){s=!0,o--,o<=0&&(s?h&&h():f&&f())},l=function(){o--,o<=0&&(s?h&&h():f&&f())};o||h&&h(),a.length>0&&r.during&&a[0].during(function(_,y){r.during(y)});for(var v=0;v<a.length;v++){var c=a[v];u&&c.done(u),l&&c.aborted(l),r.force&&c.duration(r.duration),c.start(r.easing)}return a}function Ci(e,t,r){for(var i=0;i<r;i++)e[i]=t[i]}function Ah(e){return pt(e[0])}function Eh(e,t,r){if(pt(t[r]))if(pt(e[r])||(e[r]=[]),Ea(t[r])){var i=t[r].length;e[r].length!==i&&(e[r]=new t[r].constructor(i),Ci(e[r],t[r],i))}else{var n=t[r],a=e[r],o=n.length;if(Ah(n))for(var s=n[0].length,h=0;h<o;h++)a[h]?Ci(a[h],n[h],s):a[h]=Array.prototype.slice.call(n[h]);else Ci(a,n,o);a.length=n.length}else e[r]=t[r]}function Ih(e,t){return e===t||pt(e)&&pt(t)&&Oh(e,t)}function Oh(e,t){var r=e.length;if(r!==t.length)return!1;for(var i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function _o(e,t,r,i,n,a,o,s){for(var h=tt(i),f=n.duration,u=n.delay,l=n.additive,v=n.setToFinal,c=!Dt(a),_=e.animators,y=[],d=0;d<h.length;d++){var p=h[d],g=i[p];if(g!=null&&r[p]!=null&&(c||a[p]))if(Dt(g)&&!pt(g)&&!ue(g)){if(t){s||(r[p]=g,e.updateDuringAnimation(t));continue}_o(e,p,r[p],g,n,a&&a[p],o,s)}else y.push(p);else s||(r[p]=g,e.updateDuringAnimation(t),y.push(p))}var m=y.length;if(!l&&m)for(var w=0;w<_.length;w++){var S=_[w];if(S.targetName===t){var T=S.stopTracks(y);if(T){var b=ct(_,S);_.splice(b,1)}}}if(n.force||(y=ki(y,function(P){return!Ih(i[P],r[P])}),m=y.length),m>0||n.force&&!o.length){var C=void 0,L=void 0,M=void 0;if(s){L={},v&&(C={});for(var w=0;w<m;w++){var p=y[w];L[p]=r[p],v?C[p]=i[p]:r[p]=i[p]}}else if(v){M={};for(var w=0;w<m;w++){var p=y[w];M[p]=ze(r[p]),Eh(r,i,p)}}var S=new bn(r,!1,!1,l?ki(_,function(R){return R.targetName===t}):null);S.targetName=t,n.scope&&(S.scope=n.scope),v&&C&&S.whenWithKeys(0,C,y),M&&S.whenWithKeys(0,M,y),S.whenWithKeys(f??500,s?L:i,y).delay(u||0),e.addAnimator(S,t),o.push(S)}}var go=function(e){F(t,e);function t(r){var i=e.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(r){return this._children[r]},t.prototype.childOfName=function(r){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===r)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(r){return r&&r!==this&&r.parent!==this&&(this._children.push(r),this._doAdd(r)),this},t.prototype.addBefore=function(r,i){if(r&&r!==this&&r.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,r),this._doAdd(r))}return this},t.prototype.replace=function(r,i){var n=ct(this._children,r);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(r,i){var n=this._children,a=n[i];if(r&&r!==this&&r.parent!==this&&r!==a){n[i]=r,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(r)}return this},t.prototype._doAdd=function(r){r.parent&&r.parent.remove(r),r.parent=this;var i=this.__zr;i&&i!==r.__zr&&r.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(r){var i=this.__zr,n=this._children,a=ct(n,r);return a<0?this:(n.splice(a,1),r.parent=null,i&&r.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var r=this._children,i=this.__zr,n=0;n<r.length;n++){var a=r[n];i&&a.removeSelfFromZr(i),a.parent=null}return r.length=0,this},t.prototype.eachChild=function(r,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];r.call(i,o,a)}return this},t.prototype.traverse=function(r,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=r.call(i,a);a.isGroup&&!o&&a.traverse(r,i)}return this},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(r)}},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(r)}},t.prototype.getBoundingRect=function(r){for(var i=new N(0,0,0,0),n=r||this._children,a=[],o=null,s=0;s<n.length;s++){var h=n[s];if(!(h.ignore||h.invisible)){var f=h.getBoundingRect(),u=h.getLocalTransform(a);u?(N.applyTransform(i,f,u),o=o||i.clone(),o.union(i)):(o=o||f.clone(),o.union(f))}}return o||i},t}(ri);go.prototype.type="group";/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Ye={},ir={};function Fh(e){delete ir[e]}function Bh(e){if(!e)return!1;if(typeof e=="string")return ne(e,1)<tn;if(e.colorStops){for(var t=e.colorStops,r=0,i=t.length,n=0;n<i;n++)r+=ne(t[n].color,1);return r/=i,r<tn}return!1}var Hh=function(){function e(t,r,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=r,this.id=t;var a=new qs,o=i.renderer||"canvas";Ye[o]||(o=tt(Ye)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Ye[o](r,a,i,t),h=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var f=!k.node&&!k.worker&&!h?new Ch(s.getViewportRoot(),s.root):null,u=i.useCoarsePointer,l=u==null||u==="auto"?k.touchEventsSupported:!!u,v=44,c;l&&(c=J(i.pointerSize,v)),this.handler=new Ka(a,s,f,s.root,c),this.animation=new _h({stage:{update:h?null:function(){return n._flush(!0)}}}),h||this.animation.start()}return e.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},e.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},e.prototype.configLayer=function(t,r){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,r),this.refresh())},e.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Bh(t))},e.prototype.getBackgroundColor=function(){return this._backgroundColor},e.prototype.setDarkMode=function(t){this._darkMode=t},e.prototype.isDarkMode=function(){return this._darkMode},e.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},e.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},e.prototype.flush=function(){this._disposed||this._flush(!1)},e.prototype._flush=function(t){var r,i=Mr();this._needsRefresh&&(r=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(r=!0,this.refreshHoverImmediately());var n=Mr();r?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},e.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},e.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},e.prototype.refreshHover=function(){this._needsRefreshHover=!0},e.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},e.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},e.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},e.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},e.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},e.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},e.prototype.findHover=function(t,r){if(!this._disposed)return this.handler.findHover(t,r)},e.prototype.on=function(t,r,i){return this._disposed||this.handler.on(t,r,i),this},e.prototype.off=function(t,r){this._disposed||this.handler.off(t,r)},e.prototype.trigger=function(t,r){this._disposed||this.handler.trigger(t,r)},e.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),r=0;r<t.length;r++)t[r]instanceof go&&t[r].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},e.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,Fh(this.id))},e}();function zh(e,t){var r=new Hh(dn(),e,t);return ir[r.id]=r,r}function kh(e){e.dispose()}function Nh(){for(var e in ir)ir.hasOwnProperty(e)&&ir[e].dispose();ir={}}function Wh(e){return ir[e]}function Yh(e,t){Ye[e]=t}var nn;function Gh(e){if(typeof nn=="function")return nn(e)}function Xh(e){nn=e}var qh="5.6.1";const Wu=Object.freeze(Object.defineProperty({__proto__:null,dispose:kh,disposeAll:Nh,getElementSSRData:Gh,getInstance:Wh,init:zh,registerPainter:Yh,registerSSRDataGetter:Xh,version:qh},Symbol.toStringTag,{value:"Module"}));var an=new ti(50);function $h(e){if(typeof e=="string"){var t=an.get(e);return t&&t.image}else return e}function yo(e,t,r,i,n){if(e)if(typeof e=="string"){if(t&&t.__zrImageSrc===e||!r)return t;var a=an.get(e),o={hostEl:r,cb:i,cbPayload:n};return a?(t=a.image,!ei(t)&&a.pending.push(o)):(t=vr.loadImage(e,Vn,Vn),t.__zrImageSrc=e,an.put(e,t.__cachedImgObj={image:t,pending:[o]})),t}else return e;else return t}function Vn(){var e=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<e.pending.length;t++){var r=e.pending[t],i=r.cb;i&&i(this,r.cbPayload),r.hostEl.dirty()}e.pending.length=0}function ei(e){return e&&e.width&&e.height}var Si=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Yu(e,t,r,i,n){var a={};return mo(a,e,t,r,i,n),a.text}function mo(e,t,r,i,n,a){if(!r){e.text="",e.isTruncated=!1;return}var o=(t+"").split(`
`);a=wo(r,i,n,a);for(var s=!1,h={},f=0,u=o.length;f<u;f++)To(h,o[f],a),o[f]=h.textLine,s=s||h.isTruncated;e.text=o.join(`
`),e.isTruncated=s}function wo(e,t,r,i){i=i||{};var n=H({},i);n.font=t,r=J(r,"..."),n.maxIterations=J(i.maxIterations,2);var a=n.minChar=J(i.minChar,0);n.cnCharWidth=rt("国",t);var o=n.ascCharWidth=rt("a",t);n.placeholder=J(i.placeholder,"");for(var s=e=Math.max(0,e-1),h=0;h<a&&s>=o;h++)s-=o;var f=rt(r,t);return f>s&&(r="",f=0),s=e-f,n.ellipsis=r,n.ellipsisWidth=f,n.contentWidth=s,n.containerWidth=e,n}function To(e,t,r){var i=r.containerWidth,n=r.font,a=r.contentWidth;if(!i){e.textLine="",e.isTruncated=!1;return}var o=rt(t,n);if(o<=i){e.textLine=t,e.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=r.maxIterations){t+=r.ellipsis;break}var h=s===0?Zh(t,a,r.ascCharWidth,r.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,h),o=rt(t,n)}t===""&&(t=r.placeholder),e.textLine=t,e.isTruncated=!0}function Zh(e,t,r,i){for(var n=0,a=0,o=e.length;a<o&&n<t;a++){var s=e.charCodeAt(a);n+=0<=s&&s<=127?r:i}return a}function Uh(e,t){e!=null&&(e+="");var r=t.overflow,i=t.padding,n=t.font,a=r==="truncate",o=Cn(n),s=J(t.lineHeight,o),h=!!t.backgroundColor,f=t.lineOverflow==="truncate",u=!1,l=t.width,v;l!=null&&(r==="break"||r==="breakAll")?v=e?bo(e,t.font,l,r==="breakAll",0).lines:[]:v=e?e.split(`
`):[];var c=v.length*s,_=J(t.height,c);if(c>_&&f){var y=Math.floor(_/s);u=u||v.length>y,v=v.slice(0,y)}if(e&&a&&l!=null)for(var d=wo(l,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),p={},g=0;g<v.length;g++)To(p,v[g],d),v[g]=p.textLine,u=u||p.isTruncated;for(var m=_,w=0,g=0;g<v.length;g++)w=Math.max(rt(v[g],n),w);l==null&&(l=w);var S=w;return i&&(m+=i[0]+i[2],S+=i[1]+i[3],l+=i[1]+i[3]),h&&(S=l),{lines:v,height:_,outerWidth:S,outerHeight:m,lineHeight:s,calculatedLineHeight:o,contentWidth:w,contentHeight:c,width:l,isTruncated:u}}var Vh=function(){function e(){}return e}(),Qn=function(){function e(t){this.tokens=[],t&&(this.tokens=t)}return e}(),Qh=function(){function e(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return e}();function Kh(e,t){var r=new Qh;if(e!=null&&(e+=""),!e)return r;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=Si.lastIndex=0,h;(h=Si.exec(e))!=null;){var f=h.index;f>s&&Li(r,e.substring(s,f),t,o),Li(r,h[2],t,o,h[1]),s=Si.lastIndex}s<e.length&&Li(r,e.substring(s,e.length),t,o);var u=[],l=0,v=0,c=t.padding,_=a==="truncate",y=t.lineOverflow==="truncate",d={};function p(_t,et,Y){_t.width=et,_t.lineHeight=Y,l+=Y,v=Math.max(v,et)}t:for(var g=0;g<r.lines.length;g++){for(var m=r.lines[g],w=0,S=0,T=0;T<m.tokens.length;T++){var b=m.tokens[T],C=b.styleName&&t.rich[b.styleName]||{},L=b.textPadding=C.padding,M=L?L[1]+L[3]:0,P=b.font=C.font||t.font;b.contentHeight=Cn(P);var R=J(C.height,b.contentHeight);if(b.innerHeight=R,L&&(R+=L[0]+L[2]),b.height=R,b.lineHeight=jr(C.lineHeight,t.lineHeight,R),b.align=C&&C.align||t.align,b.verticalAlign=C&&C.verticalAlign||"middle",y&&n!=null&&l+b.lineHeight>n){var O=r.lines.length;T>0?(m.tokens=m.tokens.slice(0,T),p(m,S,w),r.lines=r.lines.slice(0,g+1)):r.lines=r.lines.slice(0,g),r.isTruncated=r.isTruncated||r.lines.length<O;break t}var x=C.width,B=x==null||x==="auto";if(typeof x=="string"&&x.charAt(x.length-1)==="%")b.percentWidth=x,u.push(b),b.contentWidth=rt(b.text,P);else{if(B){var D=C.backgroundColor,W=D&&D.image;W&&(W=$h(W),ei(W)&&(b.width=Math.max(b.width,W.width*R/W.height)))}var V=_&&i!=null?i-S:null;V!=null&&V<b.width?!B||V<M?(b.text="",b.width=b.contentWidth=0):(mo(d,b.text,V-M,P,t.ellipsis,{minChar:t.truncateMinChar}),b.text=d.text,r.isTruncated=r.isTruncated||d.isTruncated,b.width=b.contentWidth=rt(b.text,P)):b.contentWidth=rt(b.text,P)}b.width+=M,S+=b.width,C&&(w=Math.max(w,b.lineHeight))}p(m,S,w)}r.outerWidth=r.width=J(i,v),r.outerHeight=r.height=J(n,l),r.contentHeight=l,r.contentWidth=v,c&&(r.outerWidth+=c[1]+c[3],r.outerHeight+=c[0]+c[2]);for(var g=0;g<u.length;g++){var b=u[g],Pt=b.percentWidth;b.width=parseInt(Pt,10)/100*r.width}return r}function Li(e,t,r,i,n){var a=t==="",o=n&&r.rich[n]||{},s=e.lines,h=o.font||r.font,f=!1,u,l;if(i){var v=o.padding,c=v?v[1]+v[3]:0;if(o.width!=null&&o.width!=="auto"){var _=oe(o.width,i.width)+c;s.length>0&&_+i.accumWidth>i.width&&(u=t.split(`
`),f=!0),i.accumWidth=_}else{var y=bo(t,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+c,l=y.linesWidths,u=y.lines}}else u=t.split(`
`);for(var d=0;d<u.length;d++){var p=u[d],g=new Vh;if(g.styleName=n,g.text=p,g.isLineHolder=!p&&!a,typeof o.width=="number"?g.width=o.width:g.width=l?l[d]:rt(p,h),!d&&!f){var m=(s[s.length-1]||(s[0]=new Qn)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=g:(p||!w||a)&&m.push(g)}else s.push(new Qn([g]))}}function Jh(e){var t=e.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var jh=he(",&?/;] ".split(""),function(e,t){return e[t]=!0,e},{});function tf(e){return Jh(e)?!!jh[e]:!0}function bo(e,t,r,i,n){for(var a=[],o=[],s="",h="",f=0,u=0,l=0;l<e.length;l++){var v=e.charAt(l);if(v===`
`){h&&(s+=h,u+=f),a.push(s),o.push(u),s="",h="",f=0,u=0;continue}var c=rt(v,t),_=i?!1:!tf(v);if(a.length?u+c>r:n+u+c>r){u?(s||h)&&(_?(s||(s=h,h="",f=0,u=f),a.push(s),o.push(u-f),h+=v,f+=c,s="",u=f):(h&&(s+=h,h="",f=0),a.push(s),o.push(u),s=v,u=c)):_?(a.push(h),o.push(f),h=v,f=c):(a.push(v),o.push(c));continue}u+=c,_?(h+=v,f+=c):(h&&(s+=h,h="",f=0),s+=v)}return!a.length&&!s&&(s=e,h="",f=0),h&&(s+=h),s&&(a.push(s),o.push(u)),a.length===1&&(u+=n),{accumWidth:u,lines:a,linesWidths:o}}var on="__zr_style_"+Math.round(Math.random()*10),ur={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ii={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};ur[on]=!0;var Kn=["z","z2","invisible"],rf=["invisible"],ve=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype._init=function(r){for(var i=tt(r),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(r[a]):e.prototype.attrKV.call(this,a,r[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(r,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&ef(this,r,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var h=this.parent;h;){if(h.ignore)return!1;h=h.parent}return!0},t.prototype.contain=function(r,i){return this.rectContain(r,i)},t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.rectContain=function(r,i){var n=this.transformCoordToLocal(r,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var r=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,h=a.shadowOffsetY||0;r=this._paintRect||(this._paintRect=new N(0,0,0,0)),i?N.applyTransform(r,n,i):r.copy(n),(o||s||h)&&(r.width+=o*2+Math.abs(s),r.height+=o*2+Math.abs(h),r.x=Math.min(r.x,r.x+s-o),r.y=Math.min(r.y,r.y+h-o));var f=this.dirtyRectTolerance;r.isZero()||(r.x=Math.floor(r.x-f),r.y=Math.floor(r.y-f),r.width=Math.ceil(r.width+1+f*2),r.height=Math.ceil(r.height+1+f*2))}return r},t.prototype.setPrevPaintRect=function(r){r?(this._prevPaintRect=this._prevPaintRect||new N(0,0,0,0),this._prevPaintRect.copy(r)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(r){return this.animate("style",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(r,i){r!=="style"?e.prototype.attrKV.call(this,r,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(r,i){return typeof r=="string"?this.style[r]=i:H(this.style,r),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(r){r||this.markRedraw(),this.__dirty|=He,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&He)},t.prototype.styleUpdated=function(){this.__dirty&=-3},t.prototype.createStyle=function(r){return le(ur,r)},t.prototype.useStyle=function(r){r[on]||(r=this.createStyle(r)),this.__inHover?this.__hoverStyle=r:this.style=r,this.dirtyStyle()},t.prototype.isStyleObject=function(r){return r[on]},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(r,i,Kn)},t.prototype._applyStateObj=function(r,i,n,a,o,s){e.prototype._applyStateObj.call(this,r,i,n,a,o,s);var h=!(i&&a),f;if(i&&i.style?o?a?f=i.style:(f=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(f,i.style)):(f=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(f,i.style)):h&&(f=n.style),f)if(o){var u=this.style;if(this.style=this.createStyle(h?{}:u),h)for(var l=tt(u),v=0;v<l.length;v++){var c=l[v];c in f&&(f[c]=f[c],this.style[c]=u[c])}for(var _=tt(f),v=0;v<_.length;v++){var c=_[v];this.style[c]=this.style[c]}this._transitionState(r,{style:f},s,this.getAnimationStyleProps())}else this.useStyle(f);for(var y=this.__inHover?rf:Kn,v=0;v<y.length;v++){var c=y[v];i&&i[c]!=null?this[c]=i[c]:h&&n[c]!=null&&(this[c]=n[c])}},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),n,a=0;a<r.length;a++){var o=r[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(r,i){return H(r,i),r},t.prototype.getAnimationStyleProps=function(){return ii},t.initDefaultProps=function(){var r=t.prototype;r.type="displayable",r.invisible=!1,r.z=0,r.z2=0,r.zlevel=0,r.culling=!1,r.cursor="pointer",r.rectHover=!1,r.incremental=!1,r._rect=null,r.dirtyRectTolerance=0,r.__dirty=Lt|He}(),t}(ri),Pi=new N(0,0,0,0),Mi=new N(0,0,0,0);function ef(e,t,r){return Pi.copy(e.getBoundingRect()),e.transform&&Pi.applyTransform(e.transform),Mi.width=t,Mi.height=r,!Pi.intersect(Mi)}var ot=Math.min,st=Math.max,Ri=Math.sin,xi=Math.cos,Xt=Math.PI*2,Ce=pr(),Se=pr(),Le=pr();function Jn(e,t,r,i,n,a){n[0]=ot(e,r),n[1]=ot(t,i),a[0]=st(e,r),a[1]=st(t,i)}var jn=[],ta=[];function nf(e,t,r,i,n,a,o,s,h,f){var u=no,l=G,v=u(e,r,n,o,jn);h[0]=1/0,h[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var c=0;c<v;c++){var _=l(e,r,n,o,jn[c]);h[0]=ot(_,h[0]),f[0]=st(_,f[0])}v=u(t,i,a,s,ta);for(var c=0;c<v;c++){var y=l(t,i,a,s,ta[c]);h[1]=ot(y,h[1]),f[1]=st(y,f[1])}h[0]=ot(e,h[0]),f[0]=st(e,f[0]),h[0]=ot(o,h[0]),f[0]=st(o,f[0]),h[1]=ot(t,h[1]),f[1]=st(t,f[1]),h[1]=ot(s,h[1]),f[1]=st(s,f[1])}function af(e,t,r,i,n,a,o,s){var h=ao,f=q,u=st(ot(h(e,r,n),1),0),l=st(ot(h(t,i,a),1),0),v=f(e,r,n,u),c=f(t,i,a,l);o[0]=ot(e,n,v),o[1]=ot(t,a,c),s[0]=st(e,n,v),s[1]=st(t,a,c)}function of(e,t,r,i,n,a,o,s,h){var f=rr,u=er,l=Math.abs(n-a);if(l%Xt<1e-4&&l>1e-4){s[0]=e-r,s[1]=t-i,h[0]=e+r,h[1]=t+i;return}if(Ce[0]=xi(n)*r+e,Ce[1]=Ri(n)*i+t,Se[0]=xi(a)*r+e,Se[1]=Ri(a)*i+t,f(s,Ce,Se),u(h,Ce,Se),n=n%Xt,n<0&&(n=n+Xt),a=a%Xt,a<0&&(a=a+Xt),n>a&&!o?a+=Xt:n<a&&o&&(n+=Xt),o){var v=a;a=n,n=v}for(var c=0;c<a;c+=Math.PI/2)c>n&&(Le[0]=xi(c)*r+e,Le[1]=Ri(c)*i+t,f(s,Le,s),u(h,Le,h))}var I={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},qt=[],$t=[],wt=[],At=[],Tt=[],bt=[],Di=Math.min,Ai=Math.max,Zt=Math.cos,Ut=Math.sin,Mt=Math.abs,sn=Math.PI,It=sn*2,Ei=typeof Float32Array<"u",qr=[];function Ii(e){var t=Math.round(e/sn*1e8)/1e8;return t%2*sn}function sf(e,t){var r=Ii(e[0]);r<0&&(r+=It);var i=r-e[0],n=e[1];n+=i,!t&&n-r>=It?n=r+It:t&&r-n>=It?n=r-It:!t&&r>n?n=r+(It-Ii(r-n)):t&&r<n&&(n=r-(It-Ii(n-r))),e[0]=r,e[1]=n}var Or=function(){function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,r,i){i=i||0,i>0&&(this._ux=Mt(i/Ue/t)||0,this._uy=Mt(i/Ue/r)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,r){return this._drawPendingPt(),this.addData(I.M,t,r),this._ctx&&this._ctx.moveTo(t,r),this._x0=t,this._y0=r,this._xi=t,this._yi=r,this},e.prototype.lineTo=function(t,r){var i=Mt(t-this._xi),n=Mt(r-this._yi),a=i>this._ux||n>this._uy;if(this.addData(I.L,t,r),this._ctx&&a&&this._ctx.lineTo(t,r),a)this._xi=t,this._yi=r,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=r,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,r,i,n,a,o){return this._drawPendingPt(),this.addData(I.C,t,r,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,r,i,n,a,o),this._xi=a,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,r,i,n){return this._drawPendingPt(),this.addData(I.Q,t,r,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,r,i,n),this._xi=i,this._yi=n,this},e.prototype.arc=function(t,r,i,n,a,o){this._drawPendingPt(),qr[0]=n,qr[1]=a,sf(qr,o),n=qr[0],a=qr[1];var s=a-n;return this.addData(I.A,t,r,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,r,i,n,a,o),this._xi=Zt(a)*i+t,this._yi=Ut(a)*i+r,this},e.prototype.arcTo=function(t,r,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,r,i,n,a),this},e.prototype.rect=function(t,r,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,r,i,n),this.addData(I.R,t,r,i,n),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(I.Z);var t=this._ctx,r=this._x0,i=this._y0;return t&&t.closePath(),this._xi=r,this._yi=i,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var r=t.length;!(this.data&&this.data.length===r)&&Ei&&(this.data=new Float32Array(r));for(var i=0;i<r;i++)this.data[i]=t[i];this._len=r},e.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var r=t.length,i=0,n=this._len,a=0;a<r;a++)i+=t[a].len();Ei&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<r;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},e.prototype.addData=function(t,r,i,n,a,o,s,h,f){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var l=0;l<arguments.length;l++)u[this._len++]=arguments[l]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],r=0;r<this._len;r++)t[r]=this.data[r];this.data=t}},e.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Ei&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){wt[0]=wt[1]=Tt[0]=Tt[1]=Number.MAX_VALUE,At[0]=At[1]=bt[0]=bt[1]=-Number.MAX_VALUE;var t=this.data,r=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],h=o===1;switch(h&&(r=t[o],i=t[o+1],n=r,a=i),s){case I.M:r=n=t[o++],i=a=t[o++],Tt[0]=n,Tt[1]=a,bt[0]=n,bt[1]=a;break;case I.L:Jn(r,i,t[o],t[o+1],Tt,bt),r=t[o++],i=t[o++];break;case I.C:nf(r,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Tt,bt),r=t[o++],i=t[o++];break;case I.Q:af(r,i,t[o++],t[o++],t[o],t[o+1],Tt,bt),r=t[o++],i=t[o++];break;case I.A:var f=t[o++],u=t[o++],l=t[o++],v=t[o++],c=t[o++],_=t[o++]+c;o+=1;var y=!t[o++];h&&(n=Zt(c)*l+f,a=Ut(c)*v+u),of(f,u,l,v,c,_,y,Tt,bt),r=Zt(_)*l+f,i=Ut(_)*v+u;break;case I.R:n=r=t[o++],a=i=t[o++];var d=t[o++],p=t[o++];Jn(n,a,n+d,a+p,Tt,bt);break;case I.Z:r=n,i=a;break}rr(wt,wt,Tt),er(At,At,bt)}return o===0&&(wt[0]=wt[1]=At[0]=At[1]=0),new N(wt[0],wt[1],At[0]-wt[0],At[1]-wt[1])},e.prototype._calculateLength=function(){var t=this.data,r=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,h=0;this._pathSegLen||(this._pathSegLen=[]);for(var f=this._pathSegLen,u=0,l=0,v=0;v<r;){var c=t[v++],_=v===1;_&&(a=t[v],o=t[v+1],s=a,h=o);var y=-1;switch(c){case I.M:a=s=t[v++],o=h=t[v++];break;case I.L:{var d=t[v++],p=t[v++],g=d-a,m=p-o;(Mt(g)>i||Mt(m)>n||v===r-1)&&(y=Math.sqrt(g*g+m*m),a=d,o=p);break}case I.C:{var w=t[v++],S=t[v++],d=t[v++],p=t[v++],T=t[v++],b=t[v++];y=Zs(a,o,w,S,d,p,T,b,10),a=T,o=b;break}case I.Q:{var w=t[v++],S=t[v++],d=t[v++],p=t[v++];y=Qs(a,o,w,S,d,p,10),a=d,o=p;break}case I.A:var C=t[v++],L=t[v++],M=t[v++],P=t[v++],R=t[v++],O=t[v++],x=O+R;v+=1,_&&(s=Zt(R)*M+C,h=Ut(R)*P+L),y=Ai(M,P)*Di(It,Math.abs(O)),a=Zt(x)*M+C,o=Ut(x)*P+L;break;case I.R:{s=a=t[v++],h=o=t[v++];var B=t[v++],D=t[v++];y=B*2+D*2;break}case I.Z:{var g=s-a,m=h-o;y=Math.sqrt(g*g+m*m),a=s,o=h;break}}y>=0&&(f[l++]=y,u+=y)}return this._pathLen=u,u},e.prototype.rebuildPath=function(t,r){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,h,f,u,l,v,c=r<1,_,y,d=0,p=0,g,m=0,w,S;if(!(c&&(this._pathSegLen||this._calculateLength(),_=this._pathSegLen,y=this._pathLen,g=r*y,!g)))t:for(var T=0;T<o;){var b=i[T++],C=T===1;switch(C&&(f=i[T],u=i[T+1],s=f,h=u),b!==I.L&&m>0&&(t.lineTo(w,S),m=0),b){case I.M:s=f=i[T++],h=u=i[T++],t.moveTo(f,u);break;case I.L:{l=i[T++],v=i[T++];var L=Mt(l-f),M=Mt(v-u);if(L>n||M>a){if(c){var P=_[p++];if(d+P>g){var R=(g-d)/P;t.lineTo(f*(1-R)+l*R,u*(1-R)+v*R);break t}d+=P}t.lineTo(l,v),f=l,u=v,m=0}else{var O=L*L+M*M;O>m&&(w=l,S=v,m=O)}break}case I.C:{var x=i[T++],B=i[T++],D=i[T++],W=i[T++],V=i[T++],Pt=i[T++];if(c){var P=_[p++];if(d+P>g){var R=(g-d)/P;$e(f,x,D,V,R,qt),$e(u,B,W,Pt,R,$t),t.bezierCurveTo(qt[1],$t[1],qt[2],$t[2],qt[3],$t[3]);break t}d+=P}t.bezierCurveTo(x,B,D,W,V,Pt),f=V,u=Pt;break}case I.Q:{var x=i[T++],B=i[T++],D=i[T++],W=i[T++];if(c){var P=_[p++];if(d+P>g){var R=(g-d)/P;Ze(f,x,D,R,qt),Ze(u,B,W,R,$t),t.quadraticCurveTo(qt[1],$t[1],qt[2],$t[2]);break t}d+=P}t.quadraticCurveTo(x,B,D,W),f=D,u=W;break}case I.A:var _t=i[T++],et=i[T++],Y=i[T++],gt=i[T++],yt=i[T++],gr=i[T++],Hr=i[T++],zr=!i[T++],ce=Y>gt?Y:gt,ft=Mt(Y-gt)>.001,Z=yt+gr,A=!1;if(c){var P=_[p++];d+P>g&&(Z=yt+gr*(g-d)/P,A=!0),d+=P}if(ft&&t.ellipse?t.ellipse(_t,et,Y,gt,Hr,yt,Z,zr):t.arc(_t,et,ce,yt,Z,zr),A)break t;C&&(s=Zt(yt)*Y+_t,h=Ut(yt)*gt+et),f=Zt(Z)*Y+_t,u=Ut(Z)*gt+et;break;case I.R:s=f=i[T],h=u=i[T+1],l=i[T++],v=i[T++];var E=i[T++],kr=i[T++];if(c){var P=_[p++];if(d+P>g){var mt=g-d;t.moveTo(l,v),t.lineTo(l+Di(mt,E),v),mt-=E,mt>0&&t.lineTo(l+E,v+Di(mt,kr)),mt-=kr,mt>0&&t.lineTo(l+Ai(E-mt,0),v+kr),mt-=E,mt>0&&t.lineTo(l,v+Ai(kr-mt,0));break t}d+=P}t.rect(l,v,E,kr);break;case I.Z:if(c){var P=_[p++];if(d+P>g){var R=(g-d)/P;t.lineTo(f*(1-R)+s*R,u*(1-R)+h*R);break t}d+=P}t.closePath(),f=s,u=h}}},e.prototype.clone=function(){var t=new e,r=this.data;return t.data=r.slice?r.slice():Array.prototype.slice.call(r),t._len=this._len,t},e.CMD=I,e.initDefaultProps=function(){var t=e.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),e}();function br(e,t,r,i,n,a,o){if(n===0)return!1;var s=n,h=0,f=e;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>e+s&&a>r+s||a<e-s&&a<r-s)return!1;if(e!==r)h=(t-i)/(e-r),f=(e*i-r*t)/(e-r);else return Math.abs(a-e)<=s/2;var u=h*a-o+f,l=u*u/(h*h+1);return l<=s/2*s/2}function hf(e,t,r,i,n,a,o,s,h,f,u){if(h===0)return!1;var l=h;if(u>t+l&&u>i+l&&u>a+l&&u>s+l||u<t-l&&u<i-l&&u<a-l&&u<s-l||f>e+l&&f>r+l&&f>n+l&&f>o+l||f<e-l&&f<r-l&&f<n-l&&f<o-l)return!1;var v=$s(e,t,r,i,n,a,o,s,f,u,null);return v<=l/2}function ff(e,t,r,i,n,a,o,s,h){if(o===0)return!1;var f=o;if(h>t+f&&h>i+f&&h>a+f||h<t-f&&h<i-f&&h<a-f||s>e+f&&s>r+f&&s>n+f||s<e-f&&s<r-f&&s<n-f)return!1;var u=Vs(e,t,r,i,n,a,s,h,null);return u<=f/2}var ra=Math.PI*2;function Pe(e){return e%=ra,e<0&&(e+=ra),e}var $r=Math.PI*2;function uf(e,t,r,i,n,a,o,s,h){if(o===0)return!1;var f=o;s-=e,h-=t;var u=Math.sqrt(s*s+h*h);if(u-f>r||u+f<r)return!1;if(Math.abs(i-n)%$r<1e-4)return!0;if(a){var l=i;i=Pe(n),n=Pe(l)}else i=Pe(i),n=Pe(n);i>n&&(n+=$r);var v=Math.atan2(h,s);return v<0&&(v+=$r),v>=i&&v<=n||v+$r>=i&&v+$r<=n}function xt(e,t,r,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var h=o*(r-e)+e;return h===n?1/0:h>n?s:0}var Et=Or.CMD,Vt=Math.PI*2,lf=1e-4;function vf(e,t){return Math.abs(e-t)<lf}var U=[-1,-1,-1],nt=[-1,-1];function cf(){var e=nt[0];nt[0]=nt[1],nt[1]=e}function df(e,t,r,i,n,a,o,s,h,f){if(f>t&&f>i&&f>a&&f>s||f<t&&f<i&&f<a&&f<s)return 0;var u=io(t,i,a,s,f,U);if(u===0)return 0;for(var l=0,v=-1,c=void 0,_=void 0,y=0;y<u;y++){var d=U[y],p=d===0||d===1?.5:1,g=G(e,r,n,o,d);g<h||(v<0&&(v=no(t,i,a,s,nt),nt[1]<nt[0]&&v>1&&cf(),c=G(t,i,a,s,nt[0]),v>1&&(_=G(t,i,a,s,nt[1]))),v===2?d<nt[0]?l+=c<t?p:-p:d<nt[1]?l+=_<c?p:-p:l+=s<_?p:-p:d<nt[0]?l+=c<t?p:-p:l+=s<c?p:-p)}return l}function pf(e,t,r,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var h=Us(t,i,a,s,U);if(h===0)return 0;var f=ao(t,i,a);if(f>=0&&f<=1){for(var u=0,l=q(t,i,a,f),v=0;v<h;v++){var c=U[v]===0||U[v]===1?.5:1,_=q(e,r,n,U[v]);_<o||(U[v]<f?u+=l<t?c:-c:u+=a<l?c:-c)}return u}else{var c=U[0]===0||U[0]===1?.5:1,_=q(e,r,n,U[0]);return _<o?0:a<t?c:-c}}function _f(e,t,r,i,n,a,o,s){if(s-=t,s>r||s<-r)return 0;var h=Math.sqrt(r*r-s*s);U[0]=-h,U[1]=h;var f=Math.abs(i-n);if(f<1e-4)return 0;if(f>=Vt-1e-4){i=0,n=Vt;var u=a?1:-1;return o>=U[0]+e&&o<=U[1]+e?u:0}if(i>n){var l=i;i=n,n=l}i<0&&(i+=Vt,n+=Vt);for(var v=0,c=0;c<2;c++){var _=U[c];if(_+e>o){var y=Math.atan2(s,_),u=a?1:-1;y<0&&(y=Vt+y),(y>=i&&y<=n||y+Vt>=i&&y+Vt<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(u=-u),v+=u)}}return v}function Co(e,t,r,i,n){for(var a=e.data,o=e.len(),s=0,h=0,f=0,u=0,l=0,v,c,_=0;_<o;){var y=a[_++],d=_===1;switch(y===Et.M&&_>1&&(r||(s+=xt(h,f,u,l,i,n))),d&&(h=a[_],f=a[_+1],u=h,l=f),y){case Et.M:u=a[_++],l=a[_++],h=u,f=l;break;case Et.L:if(r){if(br(h,f,a[_],a[_+1],t,i,n))return!0}else s+=xt(h,f,a[_],a[_+1],i,n)||0;h=a[_++],f=a[_++];break;case Et.C:if(r){if(hf(h,f,a[_++],a[_++],a[_++],a[_++],a[_],a[_+1],t,i,n))return!0}else s+=df(h,f,a[_++],a[_++],a[_++],a[_++],a[_],a[_+1],i,n)||0;h=a[_++],f=a[_++];break;case Et.Q:if(r){if(ff(h,f,a[_++],a[_++],a[_],a[_+1],t,i,n))return!0}else s+=pf(h,f,a[_++],a[_++],a[_],a[_+1],i,n)||0;h=a[_++],f=a[_++];break;case Et.A:var p=a[_++],g=a[_++],m=a[_++],w=a[_++],S=a[_++],T=a[_++];_+=1;var b=!!(1-a[_++]);v=Math.cos(S)*m+p,c=Math.sin(S)*w+g,d?(u=v,l=c):s+=xt(h,f,v,c,i,n);var C=(i-p)*w/m+p;if(r){if(uf(p,g,w,S,S+T,b,t,C,n))return!0}else s+=_f(p,g,w,S,S+T,b,C,n);h=Math.cos(S+T)*m+p,f=Math.sin(S+T)*w+g;break;case Et.R:u=h=a[_++],l=f=a[_++];var L=a[_++],M=a[_++];if(v=u+L,c=l+M,r){if(br(u,l,v,l,t,i,n)||br(v,l,v,c,t,i,n)||br(v,c,u,c,t,i,n)||br(u,c,u,l,t,i,n))return!0}else s+=xt(v,l,v,c,i,n),s+=xt(u,c,u,l,i,n);break;case Et.Z:if(r){if(br(h,f,u,l,t,i,n))return!0}else s+=xt(h,f,u,l,i,n);h=u,f=l;break}}return!r&&!vf(f,l)&&(s+=xt(h,f,u,l,i,n)||0),s!==0}function gf(e,t,r){return Co(e,0,!1,t,r)}function yf(e,t,r,i){return Co(e,t,!0,r,i)}var So=Ht({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ur),mf={style:Ht({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ii.style)},Oi=ae.concat(["invisible","culling","z","z2","zlevel","parent"]),$=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.update=function(){var r=this;e.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(h){r.buildPath(h,r.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<Oi.length;++s)n[Oi[s]]=this[Oi[s]];n.__dirty|=Lt}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(r){var i=tt(r);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=r[o];o==="style"?this.style?H(this.style,s):this.useStyle(s):o==="shape"?H(this.shape,s):e.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var r=this.style.fill;if(r!=="none"){if(ee(r)){var i=ne(r,0);return i>.5?rn:i>.2?Sh:en}else if(r)return en}return rn},t.prototype.getInsideTextStroke=function(r){var i=this.style.fill;if(ee(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=ne(r,0)<tn;if(a===o)return i}},t.prototype.buildPath=function(r,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=-5},t.prototype.getUpdatedPathProxy=function(r){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,r),this.path},t.prototype.createPathProxy=function(){this.path=new Or(!1)},t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return!(i==null||i==="none"||!(r.lineWidth>0))},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var r=this._rect,i=this.style,n=!r;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Ur)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),r=o.getBoundingRect()}if(this._rect=r,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=r.clone());if(this.__dirty||n){s.copy(r);var h=i.strokeNoScale?this.getLineScale():1,f=i.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;f=Math.max(f,u??4)}h>1e-10&&(s.width+=f/h,s.height+=f/h,s.x-=f/h/2,s.y-=f/h/2)}return s}return r},t.prototype.contain=function(r,i){var n=this.transformCoordToLocal(r,i),a=this.getBoundingRect(),o=this.style;if(r=n[0],i=n[1],a.contain(r,i)){var s=this.path;if(this.hasStroke()){var h=o.lineWidth,f=o.strokeNoScale?this.getLineScale():1;if(f>1e-10&&(this.hasFill()||(h=Math.max(h,this.strokeContainThreshold)),yf(s,h/f,r,i)))return!0}if(this.hasFill())return gf(s,r,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Ur,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(r){return this.animate("shape",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():r==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(r,i){r==="shape"?this.setShape(i):e.prototype.attrKV.call(this,r,i)},t.prototype.setShape=function(r,i){var n=this.shape;return n||(n=this.shape={}),typeof r=="string"?n[r]=i:H(n,r),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Ur)},t.prototype.createStyle=function(r){return le(So,r)},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var i=this._normalState;r.shape&&!i.shape&&(i.shape=H({},this.shape))},t.prototype._applyStateObj=function(r,i,n,a,o,s){e.prototype._applyStateObj.call(this,r,i,n,a,o,s);var h=!(i&&a),f;if(i&&i.shape?o?a?f=i.shape:(f=H({},n.shape),H(f,i.shape)):(f=H({},a?this.shape:n.shape),H(f,i.shape)):h&&(f=n.shape),f)if(o){this.shape=H({},this.shape);for(var u={},l=tt(f),v=0;v<l.length;v++){var c=l[v];typeof f[c]=="object"?this.shape[c]=f[c]:u[c]=f[c]}this._transitionState(r,{shape:u},s)}else this.shape=f,this.dirtyShape()},t.prototype._mergeStates=function(r){for(var i=e.prototype._mergeStates.call(this,r),n,a=0;a<r.length;a++){var o=r[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return mf},t.prototype.isZeroArea=function(){return!1},t.extend=function(r){var i=function(a){F(o,a);function o(s){var h=a.call(this,s)||this;return r.init&&r.init.call(h,s),h}return o.prototype.getDefaultStyle=function(){return cr(r.style)},o.prototype.getDefaultShape=function(){return cr(r.shape)},o}(t);for(var n in r)typeof r[n]=="function"&&(i.prototype[n]=r[n]);return i},t.initDefaultProps=function(){var r=t.prototype;r.type="path",r.strokeContainThreshold=5,r.segmentIgnoreThreshold=0,r.subPixelOptimize=!1,r.autoBatch=!1,r.__dirty=Lt|He|Ur}(),t}(ve),wf=Ht({strokeFirst:!0,font:lr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},So),Ve=function(e){F(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var r=this.style,i=r.stroke;return i!=null&&i!=="none"&&r.lineWidth>0},t.prototype.hasFill=function(){var r=this.style,i=r.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(r){return le(wf,r)},t.prototype.setBoundingRect=function(r){this._rect=r},t.prototype.getBoundingRect=function(){var r=this.style;if(!this._rect){var i=r.text;i!=null?i+="":i="";var n=Mh(i,r.font,r.textAlign,r.textBaseline);if(n.x+=r.x||0,n.y+=r.y||0,this.hasStroke()){var a=r.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var r=t.prototype;r.dirtyRectTolerance=10}(),t}(ve);Ve.prototype.type="tspan";var Tf=Ht({x:0,y:0},ur),bf={style:Ht({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ii.style)};function Cf(e){return!!(e&&typeof e!="string"&&e.width&&e.height)}var Sn=function(e){F(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.createStyle=function(r){return le(Tf,r)},t.prototype._getSize=function(r){var i=this.style,n=i[r];if(n!=null)return n;var a=Cf(i.image)?i.image:this.__image;if(!a)return 0;var o=r==="width"?"height":"width",s=i[o];return s==null?a[r]:a[r]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return bf},t.prototype.getBoundingRect=function(){var r=this.style;return this._rect||(this._rect=new N(r.x||0,r.y||0,this.getWidth(),this.getHeight())),this._rect},t}(ve);Sn.prototype.type="image";function Sf(e,t){var r=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,h,f,u;n<0&&(r=r+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=h=f=u=o:o instanceof Array?o.length===1?s=h=f=u=o[0]:o.length===2?(s=f=o[0],h=u=o[1]):o.length===3?(s=o[0],h=u=o[1],f=o[2]):(s=o[0],h=o[1],f=o[2],u=o[3]):s=h=f=u=0;var l;s+h>n&&(l=s+h,s*=n/l,h*=n/l),f+u>n&&(l=f+u,f*=n/l,u*=n/l),h+f>a&&(l=h+f,h*=a/l,f*=a/l),s+u>a&&(l=s+u,s*=a/l,u*=a/l),e.moveTo(r+s,i),e.lineTo(r+n-h,i),h!==0&&e.arc(r+n-h,i+h,h,-Math.PI/2,0),e.lineTo(r+n,i+a-f),f!==0&&e.arc(r+n-f,i+a-f,f,0,Math.PI/2),e.lineTo(r+u,i+a),u!==0&&e.arc(r+u,i+a-u,u,Math.PI/2,Math.PI),e.lineTo(r,i+s),s!==0&&e.arc(r+s,i+s,s,Math.PI,Math.PI*1.5)}var Rr=Math.round;function Lf(e,t,r){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;e.x1=i,e.x2=n,e.y1=a,e.y2=o;var s=r&&r.lineWidth;return s&&(Rr(i*2)===Rr(n*2)&&(e.x1=e.x2=xr(i,s,!0)),Rr(a*2)===Rr(o*2)&&(e.y1=e.y2=xr(a,s,!0))),e}}function Pf(e,t,r){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;e.x=i,e.y=n,e.width=a,e.height=o;var s=r&&r.lineWidth;return s&&(e.x=xr(i,s,!0),e.y=xr(n,s,!0),e.width=Math.max(xr(i+a,s,!1)-e.x,a===0?0:1),e.height=Math.max(xr(n+o,s,!1)-e.y,o===0?0:1)),e}}function xr(e,t,r){if(!t)return e;var i=Rr(e*2);return(i+Rr(t))%2===0?i/2:(i+(r?1:-1))/2}var Mf=function(){function e(){this.x=0,this.y=0,this.width=0,this.height=0}return e}(),Rf={},Lo=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Mf},t.prototype.buildPath=function(r,i){var n,a,o,s;if(this.subPixelOptimize){var h=Pf(Rf,i,this.style);n=h.x,a=h.y,o=h.width,s=h.height,h.r=i.r,i=h}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?Sf(r,i):r.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}($);Lo.prototype.type="rect";var ea={fill:"#000"},ia=2,xf={style:Ht({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ii.style)},Df=function(e){F(t,e);function t(r){var i=e.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=ea,i.attr(r),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var r=0;r<this._children.length;r++){var i=this._children[r];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var r=this.innerTransformable;r?(r.updateTransform(),r.transform&&(this.transform=r.transform)):e.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(r){var i=this.innerTransformable;return i?i.getLocalTransform(r):e.prototype.getLocalTransform.call(this,r)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,Ff(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=r},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var r=new N(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],h=s.getBoundingRect(),f=s.getLocalTransform(n);f?(r.copy(h),r.applyTransform(f),a=a||r.clone(),a.union(r)):(a=a||h.clone(),a.union(h))}this._rect=a||r}return this._rect},t.prototype.setDefaultTextStyle=function(r){this._defaultStyle=r||ea},t.prototype.setTextContent=function(r){},t.prototype._mergeStyle=function(r,i){if(!i)return r;var n=i.rich,a=r.rich||n&&{};return H(r,i),n&&a?(this._mergeRich(a,n),r.rich=a):a&&(r.rich=a),r},t.prototype._mergeRich=function(r,i){for(var n=tt(i),a=0;a<n.length;a++){var o=n[a];r[o]=r[o]||{},H(r[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return xf},t.prototype._getOrCreateChild=function(r){var i=this._children[this._childCursor];return(!i||!(i instanceof r))&&(i=new r),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var r=this.style,i=r.font||lr,n=r.padding,a=ua(r),o=Uh(a,r),s=Fi(r),h=!!r.backgroundColor,f=o.outerHeight,u=o.outerWidth,l=o.contentWidth,v=o.lines,c=o.lineHeight,_=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=r.x||0,d=r.y||0,p=r.align||_.align||"left",g=r.verticalAlign||_.verticalAlign||"top",m=y,w=Lr(d,o.contentHeight,g);if(s||n){var S=Qr(y,u,p),T=Lr(d,f,g);s&&this._renderBackground(r,r,S,T,u,f)}w+=c/2,n&&(m=fa(y,p,n),g==="top"?w+=n[0]:g==="bottom"&&(w-=n[2]));for(var b=0,C=!1,L=ha("fill"in r?r.fill:(C=!0,_.fill)),M=sa("stroke"in r?r.stroke:!h&&(!_.autoStroke||C)?(b=ia,_.stroke):null),P=r.textShadowBlur>0,R=r.width!=null&&(r.overflow==="truncate"||r.overflow==="break"||r.overflow==="breakAll"),O=o.calculatedLineHeight,x=0;x<v.length;x++){var B=this._getOrCreateChild(Ve),D=B.createStyle();B.useStyle(D),D.text=v[x],D.x=m,D.y=w,D.textAlign=p,D.textBaseline="middle",D.opacity=r.opacity,D.strokeFirst=!0,P&&(D.shadowBlur=r.textShadowBlur||0,D.shadowColor=r.textShadowColor||"transparent",D.shadowOffsetX=r.textShadowOffsetX||0,D.shadowOffsetY=r.textShadowOffsetY||0),D.stroke=M,D.fill=L,M&&(D.lineWidth=r.lineWidth||b,D.lineDash=r.lineDash,D.lineDashOffset=r.lineDashOffset||0),D.font=i,aa(D,r),w+=c,R&&B.setBoundingRect(new N(Qr(D.x,l,D.textAlign),Lr(D.y,O,D.textBaseline),l,O))}},t.prototype._updateRichTexts=function(){var r=this.style,i=ua(r),n=Kh(i,r),a=n.width,o=n.outerWidth,s=n.outerHeight,h=r.padding,f=r.x||0,u=r.y||0,l=this._defaultStyle,v=r.align||l.align,c=r.verticalAlign||l.verticalAlign;this.isTruncated=!!n.isTruncated;var _=Qr(f,o,v),y=Lr(u,s,c),d=_,p=y;h&&(d+=h[3],p+=h[0]);var g=d+a;Fi(r)&&this._renderBackground(r,r,_,y,o,s);for(var m=!!r.backgroundColor,w=0;w<n.lines.length;w++){for(var S=n.lines[w],T=S.tokens,b=T.length,C=S.lineHeight,L=S.width,M=0,P=d,R=g,O=b-1,x=void 0;M<b&&(x=T[M],!x.align||x.align==="left");)this._placeToken(x,r,C,p,P,"left",m),L-=x.width,P+=x.width,M++;for(;O>=0&&(x=T[O],x.align==="right");)this._placeToken(x,r,C,p,R,"right",m),L-=x.width,R-=x.width,O--;for(P+=(a-(P-d)-(g-R)-L)/2;M<=O;)x=T[M],this._placeToken(x,r,C,p,P+x.width/2,"center",m),P+=x.width,M++;p+=C}},t.prototype._placeToken=function(r,i,n,a,o,s,h){var f=i.rich[r.styleName]||{};f.text=r.text;var u=r.verticalAlign,l=a+n/2;u==="top"?l=a+r.height/2:u==="bottom"&&(l=a+n-r.height/2);var v=!r.isLineHolder&&Fi(f);v&&this._renderBackground(f,i,s==="right"?o-r.width:s==="center"?o-r.width/2:o,l-r.height/2,r.width,r.height);var c=!!f.backgroundColor,_=r.textPadding;_&&(o=fa(o,s,_),l-=r.height/2-_[0]-r.innerHeight/2);var y=this._getOrCreateChild(Ve),d=y.createStyle();y.useStyle(d);var p=this._defaultStyle,g=!1,m=0,w=ha("fill"in f?f.fill:"fill"in i?i.fill:(g=!0,p.fill)),S=sa("stroke"in f?f.stroke:"stroke"in i?i.stroke:!c&&!h&&(!p.autoStroke||g)?(m=ia,p.stroke):null),T=f.textShadowBlur>0||i.textShadowBlur>0;d.text=r.text,d.x=o,d.y=l,T&&(d.shadowBlur=f.textShadowBlur||i.textShadowBlur||0,d.shadowColor=f.textShadowColor||i.textShadowColor||"transparent",d.shadowOffsetX=f.textShadowOffsetX||i.textShadowOffsetX||0,d.shadowOffsetY=f.textShadowOffsetY||i.textShadowOffsetY||0),d.textAlign=s,d.textBaseline="middle",d.font=r.font||lr,d.opacity=jr(f.opacity,i.opacity,1),aa(d,f),S&&(d.lineWidth=jr(f.lineWidth,i.lineWidth,m),d.lineDash=J(f.lineDash,i.lineDash),d.lineDashOffset=i.lineDashOffset||0,d.stroke=S),w&&(d.fill=w);var b=r.contentWidth,C=r.contentHeight;y.setBoundingRect(new N(Qr(d.x,b,d.textAlign),Lr(d.y,C,d.textBaseline),b,C))},t.prototype._renderBackground=function(r,i,n,a,o,s){var h=r.backgroundColor,f=r.borderWidth,u=r.borderColor,l=h&&h.image,v=h&&!l,c=r.borderRadius,_=this,y,d;if(v||r.lineHeight||f&&u){y=this._getOrCreateChild(Lo),y.useStyle(y.createStyle()),y.style.fill=null;var p=y.shape;p.x=n,p.y=a,p.width=o,p.height=s,p.r=c,y.dirtyShape()}if(v){var g=y.style;g.fill=h||null,g.fillOpacity=J(r.fillOpacity,1)}else if(l){d=this._getOrCreateChild(Sn),d.onload=function(){_.dirtyStyle()};var m=d.style;m.image=h.image,m.x=n,m.y=a,m.width=o,m.height=s}if(f&&u){var g=y.style;g.lineWidth=f,g.stroke=u,g.strokeOpacity=J(r.strokeOpacity,1),g.lineDash=r.borderDash,g.lineDashOffset=r.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(g.strokeFirst=!0,g.lineWidth*=2)}var w=(y||d).style;w.shadowBlur=r.shadowBlur||0,w.shadowColor=r.shadowColor||"transparent",w.shadowOffsetX=r.shadowOffsetX||0,w.shadowOffsetY=r.shadowOffsetY||0,w.opacity=jr(r.opacity,i.opacity,1)},t.makeFont=function(r){var i="";return Of(r)&&(i=[r.fontStyle,r.fontWeight,If(r.fontSize),r.fontFamily||"sans-serif"].join(" ")),i&&Pr(i)||r.textFont||r.font},t}(ve),Af={left:!0,right:1,center:1},Ef={top:1,bottom:1,middle:1},na=["fontStyle","fontWeight","fontSize","fontFamily"];function If(e){return typeof e=="string"&&(e.indexOf("px")!==-1||e.indexOf("rem")!==-1||e.indexOf("em")!==-1)?e:isNaN(+e)?ln+"px":e+"px"}function aa(e,t){for(var r=0;r<na.length;r++){var i=na[r],n=t[i];n!=null&&(e[i]=n)}}function Of(e){return e.fontSize!=null||e.fontFamily||e.fontWeight}function Ff(e){return oa(e),j(e.rich,oa),e}function oa(e){if(e){e.font=Df.makeFont(e);var t=e.align;t==="middle"&&(t="center"),e.align=t==null||Af[t]?t:"left";var r=e.verticalAlign;r==="center"&&(r="middle"),e.verticalAlign=r==null||Ef[r]?r:"top";var i=e.padding;i&&(e.padding=Fa(e.padding))}}function sa(e,t){return e==null||t<=0||e==="transparent"||e==="none"?null:e.image||e.colorStops?"#000":e}function ha(e){return e==null||e==="none"?null:e.image||e.colorStops?"#000":e}function fa(e,t,r){return t==="right"?e-r[1]:t==="center"?e+r[3]/2-r[1]/2:e+r[3]}function ua(e){var t=e.text;return t!=null&&(t+=""),t}function Fi(e){return!!(e.backgroundColor||e.lineHeight||e.borderWidth&&e.borderColor)}var Cr=Or.CMD,Bf=[[],[],[]],la=Math.sqrt,Hf=Math.atan2;function zf(e,t){if(t){var r=e.data,i=e.len(),n,a,o,s,h,f,u=Cr.M,l=Cr.C,v=Cr.L,c=Cr.R,_=Cr.A,y=Cr.Q;for(o=0,s=0;o<i;){switch(n=r[o++],s=o,a=0,n){case u:a=1;break;case v:a=1;break;case l:a=3;break;case y:a=2;break;case _:var d=t[4],p=t[5],g=la(t[0]*t[0]+t[1]*t[1]),m=la(t[2]*t[2]+t[3]*t[3]),w=Hf(-t[1]/m,t[0]/g);r[o]*=g,r[o++]+=d,r[o]*=m,r[o++]+=p,r[o++]*=g,r[o++]*=m,r[o++]+=w,r[o++]+=w,o+=2,s=o;break;case c:f[0]=r[o++],f[1]=r[o++],Ar(f,f,t),r[s++]=f[0],r[s++]=f[1],f[0]+=r[o++],f[1]+=r[o++],Ar(f,f,t),r[s++]=f[0],r[s++]=f[1]}for(h=0;h<a;h++){var S=Bf[h];S[0]=r[o++],S[1]=r[o++],Ar(S,S,t),r[s++]=S[0],r[s++]=S[1]}}e.increaseVersion()}}var Bi=Math.sqrt,Me=Math.sin,Re=Math.cos,Zr=Math.PI;function va(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])}function hn(e,t){return(e[0]*t[0]+e[1]*t[1])/(va(e)*va(t))}function ca(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(hn(e,t))}function da(e,t,r,i,n,a,o,s,h,f,u){var l=h*(Zr/180),v=Re(l)*(e-r)/2+Me(l)*(t-i)/2,c=-1*Me(l)*(e-r)/2+Re(l)*(t-i)/2,_=v*v/(o*o)+c*c/(s*s);_>1&&(o*=Bi(_),s*=Bi(_));var y=(n===a?-1:1)*Bi((o*o*(s*s)-o*o*(c*c)-s*s*(v*v))/(o*o*(c*c)+s*s*(v*v)))||0,d=y*o*c/s,p=y*-s*v/o,g=(e+r)/2+Re(l)*d-Me(l)*p,m=(t+i)/2+Me(l)*d+Re(l)*p,w=ca([1,0],[(v-d)/o,(c-p)/s]),S=[(v-d)/o,(c-p)/s],T=[(-1*v-d)/o,(-1*c-p)/s],b=ca(S,T);if(hn(S,T)<=-1&&(b=Zr),hn(S,T)>=1&&(b=0),b<0){var C=Math.round(b/Zr*1e6)/1e6;b=Zr*2+C%2*Zr}u.addData(f,g,m,o,s,w,b,l,a)}var kf=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,Nf=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Wf(e){var t=new Or;if(!e)return t;var r=0,i=0,n=r,a=i,o,s=Or.CMD,h=e.match(kf);if(!h)return t;for(var f=0;f<h.length;f++){for(var u=h[f],l=u.charAt(0),v=void 0,c=u.match(Nf)||[],_=c.length,y=0;y<_;y++)c[y]=parseFloat(c[y]);for(var d=0;d<_;){var p=void 0,g=void 0,m=void 0,w=void 0,S=void 0,T=void 0,b=void 0,C=r,L=i,M=void 0,P=void 0;switch(l){case"l":r+=c[d++],i+=c[d++],v=s.L,t.addData(v,r,i);break;case"L":r=c[d++],i=c[d++],v=s.L,t.addData(v,r,i);break;case"m":r+=c[d++],i+=c[d++],v=s.M,t.addData(v,r,i),n=r,a=i,l="l";break;case"M":r=c[d++],i=c[d++],v=s.M,t.addData(v,r,i),n=r,a=i,l="L";break;case"h":r+=c[d++],v=s.L,t.addData(v,r,i);break;case"H":r=c[d++],v=s.L,t.addData(v,r,i);break;case"v":i+=c[d++],v=s.L,t.addData(v,r,i);break;case"V":i=c[d++],v=s.L,t.addData(v,r,i);break;case"C":v=s.C,t.addData(v,c[d++],c[d++],c[d++],c[d++],c[d++],c[d++]),r=c[d-2],i=c[d-1];break;case"c":v=s.C,t.addData(v,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i,c[d++]+r,c[d++]+i),r+=c[d-2],i+=c[d-1];break;case"S":p=r,g=i,M=t.len(),P=t.data,o===s.C&&(p+=r-P[M-4],g+=i-P[M-3]),v=s.C,C=c[d++],L=c[d++],r=c[d++],i=c[d++],t.addData(v,p,g,C,L,r,i);break;case"s":p=r,g=i,M=t.len(),P=t.data,o===s.C&&(p+=r-P[M-4],g+=i-P[M-3]),v=s.C,C=r+c[d++],L=i+c[d++],r+=c[d++],i+=c[d++],t.addData(v,p,g,C,L,r,i);break;case"Q":C=c[d++],L=c[d++],r=c[d++],i=c[d++],v=s.Q,t.addData(v,C,L,r,i);break;case"q":C=c[d++]+r,L=c[d++]+i,r+=c[d++],i+=c[d++],v=s.Q,t.addData(v,C,L,r,i);break;case"T":p=r,g=i,M=t.len(),P=t.data,o===s.Q&&(p+=r-P[M-4],g+=i-P[M-3]),r=c[d++],i=c[d++],v=s.Q,t.addData(v,p,g,r,i);break;case"t":p=r,g=i,M=t.len(),P=t.data,o===s.Q&&(p+=r-P[M-4],g+=i-P[M-3]),r+=c[d++],i+=c[d++],v=s.Q,t.addData(v,p,g,r,i);break;case"A":m=c[d++],w=c[d++],S=c[d++],T=c[d++],b=c[d++],C=r,L=i,r=c[d++],i=c[d++],v=s.A,da(C,L,r,i,T,b,m,w,S,v,t);break;case"a":m=c[d++],w=c[d++],S=c[d++],T=c[d++],b=c[d++],C=r,L=i,r+=c[d++],i+=c[d++],v=s.A,da(C,L,r,i,T,b,m,w,S,v,t);break}}(l==="z"||l==="Z")&&(v=s.Z,t.addData(v),r=n,i=a),o=v}return t.toStatic(),t}var Po=function(e){F(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.applyTransform=function(r){},t}($);function Mo(e){return e.setData!=null}function Ro(e,t){var r=Wf(e),i=H({},t);return i.buildPath=function(n){if(Mo(n)){n.setData(r.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;r.rebuildPath(a,1)}},i.applyTransform=function(n){zf(r,n),this.dirtyShape()},i}function Gu(e,t){return new Po(Ro(e,t))}function Xu(e,t){var r=Ro(e,t),i=function(n){F(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=r.applyTransform,s.buildPath=r.buildPath,s}return a}(Po);return i}function qu(e,t){for(var r=[],i=e.length,n=0;n<i;n++){var a=e[n];r.push(a.getUpdatedPathProxy(!0))}var o=new $(t);return o.createPathProxy(),o.buildPath=function(s){if(Mo(s)){s.appendPath(r);var h=s.getContext();h&&s.rebuildPath(h,1)}},o}var Yf=function(){function e(){this.cx=0,this.cy=0,this.r=0}return e}(),Gf=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Yf},t.prototype.buildPath=function(r,i){r.moveTo(i.cx+i.r,i.cy),r.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}($);Gf.prototype.type="circle";var Xf=function(){function e(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return e}(),qf=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Xf},t.prototype.buildPath=function(r,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,h=i.ry,f=s*n,u=h*n;r.moveTo(a-s,o),r.bezierCurveTo(a-s,o-u,a-f,o-h,a,o-h),r.bezierCurveTo(a+f,o-h,a+s,o-u,a+s,o),r.bezierCurveTo(a+s,o+u,a+f,o+h,a,o+h),r.bezierCurveTo(a-f,o+h,a-s,o+u,a-s,o),r.closePath()},t}($);qf.prototype.type="ellipse";var xo=Math.PI,Hi=xo*2,Qt=Math.sin,Sr=Math.cos,$f=Math.acos,X=Math.atan2,pa=Math.abs,re=Math.sqrt,Kr=Math.max,Ct=Math.min,ut=1e-4;function Zf(e,t,r,i,n,a,o,s){var h=r-e,f=i-t,u=o-n,l=s-a,v=l*h-u*f;if(!(v*v<ut))return v=(u*(t-a)-l*(e-n))/v,[e+v*h,t+v*f]}function xe(e,t,r,i,n,a,o){var s=e-r,h=t-i,f=(o?a:-a)/re(s*s+h*h),u=f*h,l=-f*s,v=e+u,c=t+l,_=r+u,y=i+l,d=(v+_)/2,p=(c+y)/2,g=_-v,m=y-c,w=g*g+m*m,S=n-a,T=v*y-_*c,b=(m<0?-1:1)*re(Kr(0,S*S*w-T*T)),C=(T*m-g*b)/w,L=(-T*g-m*b)/w,M=(T*m+g*b)/w,P=(-T*g+m*b)/w,R=C-d,O=L-p,x=M-d,B=P-p;return R*R+O*O>x*x+B*B&&(C=M,L=P),{cx:C,cy:L,x0:-u,y0:-l,x1:C*(n/S-1),y1:L*(n/S-1)}}function Uf(e){var t;if(Ir(e)){var r=e.length;if(!r)return e;r===1?t=[e[0],e[0],0,0]:r===2?t=[e[0],e[0],e[1],e[1]]:r===3?t=e.concat(e[2]):t=e}else t=[e,e,e,e];return t}function Vf(e,t){var r,i=Kr(t.r,0),n=Kr(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var h=t.startAngle,f=t.endAngle;if(!(isNaN(h)||isNaN(f))){var u=t.cx,l=t.cy,v=!!t.clockwise,c=pa(f-h),_=c>Hi&&c%Hi;if(_>ut&&(c=_),!(i>ut))e.moveTo(u,l);else if(c>Hi-ut)e.moveTo(u+i*Sr(h),l+i*Qt(h)),e.arc(u,l,i,h,f,!v),n>ut&&(e.moveTo(u+n*Sr(f),l+n*Qt(f)),e.arc(u,l,n,f,h,v));else{var y=void 0,d=void 0,p=void 0,g=void 0,m=void 0,w=void 0,S=void 0,T=void 0,b=void 0,C=void 0,L=void 0,M=void 0,P=void 0,R=void 0,O=void 0,x=void 0,B=i*Sr(h),D=i*Qt(h),W=n*Sr(f),V=n*Qt(f),Pt=c>ut;if(Pt){var _t=t.cornerRadius;_t&&(r=Uf(_t),y=r[0],d=r[1],p=r[2],g=r[3]);var et=pa(i-n)/2;if(m=Ct(et,p),w=Ct(et,g),S=Ct(et,y),T=Ct(et,d),L=b=Kr(m,w),M=C=Kr(S,T),(b>ut||C>ut)&&(P=i*Sr(f),R=i*Qt(f),O=n*Sr(h),x=n*Qt(h),c<xo)){var Y=Zf(B,D,O,x,P,R,W,V);if(Y){var gt=B-Y[0],yt=D-Y[1],gr=P-Y[0],Hr=R-Y[1],zr=1/Qt($f((gt*gr+yt*Hr)/(re(gt*gt+yt*yt)*re(gr*gr+Hr*Hr)))/2),ce=re(Y[0]*Y[0]+Y[1]*Y[1]);L=Ct(b,(i-ce)/(zr+1)),M=Ct(C,(n-ce)/(zr-1))}}}if(!Pt)e.moveTo(u+B,l+D);else if(L>ut){var ft=Ct(p,L),Z=Ct(g,L),A=xe(O,x,B,D,i,ft,v),E=xe(P,R,W,V,i,Z,v);e.moveTo(u+A.cx+A.x0,l+A.cy+A.y0),L<b&&ft===Z?e.arc(u+A.cx,l+A.cy,L,X(A.y0,A.x0),X(E.y0,E.x0),!v):(ft>0&&e.arc(u+A.cx,l+A.cy,ft,X(A.y0,A.x0),X(A.y1,A.x1),!v),e.arc(u,l,i,X(A.cy+A.y1,A.cx+A.x1),X(E.cy+E.y1,E.cx+E.x1),!v),Z>0&&e.arc(u+E.cx,l+E.cy,Z,X(E.y1,E.x1),X(E.y0,E.x0),!v))}else e.moveTo(u+B,l+D),e.arc(u,l,i,h,f,!v);if(!(n>ut)||!Pt)e.lineTo(u+W,l+V);else if(M>ut){var ft=Ct(y,M),Z=Ct(d,M),A=xe(W,V,P,R,n,-Z,v),E=xe(B,D,O,x,n,-ft,v);e.lineTo(u+A.cx+A.x0,l+A.cy+A.y0),M<C&&ft===Z?e.arc(u+A.cx,l+A.cy,M,X(A.y0,A.x0),X(E.y0,E.x0),!v):(Z>0&&e.arc(u+A.cx,l+A.cy,Z,X(A.y0,A.x0),X(A.y1,A.x1),!v),e.arc(u,l,n,X(A.cy+A.y1,A.cx+A.x1),X(E.cy+E.y1,E.cx+E.x1),v),ft>0&&e.arc(u+E.cx,l+E.cy,ft,X(E.y1,E.x1),X(E.y0,E.x0),!v))}else e.lineTo(u+W,l+V),e.arc(u,l,n,f,h,v)}e.closePath()}}}var Qf=function(){function e(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return e}(),Kf=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Qf},t.prototype.buildPath=function(r,i){Vf(r,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}($);Kf.prototype.type="sector";var Jf=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return e}(),jf=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Jf},t.prototype.buildPath=function(r,i){var n=i.cx,a=i.cy,o=Math.PI*2;r.moveTo(n+i.r,a),r.arc(n,a,i.r,0,o,!1),r.moveTo(n+i.r0,a),r.arc(n,a,i.r0,0,o,!0)},t}($);jf.prototype.type="ring";function tu(e,t,r,i){var n=[],a=[],o=[],s=[],h,f,u,l;if(i){u=[1/0,1/0],l=[-1/0,-1/0];for(var v=0,c=e.length;v<c;v++)rr(u,u,e[v]),er(l,l,e[v]);rr(u,u,i[0]),er(l,l,i[1])}for(var v=0,c=e.length;v<c;v++){var _=e[v];if(r)h=e[v?v-1:c-1],f=e[(v+1)%c];else if(v===0||v===c-1){n.push(Na(e[v]));continue}else h=e[v-1],f=e[v+1];Wa(a,f,h),Fe(a,a,t);var y=Xe(_,h),d=Xe(_,f),p=y+d;p!==0&&(y/=p,d/=p),Fe(o,a,-y),Fe(s,a,d);var g=Wi([],_,o),m=Wi([],_,s);i&&(er(g,g,u),rr(g,g,l),er(m,m,u),rr(m,m,l)),n.push(g),n.push(m)}return r&&n.push(n.shift()),n}function Do(e,t,r){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=tu(n,i,r,t.smoothConstraint);e.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(r?o:o-1);s++){var h=a[s*2],f=a[s*2+1],u=n[(s+1)%o];e.bezierCurveTo(h[0],h[1],f[0],f[1],u[0],u[1])}}else{e.moveTo(n[0][0],n[0][1]);for(var s=1,l=n.length;s<l;s++)e.lineTo(n[s][0],n[s][1])}r&&e.closePath()}}var ru=function(){function e(){this.points=null,this.smooth=0,this.smoothConstraint=null}return e}(),eu=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new ru},t.prototype.buildPath=function(r,i){Do(r,i,!0)},t}($);eu.prototype.type="polygon";var iu=function(){function e(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return e}(),nu=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new iu},t.prototype.buildPath=function(r,i){Do(r,i,!1)},t}($);nu.prototype.type="polyline";var au={},ou=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return e}(),su=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new ou},t.prototype.buildPath=function(r,i){var n,a,o,s;if(this.subPixelOptimize){var h=Lf(au,i,this.style);n=h.x1,a=h.y1,o=h.x2,s=h.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var f=i.percent;f!==0&&(r.moveTo(n,a),f<1&&(o=n*(1-f)+o*f,s=a*(1-f)+s*f),r.lineTo(o,s))},t.prototype.pointAt=function(r){var i=this.shape;return[i.x1*(1-r)+i.x2*r,i.y1*(1-r)+i.y2*r]},t}($);su.prototype.type="line";var Q=[],hu=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return e}();function _a(e,t,r){var i=e.cpx2,n=e.cpy2;return i!=null||n!=null?[(r?Bn:G)(e.x1,e.cpx1,e.cpx2,e.x2,t),(r?Bn:G)(e.y1,e.cpy1,e.cpy2,e.y2,t)]:[(r?Hn:q)(e.x1,e.cpx1,e.x2,t),(r?Hn:q)(e.y1,e.cpy1,e.y2,t)]}var fu=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new hu},t.prototype.buildPath=function(r,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,h=i.cpx1,f=i.cpy1,u=i.cpx2,l=i.cpy2,v=i.percent;v!==0&&(r.moveTo(n,a),u==null||l==null?(v<1&&(Ze(n,h,o,v,Q),h=Q[1],o=Q[2],Ze(a,f,s,v,Q),f=Q[1],s=Q[2]),r.quadraticCurveTo(h,f,o,s)):(v<1&&($e(n,h,u,o,v,Q),h=Q[1],u=Q[2],o=Q[3],$e(a,f,l,s,v,Q),f=Q[1],l=Q[2],s=Q[3]),r.bezierCurveTo(h,f,u,l,o,s)))},t.prototype.pointAt=function(r){return _a(this.shape,r,!1)},t.prototype.tangentAt=function(r){var i=_a(this.shape,r,!0);return Ya(i,i)},t}($);fu.prototype.type="bezier-curve";var uu=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return e}(),lu=function(e){F(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new uu},t.prototype.buildPath=function(r,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,h=i.endAngle,f=i.clockwise,u=Math.cos(s),l=Math.sin(s);r.moveTo(u*o+n,l*o+a),r.arc(n,a,o,s,h,!f)},t}($);lu.prototype.type="arc";var $u=function(e){F(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.type="compound",r}return t.prototype._updatePathDirty=function(){for(var r=this.shape.paths,i=this.shapeChanged(),n=0;n<r.length;n++)i=i||r[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var r=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<r.length;n++)r[n].path||r[n].createPathProxy(),r[n].path.setScale(i[0],i[1],r[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(r,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(r,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var r=this.shape.paths||[],i=0;i<r.length;i++)r[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),$.prototype.getBoundingRect.call(this)},t}($),Ao=function(){function e(t){this.colorStops=t||[]}return e.prototype.addColorStop=function(t,r){this.colorStops.push({offset:t,color:r})},e}(),Zu=function(e){F(t,e);function t(r,i,n,a,o,s){var h=e.call(this,o)||this;return h.x=r??0,h.y=i??0,h.x2=n??1,h.y2=a??0,h.type="linear",h.global=s||!1,h}return t}(Ao),Uu=function(e){F(t,e);function t(r,i,n,a,o){var s=e.call(this,a)||this;return s.x=r??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(Ao),Kt=[0,0],Jt=[0,0],De=new z,Ae=new z,Vu=function(){function e(t,r){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new z;for(var i=0;i<2;i++)this._axes[i]=new z;t&&this.fromBoundingRect(t,r)}return e.prototype.fromBoundingRect=function(t,r){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,h=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,h),i[3].set(a,h),r)for(var f=0;f<4;f++)i[f].transform(r);z.sub(n[0],i[1],i[0]),z.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var f=0;f<2;f++)this._origin[f]=n[f].dot(i[0])},e.prototype.intersect=function(t,r){var i=!0,n=!r;return De.set(1/0,1/0),Ae.set(0,0),!this._intersectCheckOneSide(this,t,De,Ae,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,De,Ae,n,-1)&&(i=!1,n)||n||z.copy(r,i?De:Ae),i},e.prototype._intersectCheckOneSide=function(t,r,i,n,a,o){for(var s=!0,h=0;h<2;h++){var f=this._axes[h];if(this._getProjMinMaxOnAxis(h,t._corners,Kt),this._getProjMinMaxOnAxis(h,r._corners,Jt),Kt[1]<Jt[0]||Kt[0]>Jt[1]){if(s=!1,a)return s;var u=Math.abs(Jt[0]-Kt[1]),l=Math.abs(Kt[0]-Jt[1]);Math.min(u,l)>n.len()&&(u<l?z.scale(n,f,-u*o):z.scale(n,f,l*o))}else if(i){var u=Math.abs(Jt[0]-Kt[1]),l=Math.abs(Kt[0]-Jt[1]);Math.min(u,l)<i.len()&&(u<l?z.scale(i,f,u*o):z.scale(i,f,-l*o))}}return s},e.prototype._getProjMinMaxOnAxis=function(t,r,i){for(var n=this._axes[t],a=this._origin,o=r[0].dot(n)+a[t],s=o,h=o,f=1;f<r.length;f++){var u=r[f].dot(n)+a[t];s=Math.min(u,s),h=Math.max(u,h)}i[0]=s,i[1]=h},e}(),vu=[],Qu=function(e){F(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.notClear=!0,r.incremental=!0,r._displayables=[],r._temporaryDisplayables=[],r._cursor=0,r}return t.prototype.traverse=function(r,i){r.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(r,i){i?this._temporaryDisplayables.push(r):this._displayables.push(r),this.markRedraw()},t.prototype.addDisplayables=function(r,i){i=i||!1;for(var n=0;n<r.length;n++)this.addDisplayable(r[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(r){for(var i=this._cursor;i<this._displayables.length;i++)r&&r(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)r&&r(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var r=this._cursor;r<this._displayables.length;r++){var i=this._displayables[r];i.parent=this,i.update(),i.parent=null}for(var r=0;r<this._temporaryDisplayables.length;r++){var i=this._temporaryDisplayables[r];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var r=new N(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(vu)),r.union(a)}this._rect=r}return this._rect},t.prototype.contain=function(r,i){var n=this.transformCoordToLocal(r,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(r,i))return!0}return!1},t}(ve),cu=Math.round(Math.random()*9),du=typeof Object.defineProperty=="function",Ku=function(){function e(){this._id="__ec_inner_"+cu++}return e.prototype.get=function(t){return this._guard(t)[this._id]},e.prototype.set=function(t,r){var i=this._guard(t);return du?Object.defineProperty(i,this._id,{value:r,enumerable:!1,configurable:!0}):i[this._id]=r,this},e.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},e.prototype.has=function(t){return!!this._guard(t)[this._id]},e.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},e}();function nr(e){return isFinite(e)}function pu(e,t,r){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*r.width+r.x,n=n*r.width+r.x,a=a*r.height+r.y,o=o*r.height+r.y),i=nr(i)?i:0,n=nr(n)?n:1,a=nr(a)?a:0,o=nr(o)?o:0;var s=e.createLinearGradient(i,a,n,o);return s}function _u(e,t,r){var i=r.width,n=r.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,h=t.r==null?.5:t.r;t.global||(o=o*i+r.x,s=s*n+r.y,h=h*a),o=nr(o)?o:.5,s=nr(s)?s:.5,h=h>=0&&nr(h)?h:.5;var f=e.createRadialGradient(o,s,0,o,s,h);return f}function fn(e,t,r){for(var i=t.type==="radial"?_u(e,t,r):pu(e,t,r),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function gu(e,t){if(e===t||!e&&!t)return!1;if(!e||!t||e.length!==t.length)return!0;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!0;return!1}function Ee(e){return parseInt(e,10)}function Ie(e,t,r){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(r[i]!=null&&r[i]!=="auto")return parseFloat(r[i]);var s=document.defaultView.getComputedStyle(e);return(e[n]||Ee(s[i])||Ee(e.style[i]))-(Ee(s[a])||0)-(Ee(s[o])||0)|0}function yu(e,t){return!e||e==="solid"||!(t>0)?null:e==="dashed"?[4*t,2*t]:e==="dotted"?[t]:Jr(e)?[e]:Ir(e)?e:null}function Eo(e){var t=e.style,r=t.lineDash&&t.lineWidth>0&&yu(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(r){var n=t.strokeNoScale&&e.getLineScale?e.getLineScale():1;n&&n!==1&&(r=dr(r,function(a){return a/n}),i/=n)}return[r,i]}var mu=new Or(!0);function Qe(e){var t=e.stroke;return!(t==null||t==="none"||!(e.lineWidth>0))}function ga(e){return typeof e=="string"&&e!=="none"}function Ke(e){var t=e.fill;return t!=null&&t!=="none"}function ya(e,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.fillOpacity*t.opacity,e.fill(),e.globalAlpha=r}else e.fill()}function ma(e,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.strokeOpacity*t.opacity,e.stroke(),e.globalAlpha=r}else e.stroke()}function un(e,t,r){var i=yo(t.image,t.__image,r);if(ei(i)){var n=e.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*ka),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function wu(e,t,r,i){var n,a=Qe(r),o=Ke(r),s=r.strokePercent,h=s<1,f=!t.path;(!t.silent||h)&&f&&t.createPathProxy();var u=t.path||mu,l=t.__dirty;if(!i){var v=r.fill,c=r.stroke,_=o&&!!v.colorStops,y=a&&!!c.colorStops,d=o&&!!v.image,p=a&&!!c.image,g=void 0,m=void 0,w=void 0,S=void 0,T=void 0;(_||y)&&(T=t.getBoundingRect()),_&&(g=l?fn(e,v,T):t.__canvasFillGradient,t.__canvasFillGradient=g),y&&(m=l?fn(e,c,T):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),d&&(w=l||!t.__canvasFillPattern?un(e,v,t):t.__canvasFillPattern,t.__canvasFillPattern=w),p&&(S=l||!t.__canvasStrokePattern?un(e,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),_?e.fillStyle=g:d&&(w?e.fillStyle=w:o=!1),y?e.strokeStyle=m:p&&(S?e.strokeStyle=S:a=!1)}var b=t.getGlobalScale();u.setScale(b[0],b[1],t.segmentIgnoreThreshold);var C,L;e.setLineDash&&r.lineDash&&(n=Eo(t),C=n[0],L=n[1]);var M=!0;(f||l&Ur)&&(u.setDPR(e.dpr),h?u.setContext(null):(u.setContext(e),M=!1),u.reset(),t.buildPath(u,t.shape,i),u.toStatic(),t.pathUpdated()),M&&u.rebuildPath(e,h?s:1),C&&(e.setLineDash(C),e.lineDashOffset=L),i||(r.strokeFirst?(a&&ma(e,r),o&&ya(e,r)):(o&&ya(e,r),a&&ma(e,r))),C&&e.setLineDash([])}function Tu(e,t,r){var i=t.__image=yo(r.image,t.__image,t,t.onload);if(!(!i||!ei(i))){var n=r.x||0,a=r.y||0,o=t.getWidth(),s=t.getHeight(),h=i.width/i.height;if(o==null&&s!=null?o=s*h:s==null&&o!=null?s=o/h:o==null&&s==null&&(o=i.width,s=i.height),r.sWidth&&r.sHeight){var f=r.sx||0,u=r.sy||0;e.drawImage(i,f,u,r.sWidth,r.sHeight,n,a,o,s)}else if(r.sx&&r.sy){var f=r.sx,u=r.sy,l=o-f,v=s-u;e.drawImage(i,f,u,l,v,n,a,o,s)}else e.drawImage(i,n,a,o,s)}}function bu(e,t,r){var i,n=r.text;if(n!=null&&(n+=""),n){e.font=r.font||lr,e.textAlign=r.textAlign,e.textBaseline=r.textBaseline;var a=void 0,o=void 0;e.setLineDash&&r.lineDash&&(i=Eo(t),a=i[0],o=i[1]),a&&(e.setLineDash(a),e.lineDashOffset=o),r.strokeFirst?(Qe(r)&&e.strokeText(n,r.x,r.y),Ke(r)&&e.fillText(n,r.x,r.y)):(Ke(r)&&e.fillText(n,r.x,r.y),Qe(r)&&e.strokeText(n,r.x,r.y)),a&&e.setLineDash([])}}var wa=["shadowBlur","shadowOffsetX","shadowOffsetY"],Ta=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Io(e,t,r,i,n){var a=!1;if(!i&&(r=r||{},t===r))return!1;if(i||t.opacity!==r.opacity){K(e,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);e.globalAlpha=isNaN(o)?ur.opacity:o}(i||t.blend!==r.blend)&&(a||(K(e,n),a=!0),e.globalCompositeOperation=t.blend||ur.blend);for(var s=0;s<wa.length;s++){var h=wa[s];(i||t[h]!==r[h])&&(a||(K(e,n),a=!0),e[h]=e.dpr*(t[h]||0))}return(i||t.shadowColor!==r.shadowColor)&&(a||(K(e,n),a=!0),e.shadowColor=t.shadowColor||ur.shadowColor),a}function ba(e,t,r,i,n){var a=se(t,n.inHover),o=i?null:r&&se(r,n.inHover)||{};if(a===o)return!1;var s=Io(e,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(K(e,n),s=!0),ga(a.fill)&&(e.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(K(e,n),s=!0),ga(a.stroke)&&(e.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(K(e,n),s=!0),e.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var h=a.lineWidth,f=h/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);e.lineWidth!==f&&(s||(K(e,n),s=!0),e.lineWidth=f)}for(var u=0;u<Ta.length;u++){var l=Ta[u],v=l[0];(i||a[v]!==o[v])&&(s||(K(e,n),s=!0),e[v]=a[v]||l[1])}return s}function Cu(e,t,r,i,n){return Io(e,se(t,n.inHover),r&&se(r,n.inHover),i,n)}function Oo(e,t){var r=t.transform,i=e.dpr||1;r?e.setTransform(i*r[0],i*r[1],i*r[2],i*r[3],i*r[4],i*r[5]):e.setTransform(i,0,0,i,0,0)}function Su(e,t,r){for(var i=!1,n=0;n<e.length;n++){var a=e[n];i=i||a.isZeroArea(),Oo(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}r.allClipped=i}function Lu(e,t){return e&&t?e[0]!==t[0]||e[1]!==t[1]||e[2]!==t[2]||e[3]!==t[3]||e[4]!==t[4]||e[5]!==t[5]:!(!e&&!t)}var Ca=1,Sa=2,La=3,Pa=4;function Pu(e){var t=Ke(e),r=Qe(e);return!(e.lineDash||!(+t^+r)||t&&typeof e.fill!="string"||r&&typeof e.stroke!="string"||e.strokePercent<1||e.strokeOpacity<1||e.fillOpacity<1)}function K(e,t){t.batchFill&&e.fill(),t.batchStroke&&e.stroke(),t.batchFill="",t.batchStroke=""}function se(e,t){return t&&e.__hoverStyle||e.style}function Mu(e,t){ar(e,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function ar(e,t,r,i){var n=t.transform;if(!t.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1)){t.__dirty&=-2,t.__isRendered=!1;return}var a=t.__clipPaths,o=r.prevElClipPaths,s=!1,h=!1;if((!o||gu(a,o))&&(o&&o.length&&(K(e,r),e.restore(),h=s=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),a&&a.length&&(K(e,r),e.save(),Su(a,e,r),s=!0),r.prevElClipPaths=a),r.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var f=r.prevEl;f||(h=s=!0);var u=t instanceof $&&t.autoBatch&&Pu(t.style);s||Lu(n,f.transform)?(K(e,r),Oo(e,t)):u||K(e,r);var l=se(t,r.inHover);t instanceof $?(r.lastDrawType!==Ca&&(h=!0,r.lastDrawType=Ca),ba(e,t,f,h,r),(!u||!r.batchFill&&!r.batchStroke)&&e.beginPath(),wu(e,t,l,u),u&&(r.batchFill=l.fill||"",r.batchStroke=l.stroke||"")):t instanceof Ve?(r.lastDrawType!==La&&(h=!0,r.lastDrawType=La),ba(e,t,f,h,r),bu(e,t,l)):t instanceof Sn?(r.lastDrawType!==Sa&&(h=!0,r.lastDrawType=Sa),Cu(e,t,f,h,r),Tu(e,t,l)):t.getTemporalDisplayables&&(r.lastDrawType!==Pa&&(h=!0,r.lastDrawType=Pa),Ru(e,t,r)),u&&i&&K(e,r),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),r.prevEl=t,t.__dirty=0,t.__isRendered=!0}function Ru(e,t,r){var i=t.getDisplayables(),n=t.getTemporalDisplayables();e.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var h=i[o];h.beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),ar(e,h,a,o===s-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),a.prevEl=h}for(var f=0,u=n.length;f<u;f++){var h=n[f];h.beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),ar(e,h,a,f===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),a.prevEl=h}t.clearTemporalDisplayables(),t.notClear=!0,e.restore()}var xu=1e-8;function Ma(e,t){return Math.abs(e-t)<xu}function Ju(e,t,r){var i=0,n=e[0];if(!n)return!1;for(var a=1;a<e.length;a++){var o=e[a];i+=xt(n[0],n[1],o[0],o[1],t,r),n=o}var s=e[0];return(!Ma(n[0],s[0])||!Ma(n[1],s[1]))&&(i+=xt(n[0],n[1],s[0],s[1],t,r)),i!==0}function Ra(e,t,r){var i=vr.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",e)),i.width=n*r,i.height=a*r,i}var zi=function(e){F(t,e);function t(r,i,n){var a=e.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||Ue,typeof r=="string"?o=Ra(r,i,n):Dt(r)&&(o=r,r=o.id),a.id=r,a.dom=o;var s=o.style;return s&&(gn(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var r=this.dpr;this.domBack=Ra("back-"+this.id,this.painter,r),this.ctxBack=this.domBack.getContext("2d"),r!==1&&this.ctxBack.scale(r,r)},t.prototype.createRepaintRects=function(r,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,h=!1,f=new N(0,0,0,0);function u(g){if(!(!g.isFinite()||g.isZero()))if(o.length===0){var m=new N(0,0,0,0);m.copy(g),o.push(m)}else{for(var w=!1,S=1/0,T=0,b=0;b<o.length;++b){var C=o[b];if(C.intersect(g)){var L=new N(0,0,0,0);L.copy(C),L.union(g),o[b]=L,w=!0;break}else if(h){f.copy(g),f.union(C);var M=g.width*g.height,P=C.width*C.height,R=f.width*f.height,O=R-M-P;O<S&&(S=O,T=b)}}if(h&&(o[T].union(g),w=!0),!w){var m=new N(0,0,0,0);m.copy(g),o.push(m)}h||(h=o.length>=s)}}for(var l=this.__startIndex;l<this.__endIndex;++l){var v=r[l];if(v){var c=v.shouldBePainted(n,a,!0,!0),_=v.__isRendered&&(v.__dirty&Lt||!c)?v.getPrevPaintRect():null;_&&u(_);var y=c&&(v.__dirty&Lt||!v.__isRendered)?v.getPaintRect():null;y&&u(y)}}for(var l=this.__prevStartIndex;l<this.__prevEndIndex;++l){var v=i[l],c=v&&v.shouldBePainted(n,a,!0,!0);if(v&&(!c||!v.__zr)&&v.__isRendered){var _=v.getPrevPaintRect();_&&u(_)}}var d;do{d=!1;for(var l=0;l<o.length;){if(o[l].isZero()){o.splice(l,1);continue}for(var p=l+1;p<o.length;)o[l].intersect(o[p])?(d=!0,o[l].union(o[p]),o.splice(p,1)):p++;l++}}while(d);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(r,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=r+"px",o.height=i+"px"),a.width=r*n,a.height=i*n,s&&(s.width=r*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(r,i,n){var a=this.dom,o=this.ctx,s=a.width,h=a.height;i=i||this.clearColor;var f=this.motionBlur&&!r,u=this.lastFrameAlpha,l=this.dpr,v=this;f&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/l,h/l));var c=this.domBack;function _(y,d,p,g){if(o.clearRect(y,d,p,g),i&&i!=="transparent"){var m=void 0;if(ue(i)){var w=i.global||i.__width===p&&i.__height===g;m=w&&i.__canvasGradient||fn(o,i,{x:0,y:0,width:p,height:g}),i.__canvasGradient=m,i.__width=p,i.__height=g}else Ia(i)&&(i.scaleX=i.scaleX||l,i.scaleY=i.scaleY||l,m=un(o,i,{dirty:function(){v.setUnpainted(),v.painter.refresh()}}));o.save(),o.fillStyle=m||i,o.fillRect(y,d,p,g),o.restore()}f&&(o.save(),o.globalAlpha=u,o.drawImage(c,y,d,p,g),o.restore())}!n||f?_(0,0,s,h):n.length&&j(n,function(y){_(y.x*l,y.y*l,y.width*l,y.height*l)})},t}(Br),xa=1e5,jt=314159,Oe=.01,Du=.001;function Au(e){return e?e.__builtin__?!0:!(typeof e.resize!="function"||typeof e.refresh!="function"):!1}function Eu(e,t){var r=document.createElement("div");return r.style.cssText=["position:relative","width:"+e+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",r}var ju=function(){function e(t,r,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=H({},i||{}),this.dpr=i.devicePixelRatio||Ue,this._singleCanvas=a,this.root=t;var o=t.style;o&&(gn(t),t.innerHTML=""),this.storage=r;var s=this._zlevelList;this._prevDisplayList=[];var h=this._layers;if(a){var u=t,l=u.width,v=u.height;i.width!=null&&(l=i.width),i.height!=null&&(v=i.height),this.dpr=i.devicePixelRatio||1,u.width=l*this.dpr,u.height=v*this.dpr,this._width=l,this._height=v;var c=new zi(u,this,this.dpr);c.__builtin__=!0,c.initContext(),h[jt]=c,c.zlevel=jt,s.push(jt),this._domRoot=t}else{this._width=Ie(t,0,i),this._height=Ie(t,1,i);var f=this._domRoot=Eu(this._width,this._height);t.appendChild(f)}}return e.prototype.getType=function(){return"canvas"},e.prototype.isSingleCanvas=function(){return this._singleCanvas},e.prototype.getViewportRoot=function(){return this._domRoot},e.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},e.prototype.refresh=function(t){var r=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(r,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var h=a===0?this._backgroundColor:null;s.refresh(h)}}return this._opts.useDirtyRect&&(this._prevDisplayList=r.slice()),this},e.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},e.prototype._paintHoverList=function(t){var r=t.length,i=this._hoverlayer;if(i&&i.clear(),!!r){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<r;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(xa)),a||(a=i.ctx,a.save()),ar(a,s,n,o===r-1))}a&&a.restore()}},e.prototype.getHoverLayer=function(){return this.getLayer(xa)},e.prototype.paintOne=function(t,r){Mu(t,r)},e.prototype._paintList=function(t,r,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,r,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(f){f.afterBrush&&f.afterBrush()});else{var h=this;qe(function(){h._paintList(t,r,i,n)})}}},e.prototype._compositeManually=function(){var t=this.getLayer(jt).ctx,r=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,r,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,r,i)})},e.prototype._doPaintList=function(t,r,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var h=this._zlevelList[s],f=this._layers[h];f.__builtin__&&f!==this._hoverlayer&&(f.__dirty||i)&&a.push(f)}for(var u=!0,l=!1,v=function(y){var d=a[y],p=d.ctx,g=o&&d.createRepaintRects(t,r,c._width,c._height),m=i?d.__startIndex:d.__drawIndex,w=!i&&d.incremental&&Date.now,S=w&&Date.now(),T=d.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(d.__startIndex===d.__endIndex)d.clear(!1,T,g);else if(m===d.__startIndex){var b=t[m];(!b.incremental||!b.notClear||i)&&d.clear(!1,T,g)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=d.__startIndex);var C,L=function(O){var x={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(C=m;C<d.__endIndex;C++){var B=t[C];if(B.__inHover&&(l=!0),n._doPaintEl(B,d,o,O,x,C===d.__endIndex-1),w){var D=Date.now()-S;if(D>15)break}}x.prevElClipPaths&&p.restore()};if(g)if(g.length===0)C=d.__endIndex;else for(var M=c.dpr,P=0;P<g.length;++P){var R=g[P];p.save(),p.beginPath(),p.rect(R.x*M,R.y*M,R.width*M,R.height*M),p.clip(),L(R),p.restore()}else p.save(),L(),p.restore();d.__drawIndex=C,d.__drawIndex<d.__endIndex&&(u=!1)},c=this,_=0;_<a.length;_++)v(_);return k.wxa&&j(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:u,needsRefreshHover:l}},e.prototype._doPaintEl=function(t,r,i,n,a,o){var s=r.ctx;if(i){var h=t.getPaintRect();(!n||h&&h.intersect(n))&&(ar(s,t,a,o),t.setPrevPaintRect(h))}else ar(s,t,a,o)},e.prototype.getLayer=function(t,r){this._singleCanvas&&!this._needsManuallyCompositing&&(t=jt);var i=this._layers[t];return i||(i=new zi("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?tr(i,this._layerConfig[t],!0):this._layerConfig[t-Oe]&&tr(i,this._layerConfig[t-Oe],!0),r&&(i.virtual=r),this.insertLayer(t,i),i.initContext()),i},e.prototype.insertLayer=function(t,r){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,h=-1;if(!i[t]&&Au(r)){if(a>0&&t>n[0]){for(h=0;h<a-1&&!(n[h]<t&&n[h+1]>t);h++);s=i[n[h]]}if(n.splice(h+1,0,t),i[t]=r,!r.virtual)if(s){var f=s.dom;f.nextSibling?o.insertBefore(r.dom,f.nextSibling):o.appendChild(r.dom)}else o.firstChild?o.insertBefore(r.dom,o.firstChild):o.appendChild(r.dom);r.painter||(r.painter=this)}},e.prototype.eachLayer=function(t,r){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(r,this._layers[a],a)}},e.prototype.eachBuiltinLayer=function(t,r){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(r,o,a)}},e.prototype.eachOtherLayer=function(t,r){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(r,o,a)}},e.prototype.getLayers=function(){return this._layers},e.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(l,v){l.__dirty=l.__used=!1});function r(l){a&&(a.__endIndex!==l&&(a.__dirty=!0),a.__endIndex=l)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,h;for(h=0;h<t.length;h++){var n=t[h],f=n.zlevel,u=void 0;s!==f&&(s=f,o=0),n.incremental?(u=this.getLayer(f+Du,this._needsManuallyCompositing),u.incremental=!0,o=1):u=this.getLayer(f+(o>0?Oe:0),this._needsManuallyCompositing),u.__builtin__||je("ZLevel "+f+" has been used by unkown layer "+u.id),u!==a&&(u.__used=!0,u.__startIndex!==h&&(u.__dirty=!0),u.__startIndex=h,u.incremental?u.__drawIndex=-1:u.__drawIndex=h,r(h),a=u),n.__dirty&Lt&&!n.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=h))}r(h),this.eachBuiltinLayer(function(l,v){!l.__used&&l.getElementCount()>0&&(l.__dirty=!0,l.__startIndex=l.__endIndex=l.__drawIndex=0),l.__dirty&&l.__drawIndex<0&&(l.__drawIndex=l.__startIndex)})},e.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},e.prototype._clearLayer=function(t){t.clear()},e.prototype.setBackgroundColor=function(t){this._backgroundColor=t,j(this._layers,function(r){r.setUnpainted()})},e.prototype.configLayer=function(t,r){if(r){var i=this._layerConfig;i[t]?tr(i[t],r,!0):i[t]=r;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Oe){var o=this._layers[a];tr(o,i[t],!0)}}}},e.prototype.delLayer=function(t){var r=this._layers,i=this._zlevelList,n=r[t];n&&(n.dom.parentNode.removeChild(n.dom),delete r[t],i.splice(ct(i,t),1))},e.prototype.resize=function(t,r){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),r!=null&&(n.height=r),t=Ie(a,0,n),r=Ie(a,1,n),i.style.display="",this._width!==t||r!==this._height){i.style.width=t+"px",i.style.height=r+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,r);this.refresh(!0)}this._width=t,this._height=r}else{if(t==null||r==null)return;this._width=t,this._height=r,this.getLayer(jt).resize(t,r)}return this},e.prototype.clearLayer=function(t){var r=this._layers[t];r&&r.clear()},e.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},e.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[jt].dom;var r=new zi("image",this,t.pixelRatio||this.dpr);r.initContext(),r.clear(!1,t.backgroundColor||this._backgroundColor);var i=r.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=r.dom.width,a=r.dom.height;this.eachLayer(function(l){l.__builtin__?i.drawImage(l.dom,0,0,n,a):l.renderToCanvas&&(i.save(),l.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),h=0,f=s.length;h<f;h++){var u=s[h];ar(i,u,o,h===f-1)}return r.dom},e.prototype.getWidth=function(){return this._width},e.prototype.getHeight=function(){return this._height},e}();export{Pr as $,Xu as A,Lf as B,Pf as C,$a as D,Be as E,Gf as F,qf as G,eu as H,nu as I,Lo as J,fu as K,su as L,lu as M,N,$u as O,$ as P,go as Q,jf as R,Kf as S,po as T,Qu as U,Zu as V,Vu as W,z as X,Uu as Y,Sn as Z,Df as _,ns as a,tr as a0,cr as a1,pn as a2,zu as a3,Fa as a4,ts as a5,Ea as a6,ki as a7,jo as a8,as as a9,Pe as aA,Nu as aB,ku as aC,Bu as aD,Fu as aE,Wu as aF,io as aG,G as aH,uo as aI,is as aJ,Za as aK,Es as aL,hr as aM,qi as aN,Ge as aO,lt as aP,Hu as aQ,ht as aR,_r as aS,ju as aT,fs as aa,or as ab,Rh as ac,Ku as ad,ti as ae,vr as af,Mu as ag,Br as ah,zh as ai,Xs as aj,Ou as ak,Xh as al,_n as am,oe as an,Oa as ao,Yh as ap,Ju as aq,rr as ar,er as as,Yu as at,Mh as au,Ga as av,ws as aw,Or as ax,Vs as ay,$s as az,Ir as b,Dt as c,hs as d,j as e,ct as f,Jr as g,rs as h,ee as i,k as j,Qo as k,H as l,dr as m,fe as n,pt as o,tt as p,hh as q,he as r,J as s,Gu as t,qu as u,xr as v,us as w,Ht as x,Va as y,Ar as z};
