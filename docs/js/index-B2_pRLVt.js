import{d as O,c as S,o as y,t as h,r as ge,a as G,b as X,e as Ce,f as u,i as Pe,g as pe,n as ye,h as $e,j as z,w as fe,v as be,k as ae,l as N,s as W,m as J,p as Ae,q as ze,u as H,x as E,y as k,z as ne,F as se,A as le,B as Me,C as Ie}from"./@vue-Blq8kYk9.js";import{d as ke}from"./dayjs-DFssHqF2.js";import{u as De,i as Re,a as Te,b as We,c as Ne,d as qe,e as He,f as Be,g as je,h as Ue}from"./echarts-DJvnQIox.js";import{i as Ge,a as V,f as Y,r as Fe}from"./lodash-es-Do3IEV3x.js";import{m as x}from"./mockjs-QKutli0O.js";import{D as Qe,E as Ve,R as Ye,O as Ke,V as te,a as Xe,M as Ee,P as Je,e as M,B as Ze,b as et,C as tt,A as at,c as lt,d as nt,G as st,S as ot,f as it,W as rt,g as ut,h as ct,i as we,j as dt,k as Se,l as Le,m as Oe,n as vt,o as mt}from"./three-CoCEa9dO.js";import{_ as pt}from"./autofit.js-3s0rcbly.js";/* empty css                    */import"./zrender-BqiosACK.js";import"./tslib-BDyQ-Jie.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const n of a)if(n.type==="childList")for(const d of n.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function t(a){const n={};return a.integrity&&(n.integrity=a.integrity),a.referrerPolicy&&(n.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?n.credentials="include":a.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function s(a){if(a.ep)return;a.ep=!0;const n=t(a);fetch(a.href,n)}})();const ft=O({__name:"WidgetLabel",props:{name:{}},setup(p){const e=()=>{};return(t,s)=>(y(),S("div",{class:"equipment-label",onClick:e},h(t.name),1))}}),C=(p,e)=>{const t=p.__vccOpts||p;for(const[s,a]of e)t[s]=a;return t},bt=C(ft,[["__scopeId","data-v-87cd42ff"]]),gt={class:"layout-header"},_t={class:"header-right"},ht=O({__name:"LayoutHeader",setup(p){const e=ge({time:"--:--:--",date:"--/--/--",week:"--"}),t=()=>{const a=ke();e.time=a.format("HH:mm:ss"),e.date=a.format("MM/DD/YYYY"),e.week=a.format("dddd")};let s;return G(()=>{t(),s=setInterval(t,1e3)}),X(()=>{clearInterval(s)}),(a,n)=>(y(),S("div",gt,[n[1]||(n[1]=Ce('<div class="header-midden" data-v-503410de><div class="cn" data-v-503410de>大型风力发电机监控平台</div><div class="en" data-v-503410de>Large Wind Turbine Monitoring Platform</div></div><div class="header-left" data-v-503410de><i class="fa-regular fa-envelope" data-v-503410de></i><div class="message" content="【系统通知】感谢大家对我们项目的关注与支持!希望能为我们的项目点一个Star,您的支持对我们来说至关重要。" data-v-503410de></div></div>',2)),u("div",_t,[u("span",null,h(e.time),1),u("span",null,h(e.date),1),u("span",null,h(e.week),1),n[0]||(n[0]=u("span",null,"13°c",-1))])]))}}),yt=C(ht,[["__scopeId","data-v-503410de"]]),Et={class:"layout-footer"},wt=O({__name:"LayoutFooter",setup(p){const e=ge({isWarning:!1,isDecompose:!1,isDecomposeRuning:!1}),t=Pe("events"),s=async()=>{e.isWarning=!e.isWarning,e.isWarning?await t.startWarning():await t.stopWarning()},a=async()=>{e.isDecomposeRuning||(e.isDecompose=!e.isDecompose,e.isDecomposeRuning=!0,e.isDecompose?(await t.eqDecomposeAnimation(),e.isDecomposeRuning=!1):(await t.eqComposeAnimation(),e.isDecomposeRuning=!1))},n=pe(()=>{const m={};return e.isDecompose,m.cursor="pointer",e.isWarning?m.color="#5bc7fa":m.color="#fff",m}),d=pe(()=>{const m={};return e.isDecomposeRuning?m.cursor="not-allowed":m.cursor="pointer",e.isDecompose?m.color="#5bc7fa":m.color="#fff",m});return(m,P)=>(y(),S("div",Et,[u("div",{class:"item",style:ye(n.value),onClick:s},h(e.isWarning?"取消告警":"设备告警"),5),u("div",{class:"item",style:ye(d.value),onClick:a},h(e.isDecompose?"设备组装":"设备拆解"),5)]))}}),Lt=C(wt,[["__scopeId","data-v-2b51f703"]]),Ot={key:0,class:"layout-loading animate animate__slideInDown"},xt={class:"loading-tip"},St=O({__name:"LayoutLoading",props:{loading:{}},setup(p){return(e,t)=>e.loading.isLoading?(y(),S("div",Ot,[t[0]||(t[0]=u("div",{class:"loading-gif"},null,-1)),u("div",xt," 模型正在加载中 ("+h(e.loading.loaded)+"/"+h(e.loading.total)+") ... ",1)])):$e("",!0)}}),Ct=C(St,[["__scopeId","data-v-8e11c8ae"]]),Pt={class:"layout"},$t={class:"layout-main"},At={class:"main-left"},zt={class:"main-right"},Mt={class:"main-middle",ref:"container"},It=O({__name:"Layout",props:{loading:{}},setup(p){return(e,t)=>(y(),S("div",Pt,[z(yt),fe(z(Lt,null,null,512),[[be,!e.loading.isLoading]]),u("div",$t,[u("div",At,[ae(e.$slots,"left",{},void 0)]),u("div",zt,[ae(e.$slots,"right",{},void 0)]),u("div",Mt,[z(Ct,{loading:e.loading},null,8,["loading"]),ae(e.$slots,"middle",{},void 0)],512)])]))}}),kt=C(It,[["__scopeId","data-v-9675c270"]]),Dt={class:"layout-panel animate animate__bounceIn"},Rt={class:"panel-header"},Tt={class:"panel-header-title"},Wt={class:"panel-body"},Nt=O({__name:"LayoutPanel",props:{title:{}},setup(p){return(e,t)=>(y(),S("div",Dt,[u("div",Rt,[u("div",Tt,h(e.title),1)]),u("div",Wt,[ae(e.$slots,"default",{},void 0)])]))}}),F=C(Nt,[["__scopeId","data-v-f1785c34"]]);De([Re,Te,We,Ne,qe,He,Be]);function _e(){const p=N(),e=W(),t=()=>{var d;return(d=e.value)==null?void 0:d.resize()},s=()=>{var d;return(d=e.value)==null?void 0:d.clear()},a=(d="light")=>{var m;e.value&&((m=e.value)==null||m.dispose()),Ge(p.value)?e.value=Ue(p.value,d):console.warn("容器还未初始化"),window.removeEventListener("resize",t),window.addEventListener("resize",t)},n=d=>{var m;e.value||a(),(m=e.value)==null||m.setOption(d)};return X(()=>{window.removeEventListener("resize",t)}),G(()=>{window.addEventListener("resize",t)}),{container:p,chart:e,setOption:n,resize:t,clear:s,echarts:je}}const xe={CAMERA_POSITION:[.2,2.8,.4],DECODER_PATH:"./js/draco/gltf/"};function qt(){const p=N(),e=W(),t=W(),s=W(),a=W(),n=W(),d=W(),m=W(),P=W(),I=new Map,B=[],D=new Le,Q=new Map,j=new Qe;j.setDecoderPath(xe.DECODER_PATH),j.setDecoderConfig({type:"js"});const R=()=>{q(),L(),A(),U(),oe(),Z(),ie(),ee(),v()},q=()=>{e.value=new ot},L=()=>{const{clientWidth:l,clientHeight:o}=p.value;t.value=new it(45,l/o,.1,1e4),t.value.position.set(...xe.CAMERA_POSITION)},A=()=>{const{clientWidth:l,clientHeight:o}=p.value;s.value=new rt({antialias:!0,alpha:!0}),s.value.shadowMap.enabled=!1,s.value.setSize(l,o),s.value.localClippingEnabled=!0,s.value.setClearAlpha(.5),s.value.domElement.className="webgl-renderer",p.value.appendChild(s.value.domElement),a.value=new ut,a.value.setSize(l,o),a.value.domElement.className="css2d-renderer",a.value.domElement.style.position="absolute",a.value.domElement.style.top="0px",a.value.domElement.style.pointerEvents="none",p.value.appendChild(a.value.domElement)},U=()=>{n.value=new ct(t.value,s.value.domElement),n.value.minPolarAngle=0,n.value.enableDamping=!0,n.value.dampingFactor=.1,n.value.target.set(0,2.65,0),n.value.maxPolarAngle=we.degToRad(90),n.value.minPolarAngle=we.degToRad(45),n.value.minDistance=.5,n.value.maxDistance=2,n.value.update()},oe=()=>{const l=new dt(10066329,10);e.value.add(l);const o=new Se(16777215,.5);o.position.set(20,20,20),o.position.multiplyScalar(1),o.castShadow=!0,o.shadow.mapSize=new te(1024,1024),e.value.add(o)},ie=()=>{const l=()=>{const{clientWidth:o,clientHeight:c}=p.value;t.value.aspect=o/c,t.value.updateProjectionMatrix(),s.value.setSize(o,c),a.value.setSize(o,c),n.value.update()};window.addEventListener("resize",l),X(()=>{window.removeEventListener("resize",l)})},Z=()=>{const l=new Le().getDelta();s.value.render(e.value,t.value);const o=D.getDelta();B.forEach(c=>c.update(o)),I.forEach(c=>c.render(l)),Q.forEach(c=>V(c)&&c()),a.value.render(e.value,t.value),M.update(),requestAnimationFrame(()=>Z())},re=l=>{const o=new st;o.setDRACOLoader(j);const c=(i,r)=>r(i);return new Promise(i=>{o.load(l,r=>c(r,i))})},ue=(l,o,c)=>{const i=new lt(l),r=nt.findByName(o,c);if(!r)return;i.clipAction(r).play(),B.push(i)},ce=()=>{const l=new at(5e3);e.value.add(l)},de=(l,o)=>{const i=((f,w)=>{const _=O({render:()=>ze(f,w)}),$=z(_);return Ae($,document.createElement("div")),$.el})(l,o);return new tt(i)},ve=()=>{const l=new Ze(1,1,1),o=new et({color:65280}),c=new Ee(l,o);e.value.add(c)},K=l=>{const{from:o,to:c,duration:i,easing:r=M.Easing.Quadratic.Out,onUpdate:f,onComplete:w}=l;return new M.Tween(o).to(c,i).easing(r).onUpdate(_=>V(f)&&f(_)).onComplete(_=>V(w)&&w(_))},me=l=>{const{objects:o,during:c,easing:i,from:r,to:f,onComplete:w}=l,_=new Je(new vt(0,-1,0),r);return o.forEach($=>{$==null||$.traverse(he=>{he instanceof Ee&&(he.material.clippingPlanes=[_])})}),K({from:{constant:r},to:{constant:f},duration:c??1e3,easing:i??M.Easing.Quadratic.Out,onUpdate:$=>{_.constant=$.constant},onComplete:()=>{V(w)&&w()}})},ee=l=>{const o=new Ve(s.value),c=new Ye(e.value,t.value);o.addPass(c),m.value=new Ke(new te(window.innerWidth,window.innerHeight),e.value,t.value);const r=Object.assign({},{edgeStrength:3,edgeGlow:0,edgeThickness:1,pulsePeriod:0,usePatternTexture:!1,visibleEdgeColor:"#fff",hiddenEdgeColor:"#fff"},l);m.value.edgeStrength=r.edgeStrength,m.value.edgeGlow=r.edgeGlow,m.value.edgeThickness=r.edgeThickness,m.value.visibleEdgeColor.set(r.visibleEdgeColor),m.value.hiddenEdgeColor.set(r.hiddenEdgeColor),m.value.selectedObjects=[],o.addPass(m.value);const f=new Xe;o.addPass(f),I.set("outline",o)},v=l=>{let o=[];P.value={get selectedObjects(){return o},set selectedObjects(c){o.forEach(i=>{i.material&&i.material.emissive.setHex(i.hex)}),c.forEach(i=>{i.material=i.material.clone(),i.hex=i.material.emissive.getHex(),i.material.emissive.setHex(l??8947848)}),o=[...c]}}},g=(l,o)=>{const c=i=>{const f=p.value.getBoundingClientRect(),w=new te((i.clientX-f.left)/f.width*2-1,-((i.clientY-f.top)/f.height)*2+1),_=new Oe;_.setFromCamera(w,t.value);const $=_.intersectObject(l,!0);V(o)&&o($)};document.addEventListener("click",c),X(()=>document.removeEventListener("click",c))},b=(l,o)=>{const c=i=>{const f=p.value.getBoundingClientRect(),w=new te((i.clientX-f.left)/f.width*2-1,-((i.clientY-f.top)/f.height)*2+1),_=new Oe;_.setFromCamera(w,t.value);const $=_.intersectObject(l,!0);V(o)&&o($)};document.addEventListener("mousemove",c),X(()=>document.removeEventListener("mousemove",c))};return J(()=>{R()}),{container:p,scene:e,camera:t,renderer:s,cssRenderer:a,ocontrol:n,tcontrol:d,mixers:B,renderMixins:Q,composers:I,outlinePass:m,hexPass:P,loadGltf:re,loadAnimationMixer:ue,loadAxesHelper:ce,loadCSS2DByVue:de,loadTestScene:ve,transitionAnimation:K,planeClippingAnimation:me,addModelPick:g,addModelHoverPick:b,addOutlineEffect:ee,addHexEffect:v}}const T={MODEL_SOURCES:{EQUIPMENT:"./models/equipment.glb",SKELETON:"./models/skeleton.glb"},MODEL_SCALES:[1e-4*3,1e-4*3,1e-4*3],EQUIPMENT_POSITION:{变桨系统:{LABEL:{x:.0291,y:2.6277,z:.2308},COMPOSE:{x:2519.0795,y:29288.6777,z:0},DECOMPOSE:{x:2519.0795,y:29000.6777,z:300}},转子:{LABEL:{x:.0632,y:2.7692,z:.1746},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8850,z:300}},主轴:{LABEL:{x:.0183,y:2.6193,z:.0815},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8350,z:200}},齿轮箱:{LABEL:{x:.0319,y:2.6239,z:-.0402},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8350,z:100}},油冷装置:{LABEL:{x:.0364,y:2.7995,z:.0593},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8650,z:600}},偏航电机:{LABEL:{x:-.0122,y:2.75662,z:-.0305},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8850,z:400}},风冷装置:{LABEL:{x:-.001,y:2.7643,z:-.1305},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8750,z:300}},发电机:{LABEL:{x:.0047,y:2.6156,z:-.2045},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8350,z:0}},控制柜:{LABEL:{x:.0249,y:2.7605,z:-.2521},COMPOSE:{x:20437.7851,y:8650,z:0},DECOMPOSE:{x:20437.7851,y:8850,z:0}}}};function Ht(){const{container:p,scene:e,camera:t,ocontrol:s,outlinePass:a,hexPass:n,loadGltf:d,loadAnimationMixer:m,loadCSS2DByVue:P,addModelPick:I,addModelHoverPick:B,transitionAnimation:D,planeClippingAnimation:Q}=qt(),j=N(""),R=N(!1),q=new mt,L={equipment:null,skeleton:null},A={color:null,wireframe:null},U=ge({total:2,loaded:0,isLoading:!0}),oe=async()=>{await ie(),Z(),await re(),I(L.equipment,v=>{if(v.length>0){const g=v[0].object;j.value=g.name,a.value.selectedObjects=[g]}else j.value="",a.value.selectedObjects=[]}),B(L.equipment,v=>{if(v.length>0){const g=v[0].object;n.value.selectedObjects=[g]}else n.value.selectedObjects=[]})},ie=async()=>{const v=async()=>{const l=(await d(T.MODEL_SOURCES.EQUIPMENT)).scene;l.scale.set(...T.MODEL_SCALES),L.equipment=l,U.loaded+=1,l.name="equipment",e.value.add(l)},g=async()=>{const b=await d(T.MODEL_SOURCES.SKELETON),l=b.scene;m(l,b.animations,b.animations[0].name),l.scale.set(...T.MODEL_SCALES),L.skeleton=l,U.loaded+=1,l.name="skeleton",e.value.add(l),A.color=L.skeleton.getObjectByName("颜色材质"),A.wireframe=L.skeleton.getObjectByName("线框材质")};await Promise.all([v(),g()]),U.isLoading=!1,U.loaded=2},Z=()=>{Y([[0,0,0],[-100,100,100],[100,-100,100],[100,100,-100]],([g,b,l])=>{var c;const o=new Se(16777215,5);o.position.set(g,b,l),(c=e.value)==null||c.add(o)})},re=()=>new Promise(v=>{R.value=!0,Q({objects:[A.color],from:4,to:2,during:1e3*4,onComplete(){R.value=!1,A.color.visible=!1}}).start(),D({from:t.value.position,to:{x:.5,y:2.8,z:.5},duration:1e3*2,easing:M.Easing.Quintic.InOut,onUpdate:({x:g,y:b,z:l})=>{var o;t.value.position.set(g,b,l),(o=s.value)==null||o.update()},onComplete(){R.value=!1,v(void 0)}}).start()}),ue=()=>new Promise(v=>{A.color.visible=!1,R.value=!0;const g=Q({objects:[A.wireframe],from:4,to:2,during:1e3*2,onComplete:()=>{A.wireframe.visible=!1,c.start()}}),b={},l={};Y(L.equipment.children,(i,r)=>{const f=i.name,w=T.EQUIPMENT_POSITION[f].DECOMPOSE,_=T.EQUIPMENT_POSITION[f].COMPOSE;b[`x${r}`]=_.x,b[`y${r}`]=_.y,b[`z${r}`]=_.z,l[`x${r}`]=w.x,l[`y${r}`]=w.y,l[`z${r}`]=w.z});const o=D({from:b,to:l,duration:1e3*2,easing:M.Easing.Quintic.InOut,onUpdate(i){Y(L.equipment.children,(r,f)=>{r.position.set(i[`x${f}`],i[`y${f}`],i[`z${f}`])})},onComplete:()=>{R.value=!1,de(),v(void 0)}}),c=D({from:t.value.position,to:{x:.7,y:2.8,z:0},duration:1e3*2,easing:M.Easing.Linear.None,onUpdate(i){var r;t.value.position.set(i.x,i.y,i.z),(r=s.value)==null||r.update()}});g.chain(o).start()}),ce=()=>new Promise(v=>{R.value=!0,ve(),D({from:t.value.position,to:{x:.5,y:2.8,z:.5},duration:1e3*2,easing:M.Easing.Linear.None,onUpdate(i){var r;t.value.position.set(i.x,i.y,i.z),(r=s.value)==null||r.update()}}).start();const b={},l={};Y(L.equipment.children,(i,r)=>{const f=i.name,w=T.EQUIPMENT_POSITION[f].DECOMPOSE,_=T.EQUIPMENT_POSITION[f].COMPOSE;b[`x${r}`]=w.x,b[`y${r}`]=w.y,b[`z${r}`]=w.z,l[`x${r}`]=_.x,l[`y${r}`]=_.y,l[`z${r}`]=_.z});const o=D({from:b,to:l,duration:1e3*2,easing:M.Easing.Quintic.InOut,onUpdate(i){Y(L.equipment.children,(r,f)=>{r.position.set(i[`x${f}`],i[`y${f}`],i[`z${f}`])})}});A.wireframe.visible=!0;const c=Q({objects:[A.wireframe],from:2,to:4,during:1e3*2,onComplete:()=>{R.value=!1,v(void 0)}});o.chain(c).start()}),de=()=>{Y(T.EQUIPMENT_POSITION,(v,g)=>{const b=P(bt,{name:g});b.position.set(v.LABEL.x,v.LABEL.y,v.LABEL.z),q.add(b)}),e.value.add(q)},ve=()=>{for(;q.children.length>0;){const v=q.children[0];q.remove(v),v.geometry&&v.geometry.dispose(),v.material&&v.material.dispose()}e.value.remove(q)},K=N(),me=()=>{L.equipment.children.forEach(g=>{g.material=g.material.clone(),g.hex=g.material.emissive.getHex()});const v=()=>{const g=Fe(0,L.equipment.children.length-1);L.equipment.children[g].name,L.equipment.children.forEach((b,l)=>{l===g?b.material.emissive.setHex(16711680):b.material.emissive.setHex(b.hex)}),D({from:t.value.position,to:{x:.7,y:2.8,z:0},duration:1e3*2,easing:M.Easing.Linear.None,onUpdate(b){var l;t.value.position.set(b.x,b.y,b.z),(l=s.value)==null||l.update()}}).start()};v(),K.value=setInterval(v,1e3*2)},ee=()=>{clearInterval(K.value),L.equipment.children.forEach(v=>{v.material.emissive.setHex(v.hex)}),D({from:t.value.position,to:{x:.5,y:2.8,z:.5},duration:1e3*2,easing:M.Easing.Linear.None,onUpdate(v){var g;t.value.position.set(v.x,v.y,v.z),(g=s.value)==null||g.update()}}).start()};return J(async()=>{await oe()}),{container:p,loading:U,current:j,eqDecomposeAnimation:ue,eqComposeAnimation:ce,startWarning:me,stopWarning:ee}}const Bt=O({__name:"WidgetPanel01",setup(p){const{container:e,echarts:t,setOption:s}=_e(),a=()=>({legend:{show:!0,right:0,textStyle:{color:"#fff"}},tooltip:{trigger:"axis",backgroundColor:"#000",borderColor:"#333",textStyle:{color:"#fff"}},grid:{left:"4%",right:"4%",bottom:"0%",top:"20%",containLabel:!0},xAxis:{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#fff",margin:10},data:["08月","09月","10月","11月","12月"]},yAxis:{type:"value",axisLabel:{color:"#fff"},splitLine:{lineStyle:{color:"#c8c8c830",type:"dashed"}}},series:[{name:"2024年",type:"bar",emphasis:{focus:"series"},data:[320,332,301,334,390],barWidth:20,barGap:"20%",itemStyle:{color:new t.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(0, 254, 169, 1)"},{offset:1,color:"rgba(0, 254, 169, 0.1)"}])}},{name:"2023年",type:"bar",emphasis:{focus:"series"},data:[220,182,191,234,290],barWidth:20,barGap:"20%",itemStyle:{color:new t.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(87, 153, 214, 1)"},{offset:1,color:"rgba(87, 153, 214, 0.1)"}])}}]});return G(()=>{J(()=>{const n=a();s(n)})}),(n,d)=>(y(),H(E(F),null,{default:k(()=>[u("div",{class:"container",ref_key:"container",ref:e},null,512)]),_:1}))}}),jt=C(Bt,[["__scopeId","data-v-9e582e31"]]),Ut=O({__name:"WidgetPanel02",setup(p){const{container:e,echarts:t,setOption:s}=_e(),a=n=>({legend:{show:!0,right:0,textStyle:{color:"#fff"}},tooltip:{trigger:"axis",backgroundColor:"#000",borderColor:"#333",textStyle:{color:"#fff"}},grid:{left:"1%",right:"6%",bottom:"0%",top:"20%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#fff",margin:20},data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]},yAxis:{type:"value",axisLabel:{color:"#c8c8c8"},splitLine:{lineStyle:{color:"#c8c8c830",type:"dashed"}}},series:[{name:"2024",type:"line",symbol:"none",smooth:!0,lineStyle:{normal:{width:2,color:"rgba(0, 254, 169, 1)"}},itemStyle:{color:"rgba(0, 254, 169, 0.5)"},areaStyle:{color:new t.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(31, 218, 163, 0.4)"},{offset:1,color:"rgba(31, 218, 163, 0)"}])},data:n[0]},{name:"2023",type:"line",symbol:"none",smooth:!0,lineStyle:{normal:{width:2,color:"rgba(87, 153, 214, 1)"}},itemStyle:{color:"rgba(87, 153, 214, 1)"},data:n[1]}]});return G(()=>{J(()=>{const d=a([[859,571,612,906,866,984,212,931,749,993,276,477],[598,539,861,375,576,383,896,430,315,755,808,630]]);s(d)})}),(n,d)=>(y(),H(E(F),null,{default:k(()=>[u("div",{class:"container",ref_key:"container",ref:e},null,512)]),_:1}))}}),Gt=C(Ut,[["__scopeId","data-v-5c0de3b0"]]),Ft=O({__name:"WidgetPanel03",setup(p){const{container:e,echarts:t,setOption:s}=_e(),a=()=>({legend:{show:!0,right:0,textStyle:{color:"#fff"}},tooltip:{trigger:"axis",backgroundColor:"#000",borderColor:"#333",textStyle:{color:"#fff"}},grid:{left:"4%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},xAxis:{type:"category",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"#fff",margin:10},data:[...Array(30).keys()]},yAxis:{type:"value",axisLabel:{color:"#fff"},splitLine:{lineStyle:{color:"#c8c8c830",type:"dashed"}}},series:[{smooth:!0,showSymbol:!1,data:Array.from({length:30}).map(()=>Math.floor(Math.random()*90)+10),type:"bar",itemStyle:{color:new t.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"rgba(0, 254, 169, 1)"},{offset:1,color:"rgba(65, 138, 255, 0.2)"}])}}]});return G(()=>{J(()=>{const n=a();s(n)})}),(n,d)=>(y(),H(E(F),null,{default:k(()=>[u("div",{class:"container",ref_key:"container",ref:e},null,512)]),_:1}))}}),Qt=C(Ft,[["__scopeId","data-v-72265b07"]]),Vt={class:"container"},Yt={class:"label"},Kt={class:"key"},Xt={class:"value"},Jt={class:"unit"},Zt=O({__name:"WidgetPanel04",setup(p){const e=[{icon:"fa-solid fa-temperature-three-quarters",label:"温度",value:"23",unit:"度",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-umbrella",label:"湿度",value:"70",unit:"%",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-fan",label:"气压",value:"23",unit:"kPa",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-wind",label:"最大风速",value:"11",unit:"m/s",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-temperature-arrow-up",label:"环境温度",value:"15",unit:"度",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-weight-scale",label:"负荷率",value:"23",unit:"%",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-plug",label:"总功率",value:"12",unit:"kVa",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-plug",label:"有功功率",value:"12",unit:"kVa",status:x.Random.pick([!0,!1])},{icon:"fa-solid fa-plug",label:"无功功率",value:"12",unit:"kVa",status:x.Random.pick([!0,!1])}];return(t,s)=>(y(),H(E(F),null,{default:k(()=>[u("div",Vt,[(y(),S(se,null,ne(e,(a,n)=>u("div",{class:le(["item",{error:a.status}]),key:n},[u("div",{class:le(["icon",a.icon])},null,2),u("div",Yt,h(a.label),1),u("div",Kt,[u("span",Xt,h(a.value),1),u("span",Jt,h(a.unit),1)]),s[0]||(s[0]=u("i",{class:"alert fa-solid fa-triangle-exclamation"},null,-1))],2)),64))])]),_:1}))}}),ea=C(Zt,[["__scopeId","data-v-2413ef2e"]]),ta={class:"wrap"},aa={class:"item-name"},la={class:"item-angle"},na={class:"item-type"},sa={class:"item-time"},oa=O({__name:"WidgetPanel05",setup(p){const e=["东","东北","北","西北","西","西南","南","东南"],t=N(e.map(()=>{const n=x.Random.integer(0,360);return{name:`向${x.Random.pick(e)}偏航`,angle:`${n}度`,status:n<180?1:0,time:x.Random.date("MM/dd HH:mm:ss")}})),s=N();let a;return G(()=>{a&&window.clearInterval(a),a=setInterval(()=>{s.value.classList.add("scroll"),setTimeout(()=>{a&&(s.value.classList.remove("scroll"),t.value.push(t.value.shift()))},2e3)},3e3)}),(n,d)=>(y(),H(E(F),null,{default:k(()=>[u("div",ta,[u("div",{class:"item-list",ref_key:"container",ref:s},[(y(!0),S(se,null,ne(t.value,({name:m,status:P,angle:I,time:B})=>(y(),S("div",{class:le(["item",{error:P===0}])},[d[0]||(d[0]=u("div",{class:"item-circle"},null,-1)),u("div",aa,h(m),1),u("div",la,h(I),1),u("div",na,h(P?"正常":"异常"),1),u("div",sa,h(B),1)],2))),256))],512)])]),_:1}))}}),ia=C(oa,[["__scopeId","data-v-f3ecacf2"]]),ra={class:"wrap"},ua={class:"item-name"},ca={class:"item-type"},da={class:"item-time"},va=O({__name:"WidgetPanel06",setup(p){const t=N(["发动机","叶片","轮毂","主轴","发电机","塔架","变流器","变桨系统","齿轮箱"].map(n=>({name:n,status:x.Random.pick([0,1]),time:x.Random.date("MM/dd HH:mm:ss")}))),s=N();let a;return G(()=>{a&&window.clearInterval(a),a=setInterval(()=>{s.value.classList.add("scroll"),setTimeout(()=>{a&&(s.value.classList.remove("scroll"),t.value.push(t.value.shift()))},2e3)},3e3)}),(n,d)=>(y(),H(E(F),null,{default:k(()=>[u("div",ra,[u("div",{class:"item-list",ref_key:"container",ref:s},[(y(!0),S(se,null,ne(t.value,({name:m,status:P,time:I})=>(y(),S("div",{class:le(["item",{error:P===0}])},[d[0]||(d[0]=u("div",{class:"item-circle"},null,-1)),u("div",ua,h(m),1),u("div",ca,h(P?"正常":"部件异常"),1),u("div",da,h(I),1)],2))),256))],512)])]),_:1}))}}),ma=C(va,[["__scopeId","data-v-6e451546"]]),pa={class:"container"},fa={class:"item"},ba={class:"item-label"},ga={class:"item-value"},_a=O({__name:"WidgetPanel07",props:{name:{}},setup(p){const e=p,t={变桨系统:[{label:"变桨角度",value:15,unit:"度"},{label:"变桨速度",value:2,unit:"度/秒"},{label:"变桨系统温度",value:45,unit:"摄氏度"},{label:"电机负载",value:3.5,unit:"千瓦"},{label:"变桨位置传感器反馈",value:1.2,unit:"米"},{label:"故障状态",value:0,unit:"(无故障)"},{label:"电源电压",value:400,unit:"伏特"},{label:"桨叶压力",value:1200,unit:"帕斯卡"},{label:"变桨角度变化",value:.5,unit:"度"},{label:"变桨响应时间",value:.1,unit:"秒"},{label:"电流",value:10,unit:"安培"},{label:"变桨系统运行时间",value:200,unit:"小时"},{label:"变桨故障次数",value:1,unit:""},{label:"系统工作状态",value:"正常",unit:""},{label:"变桨电机温度",value:60,unit:"摄氏度"},{label:"变桨角度限制",value:25,unit:"度"}],转子:[{label:"转子直径",value:120,unit:"米"},{label:"转子重量",value:15e3,unit:"千克"},{label:"转子材料",value:"玻璃纤维",unit:""},{label:"转速",value:12,unit:"转/分钟"},{label:"最大载荷",value:5e3,unit:"牛顿"},{label:"转子倾斜角度",value:5,unit:"度"},{label:"转子寿命",value:20,unit:"年"},{label:"转子气动效率",value:92,unit:"%"},{label:"转子温度",value:50,unit:"摄氏度"},{label:"转子材料强度",value:350,unit:"兆帕"},{label:"转子振动频率",value:60,unit:"赫兹"},{label:"转子旋转惯量",value:150,unit:"千克·米²"},{label:"转子工作时间",value:1e3,unit:"小时"},{label:"转子检修周期",value:5,unit:"年"},{label:"转子转动摩擦系数",value:.02,unit:""},{label:"转子冷却方式",value:"风冷",unit:""}],主轴:[{label:"主轴直径",value:.15,unit:"米"},{label:"主轴长度",value:5,unit:"米"},{label:"主轴材料",value:"钢",unit:""},{label:"主轴重量",value:2e3,unit:"千克"},{label:"主轴转速",value:15,unit:"转/分钟"},{label:"主轴承载能力",value:1e4,unit:"牛顿"},{label:"主轴温度",value:60,unit:"摄氏度"},{label:"主轴轴承类型",value:"滚动轴承",unit:""},{label:"主轴材料强度",value:500,unit:"兆帕"},{label:"主轴运行时间",value:3e3,unit:"小时"},{label:"主轴磨损程度",value:.1,unit:"毫米"},{label:"主轴动态平衡状态",value:"正常",unit:""},{label:"主轴冷却方式",value:"油冷",unit:""},{label:"主轴安装角度",value:0,unit:"度"},{label:"主轴检修周期",value:3,unit:"年"},{label:"主轴振动频率",value:50,unit:"赫兹"}],齿轮箱:[{label:"齿轮箱类型",value:"行星齿轮箱",unit:""},{label:"齿轮箱比",value:15,unit:":1"},{label:"齿轮箱输入转速",value:20,unit:"转/分钟"},{label:"齿轮箱输出转速",value:300,unit:"转/分钟"},{label:"齿轮箱效率",value:95,unit:"%"},{label:"齿轮箱温度",value:70,unit:"摄氏度"},{label:"齿轮箱重量",value:250,unit:"千克"},{label:"齿轮材料",value:"合金钢",unit:""},{label:"齿轮箱噪音级别",value:60,unit:"分贝"},{label:"齿轮箱润滑方式",value:"油润滑",unit:""},{label:"齿轮磨损程度",value:.05,unit:"毫米"},{label:"齿轮箱检修周期",value:4,unit:"年"},{label:"齿轮箱冷却方式",value:"水冷",unit:""},{label:"齿轮箱负载能力",value:2e4,unit:"牛顿"},{label:"齿轮箱运行时长",value:5e3,unit:"小时"},{label:"齿轮箱振动状态",value:"正常",unit:""}],油冷装置:[{label:"油冷却器类型",value:"板式冷却器",unit:""},{label:"油流量",value:50,unit:"升/分钟"},{label:"油温",value:40,unit:"摄氏度"},{label:"冷却效率",value:90,unit:"%"},{label:"油冷装置重量",value:100,unit:"千克"},{label:"油类型",value:"合成油",unit:""},{label:"冷却介质温度",value:25,unit:"摄氏度"},{label:"最大工作压力",value:5,unit:"巴"},{label:"油冷却器运行时间",value:2e3,unit:"小时"},{label:"油冷却器故障次数",value:0,unit:""},{label:"油流量波动",value:5,unit:"升/分钟"},{label:"油冷却器材料",value:"铝合金",unit:""},{label:"油冷却器检修周期",value:3,unit:"年"},{label:"工作噪音",value:55,unit:"分贝"},{label:"冷却器振动状态",value:"正常",unit:""},{label:"油温上限",value:70,unit:"摄氏度"}],偏航电机:[{label:"电机类型",value:"伺服电机",unit:""},{label:"额定功率",value:5,unit:"千瓦"},{label:"额定电压",value:400,unit:"伏特"},{label:"额定转速",value:1500,unit:"转/分钟"},{label:"最大扭矩",value:30,unit:"牛顿·米"},{label:"电机重量",value:50,unit:"千克"},{label:"控制类型",value:"闭环控制",unit:""},{label:"工作温度",value:70,unit:"摄氏度"},{label:"电机运行时间",value:1e3,unit:"小时"},{label:"电机故障状态",value:0,unit:"(无故障)"},{label:"电机起动次数",value:10,unit:""},{label:"电机效率",value:92,unit:"%"},{label:"电机冷却方式",value:"风冷",unit:""},{label:"电机绝缘等级",value:"F级",unit:""},{label:"电机负载状态",value:"正常",unit:""},{label:"电机振动状态",value:"正常",unit:""}],风冷装置:[{label:"冷却器类型",value:"风冷散热器",unit:""},{label:"风扇类型",value:"轴流风扇",unit:""},{label:"风扇功率",value:1.5,unit:"千瓦"},{label:"风量",value:3e3,unit:"立方米/小时"},{label:"冷却效率",value:85,unit:"%"},{label:"工作温度",value:35,unit:"摄氏度"},{label:"装置重量",value:25,unit:"千克"},{label:"风扇转速",value:1200,unit:"转/分钟"},{label:"风扇运行时间",value:1500,unit:"小时"},{label:"风扇故障状态",value:0,unit:"(无故障)"},{label:"冷却器噪音级别",value:50,unit:"分贝"},{label:"风扇材料",value:"塑料",unit:""},{label:"风冷装置检修周期",value:2,unit:"年"},{label:"风扇振动状态",value:"正常",unit:""},{label:"风扇电流",value:3,unit:"安培"},{label:"风量波动",value:100,unit:"立方米/小时"}],发电机:[{label:"发电机类型",value:"同步发电机",unit:""},{label:"额定功率",value:1e3,unit:"千瓦"},{label:"额定电压",value:400,unit:"伏特"},{label:"额定频率",value:50,unit:"赫兹"},{label:"效率",value:95,unit:"%"},{label:"发电机重量",value:500,unit:"千克"},{label:"转速",value:1500,unit:"转/分钟"},{label:"冷却方式",value:"水冷",unit:""},{label:"发电机运行时间",value:2e3,unit:"小时"},{label:"发电机故障状态",value:0,unit:"(无故障)"},{label:"发电机启动次数",value:10,unit:""},{label:"发电机输出电流",value:1500,unit:"安培"},{label:"发电机检修周期",value:4,unit:"年"},{label:"发电机振动状态",value:"正常",unit:""},{label:"发电机温度",value:70,unit:"摄氏度"},{label:"发电机负载状态",value:"正常",unit:""}],控制柜:[{label:"控制柜类型",value:"配电控制柜",unit:""},{label:"额定电压",value:400,unit:"伏特"},{label:"额定电流",value:100,unit:"安培"},{label:"防护等级",value:"IP65",unit:""},{label:"控制方式",value:"自动控制",unit:""},{label:"柜体材料",value:"冷轧钢",unit:""},{label:"柜体重量",value:150,unit:"千克"},{label:"工作温度",value:40,unit:"摄氏度"},{label:"柜内电流",value:80,unit:"安培"},{label:"柜内电压",value:380,unit:"伏特"},{label:"控制柜工作状态",value:"正常",unit:""},{label:"柜体温度",value:50,unit:"摄氏度"},{label:"柜内项目数量",value:20,unit:""},{label:"控制柜检修周期",value:3,unit:"年"},{label:"柜体振动状态",value:"正常",unit:""},{label:"柜体防护等级测试",value:"合格",unit:""}]},s=pe(()=>t[e.name]||[]);return(a,n)=>(y(),H(E(F),null,{default:k(()=>[u("div",pa,[(y(!0),S(se,null,ne(s.value,d=>(y(),S("div",fa,[u("div",ba,h(d.label),1),u("div",ga,h(d.value)+h(d.unit),1)]))),256))])]),_:1}))}}),ha=C(_a,[["__scopeId","data-v-a3ae30a5"]]),ya=O({__name:"App",setup(p){const{container:e,loading:t,current:s,eqDecomposeAnimation:a,eqComposeAnimation:n,startWarning:d,stopWarning:m}=Ht();return Me("events",{eqDecomposeAnimation:a,eqComposeAnimation:n,startWarning:d,stopWarning:m}),(P,I)=>(y(),H(E(kt),{loading:E(t)},{left:k(()=>[z(E(ea),{title:"参数监测"}),z(E(Gt),{title:"历史功率"}),z(E(Qt),{title:"日发电量监测 "})]),right:k(()=>[fe(z(E(ha),{title:E(s)+"详情",name:E(s)},null,8,["title","name"]),[[be,E(s)]]),fe(z(E(ma),{title:"运行监测"},null,512),[[be,!E(s)]]),z(E(jt),{title:"故障对比"}),z(E(ia),{title:"偏航角度监测"})]),middle:k(()=>[u("div",{style:{width:"100%",height:"100%"},ref_key:"container",ref:e},null,512)]),_:1},8,["loading"]))}}),Ea=async()=>{Ie(ya).mount("#app");const e=[1920,1080];pt.init({el:"#app",dw:e[0],dh:e[1],resize:!0})};Ea();
