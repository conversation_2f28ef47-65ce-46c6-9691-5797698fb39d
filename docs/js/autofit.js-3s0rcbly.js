var D=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function B(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var W={},k=D&&D.__values||function(e){var o=typeof Symbol=="function"&&Symbol.iterator,t=o&&e[o],n=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(W,"__esModule",{value:!0});W.elRectification=M;var L=null,R="",N="",F="",T=null,P=null,i=1,w=!1,q={isAutofitRunnig:!1,init:function(e,o){e===void 0&&(e={}),o===void 0&&(o=!0),o&&console.log("autofit.js is running");var t=e,n=t.dw,a=n===void 0?1920:n,u=t.dh,l=u===void 0?1080:u,s=t.el,r=s===void 0?typeof e=="string"?e:"body":s,d=t.resize,p=d===void 0?!0:d,S=t.ignore,h=S===void 0?[]:S,g=t.transition,m=g===void 0?"none":g,A=t.delay,f=A===void 0?0:A,c=t.limit,y=c===void 0?.1:c,j=t.cssMode,b=j===void 0?"scale":j,x=t.allowScroll,_=x===void 0?!1:x;L=r;var v=document.querySelector(r);if(!v){console.error("autofit: '".concat(r,"' is not exist"));return}var z=document.createElement("style"),H=document.createElement("style");z.lang="text/css",H.lang="text/css",z.id="autofit-style",H.id="ignoreStyle",!_&&(z.innerHTML="body {overflow: hidden;}");var C=document.querySelector("body");C.appendChild(z),C.appendChild(H),v.style.height="".concat(l,"px"),v.style.width="".concat(a,"px"),v.style.transformOrigin="0 0",!_&&(v.style.overflow="hidden"),O(a,l,v,h,y,b),T=function(){clearTimeout(P),f!=0?P=setTimeout(function(){O(a,l,v,h,y,b),w&&M(R,F,N)},f):(O(a,l,v,h,y,b),w&&M(R,F,N))},p&&window.addEventListener("resize",T),this.isAutofitRunnig=!0,setTimeout(function(){v.style.transition="".concat(m,"s")})},off:function(e){var o;e===void 0&&(e="body");try{window.removeEventListener("resize",T),(o=document.querySelector("#autofit-style"))===null||o===void 0||o.remove();var t=document.querySelector("#ignoreStyle");t&&t.remove();var n=document.querySelector(L||e);n&&(n.style=""),w&&G()}catch(a){console.error("autofit: Failed to remove normally",a),this.isAutofitRunnig=!1}this.isAutofitRunnig&&console.log("autofit.js is off")},elRectification:null,scale:i};function M(e,o,t){var n,a;o===void 0&&(o=!0),t===void 0&&(t=1),q.isAutofitRunnig||console.error("autofit.js：autofit has not been initialized yet"),G(),!e&&console.error("autofit.js：bad selector: ".concat(e)),R=e,N=t,F=o;var u=Array.from(document.querySelectorAll(e));if(u.length==0){console.error("autofit.js：elRectification found no element");return}try{for(var l=k(u),s=l.next();!s.done;s=l.next()){var r=s.value,d=i==1?1:Number(i)*Number(t);w||(r.originalWidth=r.clientWidth,r.originalHeight=r.clientHeight),o?(r.style.width="".concat(r.originalWidth*d,"px"),r.style.height="".concat(r.originalHeight*d,"px")):(r.style.width="".concat(100*d,"%"),r.style.height="".concat(100*d,"%")),r.style.transform="scale(".concat(1/Number(i),")"),r.style.transformOrigin="0 0"}}catch(p){n={error:p}}finally{try{s&&!s.done&&(a=l.return)&&a.call(l)}finally{if(n)throw n.error}}w=!0}function G(){var e,o;if(R){w=!1;try{for(var t=k(Array.from(document.querySelectorAll(R))),n=t.next();!n.done;n=t.next()){var a=n.value;a.style.width="",a.style.height="",a.style.transform=""}}catch(u){e={error:u}}finally{try{n&&!n.done&&(o=t.return)&&o.call(t)}finally{if(e)throw e.error}}}}function O(e,o,t,n,a,u){var l,s;u===void 0&&(u="scale");var r=document.documentElement.clientHeight,d=document.documentElement.clientWidth;i=d/r<e/o?d/e:r/o,i=Math.abs(1-i)>a?i:1,q.scale=+i;var p=Math.round(r/Number(i)),S=Math.round(d/Number(i));t.style.height="".concat(p,"px"),t.style.width="".concat(S,"px"),u==="zoom"?t.style.zoom="".concat(i):t.style.transform="scale(".concat(i,")");var h=document.querySelector("#ignoreStyle");h.innerHTML="";try{for(var g=k(n),m=g.next();!m.done;m=g.next()){var A=m.value,f=A,c=f.el||f.dom;if(typeof f=="string"&&(c=f),!c||typeof c=="object"&&!Object.keys(c).length){console.error("autofit: found invalid or empty selector/object: ".concat(c));continue}var y=f.scale?f.scale:1/Number(i),j=y!=i&&f.fontSize,b=y!=i&&f.width,x=y!=i&&f.height;h.innerHTML+=`
`.concat(c,` { 
      transform: scale(`).concat(y,`)!important;
      transform-origin: 0 0;
      `).concat(b?"width: ".concat(b,"!important;"):"",`
      `).concat(x?"height: ".concat(x,"!important;"):"",`
    }`),j&&(h.innerHTML+=`
`.concat(c," div ,").concat(c," span,").concat(c," a,").concat(c,` * {
        font-size: `).concat(j,`px;
      }`))}}catch(_){l={error:_}}finally{try{m&&!m.done&&(s=g.return)&&s.call(g)}finally{if(l)throw l.error}}}q.elRectification=M;var J=W.default=q;export{J as _,D as c,B as g};
