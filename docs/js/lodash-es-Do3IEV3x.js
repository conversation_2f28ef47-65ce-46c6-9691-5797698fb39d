var $=typeof global=="object"&&global&&global.Object===Object&&global,L=typeof self=="object"&&self&&self.Object===Object&&self,S=$||L||Function("return this")(),p=S.Symbol,E=Object.prototype,_=E.hasOwnProperty,w=E.toString,b=p?p.toStringTag:void 0;function q(r){var t=_.call(r,b),e=r[b];try{r[b]=void 0;var o=!0}catch{}var a=w.call(r);return o&&(t?r[b]=e:delete r[b]),a}var K=Object.prototype,X=K.toString;function D(r){return X.call(r)}var V="[object Null]",W="[object Undefined]",O=p?p.toStringTag:void 0;function g(r){return r==null?r===void 0?W:V:O&&O in Object(r)?q(r):D(r)}function s(r){return r!=null&&typeof r=="object"}var H="[object Symbol]";function Y(r){return typeof r=="symbol"||s(r)&&g(r)==H}var F=Array.isArray,k=/\s/;function z(r){for(var t=r.length;t--&&k.test(r.charAt(t)););return t}var J=/^\s+/;function Q(r){return r&&r.slice(0,z(r)+1).replace(J,"")}function j(r){var t=typeof r;return r!=null&&(t=="object"||t=="function")}var h=NaN,Z=/^[-+]0x[0-9a-f]+$/i,rr=/^0b[01]+$/i,tr=/^0o[0-7]+$/i,er=parseInt;function nr(r){if(typeof r=="number")return r;if(Y(r))return h;if(j(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=j(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=Q(r);var e=rr.test(r);return e||tr.test(r)?er(r.slice(2),e?2:8):Z.test(r)?h:+r}var or=1/0,ar=17976931348623157e292;function l(r){if(!r)return r===0?r:0;if(r=nr(r),r===or||r===-1/0){var t=r<0?-1:1;return t*ar}return r===r?r:0}function ir(r){return r}var fr="[object AsyncFunction]",cr="[object Function]",sr="[object GeneratorFunction]",ur="[object Proxy]";function br(r){if(!j(r))return!1;var t=g(r);return t==cr||t==sr||t==fr||t==ur}function yr(r,t){for(var e=-1,o=r==null?0:r.length;++e<o&&t(r[e],e,r)!==!1;);return r}var gr=9007199254740991,dr=/^(?:0|[1-9]\d*)$/;function B(r,t){var e=typeof r;return t=t??gr,!!t&&(e=="number"||e!="symbol"&&dr.test(r))&&r>-1&&r%1==0&&r<t}function pr(r,t){return r===t||r!==r&&t!==t}var jr=9007199254740991;function M(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=jr}function m(r){return r!=null&&M(r.length)&&!br(r)}function Tr(r,t,e){if(!j(e))return!1;var o=typeof t;return(o=="number"?m(e)&&B(t,e.length):o=="string"&&t in e)?pr(e[t],r):!1}var mr=Object.prototype;function Or(r){var t=r&&r.constructor,e=typeof t=="function"&&t.prototype||mr;return r===e}function hr(r,t){for(var e=-1,o=Array(r);++e<r;)o[e]=t(e);return o}var lr="[object Arguments]";function A(r){return s(r)&&g(r)==lr}var N=Object.prototype,Ar=N.hasOwnProperty,vr=N.propertyIsEnumerable,xr=A(function(){return arguments}())?A:function(r){return s(r)&&Ar.call(r,"callee")&&!vr.call(r,"callee")};function Ir(){return!1}var R=typeof exports=="object"&&exports&&!exports.nodeType&&exports,v=R&&typeof module=="object"&&module&&!module.nodeType&&module,Pr=v&&v.exports===R,x=Pr?S.Buffer:void 0,$r=x?x.isBuffer:void 0,Sr=$r||Ir,Er="[object Arguments]",Fr="[object Array]",Br="[object Boolean]",Mr="[object Date]",Nr="[object Error]",Rr="[object Function]",Ur="[object Map]",Cr="[object Number]",Gr="[object Object]",Lr="[object RegExp]",_r="[object Set]",wr="[object String]",qr="[object WeakMap]",Kr="[object ArrayBuffer]",Xr="[object DataView]",Dr="[object Float32Array]",Vr="[object Float64Array]",Wr="[object Int8Array]",Hr="[object Int16Array]",Yr="[object Int32Array]",kr="[object Uint8Array]",zr="[object Uint8ClampedArray]",Jr="[object Uint16Array]",Qr="[object Uint32Array]",n={};n[Dr]=n[Vr]=n[Wr]=n[Hr]=n[Yr]=n[kr]=n[zr]=n[Jr]=n[Qr]=!0;n[Er]=n[Fr]=n[Kr]=n[Br]=n[Xr]=n[Mr]=n[Nr]=n[Rr]=n[Ur]=n[Cr]=n[Gr]=n[Lr]=n[_r]=n[wr]=n[qr]=!1;function Zr(r){return s(r)&&M(r.length)&&!!n[g(r)]}function rt(r){return function(t){return r(t)}}var U=typeof exports=="object"&&exports&&!exports.nodeType&&exports,y=U&&typeof module=="object"&&module&&!module.nodeType&&module,tt=y&&y.exports===U,T=tt&&$.process,I=function(){try{var r=y&&y.require&&y.require("util").types;return r||T&&T.binding&&T.binding("util")}catch{}}(),P=I&&I.isTypedArray,et=P?rt(P):Zr,nt=Object.prototype,ot=nt.hasOwnProperty;function at(r,t){var e=F(r),o=!e&&xr(r),a=!e&&!o&&Sr(r),i=!e&&!o&&!a&&et(r),c=e||o||a||i,u=c?hr(r.length,String):[],d=u.length;for(var f in r)ot.call(r,f)&&!(c&&(f=="length"||a&&(f=="offset"||f=="parent")||i&&(f=="buffer"||f=="byteLength"||f=="byteOffset")||B(f,d)))&&u.push(f);return u}function C(r,t){return function(e){return r(t(e))}}var it=C(Object.keys,Object),ft=Object.prototype,ct=ft.hasOwnProperty;function st(r){if(!Or(r))return it(r);var t=[];for(var e in Object(r))ct.call(r,e)&&e!="constructor"&&t.push(e);return t}function ut(r){return m(r)?at(r):st(r)}var bt=C(Object.getPrototypeOf,Object),yt="[object Object]",gt=Function.prototype,dt=Object.prototype,G=gt.toString,pt=dt.hasOwnProperty,jt=G.call(Object);function Tt(r){if(!s(r)||g(r)!=yt)return!1;var t=bt(r);if(t===null)return!0;var e=pt.call(t,"constructor")&&t.constructor;return typeof e=="function"&&e instanceof e&&G.call(e)==jt}function mt(r){return function(t,e,o){for(var a=-1,i=Object(t),c=o(t),u=c.length;u--;){var d=c[++a];if(e(i[d],d,i)===!1)break}return t}}var Ot=mt();function ht(r,t){return r&&Ot(r,t,ut)}function lt(r,t){return function(e,o){if(e==null)return e;if(!m(e))return r(e,o);for(var a=e.length,i=-1,c=Object(e);++i<a&&o(c[i],i,c)!==!1;);return e}}var At=lt(ht);function vt(r){return typeof r=="function"?r:ir}function Ft(r,t){var e=F(r)?yr:At;return e(r,vt(t))}function Bt(r){return s(r)&&r.nodeType===1&&!Tt(r)}var xt=Math.floor,It=Math.random;function Pt(r,t){return r+xt(It()*(t-r+1))}var $t=parseFloat,St=Math.min,Et=Math.random;function Mt(r,t,e){if(e&&typeof e!="boolean"&&Tr(r,t,e)&&(t=e=void 0),e===void 0&&(typeof t=="boolean"?(e=t,t=void 0):typeof r=="boolean"&&(e=r,r=void 0)),r===void 0&&t===void 0?(r=0,t=1):(r=l(r),t===void 0?(t=r,r=0):t=l(t)),r>t){var o=r;r=t,t=o}if(e||r%1||t%1){var a=Et();return St(r+a*(t-r+$t("1e-"+((a+"").length-1))),t)}return Pt(r,t)}export{br as a,Ft as f,Bt as i,Mt as r};
