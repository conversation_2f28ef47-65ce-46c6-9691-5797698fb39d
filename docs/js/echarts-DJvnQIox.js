import{i as B,r as Vr,m as F,a as lt,b as R,c as V,d as W,e as b,f as ie,g as oe,h as xi,j as te,k as $u,l as N,n as H,o as Qn,p as Ye,P as Ie,q as ds,s as K,Z as sr,t as xh,u as bh,v as wh,w as Gt,x as ee,T as Ku,y as Jn,z as ut,A as Th,B as Ch,C as Dh,D as fo,E as On,F as ea,G as co,S as lr,R as vo,H as ho,I as ta,J as fe,L as wt,K as po,M as ra,N as Ee,O as qu,Q as me,U as ju,V as go,W as Nn,X as He,Y as Qu,_ as we,$ as tn,a0 as j,a1 as $,a2 as ft,a3 as xe,a4 as Mh,a5 as le,a6 as Re,a7 as se,a8 as Q,a9 as bi,aa as Ah,ab as Tt,ac as wi,ad as Ih,ae as Lh,af as Ju,ag as ef,ah as na,ai as ps,aj as ka,ak as tf,al as Ph,am as kh,an as Ti,ao as Ci,ap as Eh,aq as gs,ar as Rh,as as Oh,at as Nh,au as rf,av as ms,aw as Ea,ax as nf,ay as Bh,az as Fh,aA as Ar,aB as Gh,aC as Vh,aD as zh,aE as Hh,aF as Wh,aG as ys,aH as _s,aI as Uh,aJ as zr,aK as af,aL as Yh,aM as Xh,aN as Zh,aO as Ss,aP as $h,aQ as Kh,aR as qh,aS as jh,aT as Qh}from"./zrender-BqiosACK.js";import{_ as z}from"./tslib-BDyQ-Jie.js";var Jh=1e-4,of=20;function ed(r){return r.replace(/^\s+|\s+$/g,"")}function Di(r,e,t,n){var a=e[0],i=e[1],o=t[0],s=t[1],l=i-a,u=s-o;if(l===0)return u===0?o:(o+s)/2;if(n)if(l>0){if(r<=a)return o;if(r>=i)return s}else{if(r>=a)return o;if(r<=i)return s}else{if(r===a)return o;if(r===i)return s}return(r-a)/l*u+o}function ve(r,e){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return B(r)?ed(r).match(/%$/)?parseFloat(r)/100*e:parseFloat(r):r==null?NaN:+r}function ae(r,e,t){return e==null&&(e=10),e=Math.min(Math.max(0,e),of),r=(+r).toFixed(e),t?r:+r}function td(r){return r.sort(function(e,t){return e-t}),r}function Je(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var e=1,t=0;t<15;t++,e*=10)if(Math.round(r*e)/e===r)return t}return sf(r)}function sf(r){var e=r.toString().toLowerCase(),t=e.indexOf("e"),n=t>0?+e.slice(t+1):0,a=t>0?t:e.length,i=e.indexOf("."),o=i<0?0:a-1-i;return Math.max(0,o-n)}function lf(r,e){var t=Math.log,n=Math.LN10,a=Math.floor(t(r[1]-r[0])/n),i=Math.round(t(Math.abs(e[1]-e[0]))/n),o=Math.min(Math.max(-a+i,0),20);return isFinite(o)?o:20}function rd(r,e,t){if(!r[e])return 0;var n=nd(r,t);return n[e]||0}function nd(r,e){var t=Vr(r,function(v,d){return v+(isNaN(d)?0:d)},0);if(t===0)return[];for(var n=Math.pow(10,e),a=F(r,function(v){return(isNaN(v)?0:v)/t*n*100}),i=n*100,o=F(a,function(v){return Math.floor(v)}),s=Vr(o,function(v,d){return v+d},0),l=F(a,function(v,d){return v-o[d]});s<i;){for(var u=Number.NEGATIVE_INFINITY,f=null,c=0,h=l.length;c<h;++c)l[c]>u&&(u=l[c],f=c);++o[f],l[f]=0,++s}return F(o,function(v){return v/n})}function ad(r,e){var t=Math.max(Je(r),Je(e)),n=r+e;return t>of?n:ae(n,t)}var id=9007199254740991;function mo(r){var e=Math.PI*2;return(r%e+e)%e}function Hr(r){return r>-1e-4&&r<Jh}var od=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function We(r){if(r instanceof Date)return r;if(B(r)){var e=od.exec(r);if(!e)return new Date(NaN);if(e[8]){var t=+e[4]||0;return e[8].toUpperCase()!=="Z"&&(t-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,t,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}else return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function uf(r){return Math.pow(10,aa(r))}function aa(r){if(r===0)return 0;var e=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,e)>=10&&e++,e}function yo(r,e){var t=aa(r),n=Math.pow(10,t),a=r/n,i;return e?a<1.5?i=1:a<2.5?i=2:a<4?i=3:a<7?i=5:i=10:a<1?i=1:a<2?i=2:a<3?i=3:a<5?i=5:i=10,r=i*n,t>=-20?+r.toFixed(t<0?-t:0):r}function sd(r,e){var t=(r.length-1)*e+1,n=Math.floor(t),a=+r[n-1],i=t-n;return i?a+i*(r[n]-a):a}function ld(r){r.sort(function(l,u){return s(l,u,0)?-1:1});for(var e=-1/0,t=1,n=0;n<r.length;){for(var a=r[n].interval,i=r[n].close,o=0;o<2;o++)a[o]<=e&&(a[o]=e,i[o]=o?1:1-t),e=a[o],t=i[o];a[0]===a[1]&&i[0]*i[1]!==1?r.splice(n,1):n++}return r;function s(l,u,f){return l.interval[f]<u.interval[f]||l.interval[f]===u.interval[f]&&(l.close[f]-u.close[f]===(f?-1:1)||!f&&s(l,u,1))}}function Wr(r){var e=parseFloat(r);return e==r&&(e!==0||!B(r)||r.indexOf("x")<=0)?e:NaN}function ff(r){return!isNaN(Wr(r))}function cf(){return Math.round(Math.random()*9)}function vf(r,e){return e===0?r:vf(e,r%e)}function xs(r,e){return r==null?e:e==null?r:r*e/vf(r,e)}function De(r){throw new Error(r)}function bs(r,e,t){return(e-r)*t+r}var hf="series\0",ud="\0_ec_\0";function be(r){return r instanceof Array?r:r==null?[]:[r]}function ws(r,e,t){if(r){r[e]=r[e]||{},r.emphasis=r.emphasis||{},r.emphasis[e]=r.emphasis[e]||{};for(var n=0,a=t.length;n<a;n++){var i=t[n];!r.emphasis[e].hasOwnProperty(i)&&r[e].hasOwnProperty(i)&&(r.emphasis[e][i]=r[e][i])}}}var Ts=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function rn(r){return V(r)&&!R(r)&&!(r instanceof Date)?r.value:r}function fd(r){return V(r)&&!(r instanceof Array)}function cd(r,e,t){var n=t==="normalMerge",a=t==="replaceMerge",i=t==="replaceAll";r=r||[],e=(e||[]).slice();var o=W();b(e,function(l,u){if(!V(l)){e[u]=null;return}});var s=vd(r,o,t);return(n||a)&&hd(s,r,o,e),n&&dd(s,e),n||a?pd(s,e,a):i&&gd(s,e),md(s),s}function vd(r,e,t){var n=[];if(t==="replaceAll")return n;for(var a=0;a<r.length;a++){var i=r[a];i&&i.id!=null&&e.set(i.id,a),n.push({existing:t==="replaceMerge"||Ur(i)?null:i,newOption:null,keyInfo:null,brandNew:null})}return n}function hd(r,e,t,n){b(n,function(a,i){if(!(!a||a.id==null)){var o=kr(a.id),s=t.get(o);if(s!=null){var l=r[s];lt(!l.newOption,'Duplicated option on id "'+o+'".'),l.newOption=a,l.existing=e[s],n[i]=null}}})}function dd(r,e){b(e,function(t,n){if(!(!t||t.name==null))for(var a=0;a<r.length;a++){var i=r[a].existing;if(!r[a].newOption&&i&&(i.id==null||t.id==null)&&!Ur(t)&&!Ur(i)&&df("name",i,t)){r[a].newOption=t,e[n]=null;return}}})}function pd(r,e,t){b(e,function(n){if(n){for(var a,i=0;(a=r[i])&&(a.newOption||Ur(a.existing)||a.existing&&n.id!=null&&!df("id",n,a.existing));)i++;a?(a.newOption=n,a.brandNew=t):r.push({newOption:n,brandNew:t,existing:null,keyInfo:null}),i++}})}function gd(r,e){b(e,function(t){r.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})}function md(r){var e=W();b(r,function(t){var n=t.existing;n&&e.set(n.id,t)}),b(r,function(t){var n=t.newOption;lt(!n||n.id==null||!e.get(n.id)||e.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&n.id!=null&&e.set(n.id,t),!t.keyInfo&&(t.keyInfo={})}),b(r,function(t,n){var a=t.existing,i=t.newOption,o=t.keyInfo;if(V(i)){if(o.name=i.name!=null?kr(i.name):a?a.name:hf+n,a)o.id=kr(a.id);else if(i.id!=null)o.id=kr(i.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(e.get(o.id))}e.set(o.id,t)}})}function df(r,e,t){var n=tt(e[r],null),a=tt(t[r],null);return n!=null&&a!=null&&n===a}function kr(r){return tt(r,"")}function tt(r,e){return r==null?e:B(r)?r:oe(r)||xi(r)?r+"":e}function _o(r){var e=r.name;return!!(e&&e.indexOf(hf))}function Ur(r){return r&&r.id!=null&&kr(r.id).indexOf(ud)===0}function yd(r,e,t){b(r,function(n){var a=n.newOption;V(a)&&(n.keyInfo.mainType=e,n.keyInfo.subType=_d(e,a,n.existing,t))})}function _d(r,e,t,n){var a=e.type?e.type:t?t.subType:n.determineSubType(r,e);return a}function Vt(r,e){if(e.dataIndexInside!=null)return e.dataIndexInside;if(e.dataIndex!=null)return R(e.dataIndex)?F(e.dataIndex,function(t){return r.indexOfRawIndex(t)}):r.indexOfRawIndex(e.dataIndex);if(e.name!=null)return R(e.name)?F(e.name,function(t){return r.indexOfName(t)}):r.indexOfName(e.name)}function re(){var r="__ec_inner_"+Sd++;return function(e){return e[r]||(e[r]={})}}var Sd=cf();function Ra(r,e,t){var n=So(e,t),a=n.mainTypeSpecified,i=n.queryOptionMap,o=n.others,s=o,l=t?t.defaultMainType:null;return!a&&l&&i.set(l,{}),i.each(function(u,f){var c=nn(r,f,u,{useDefault:l===f,enableAll:t&&t.enableAll!=null?t.enableAll:!0,enableNone:t&&t.enableNone!=null?t.enableNone:!0});s[f+"Models"]=c.models,s[f+"Model"]=c.models[0]}),s}function So(r,e){var t;if(B(r)){var n={};n[r+"Index"]=0,t=n}else t=r;var a=W(),i={},o=!1;return b(t,function(s,l){if(l==="dataIndex"||l==="dataIndexInside"){i[l]=s;return}var u=l.match(/^(\w+)(Index|Id|Name)$/)||[],f=u[1],c=(u[2]||"").toLowerCase();if(!(!f||!c||e&&e.includeMainTypes&&ie(e.includeMainTypes,f)<0)){o=o||!!f;var h=a.get(f)||a.set(f,{});h[c]=s}}),{mainTypeSpecified:o,queryOptionMap:a,others:i}}var Qe={useDefault:!0,enableAll:!1,enableNone:!1};function nn(r,e,t,n){n=n||Qe;var a=t.index,i=t.id,o=t.name,s={models:null,specified:a!=null||i!=null||o!=null};if(!s.specified){var l=void 0;return s.models=n.useDefault&&(l=r.getComponent(e))?[l]:[],s}return a==="none"||a===!1?(lt(n.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(a==="all"&&(lt(n.enableAll,'`"all"` is not a valid value on index option.'),a=i=o=null),s.models=r.queryComponents({mainType:e,index:a,id:i,name:o}),s)}function pf(r,e,t){r.setAttribute?r.setAttribute(e,t):r[e]=t}function xd(r,e){return r.getAttribute?r.getAttribute(e):r[e]}function bd(r){return r==="auto"?te.domSupported?"html":"richText":r||"html"}function gf(r,e,t,n,a){var i=e==null||e==="auto";if(n==null)return n;if(oe(n)){var o=bs(t||0,n,a);return ae(o,i?Math.max(Je(t||0),Je(n)):e)}else{if(B(n))return a<1?t:n;for(var s=[],l=t,u=n,f=Math.max(l?l.length:0,u.length),c=0;c<f;++c){var h=r.getDimensionInfo(c);if(h&&h.type==="ordinal")s[c]=(a<1&&l?l:u)[c];else{var v=l&&l[c]?l[c]:0,d=u[c],o=bs(v,d,a);s[c]=ae(o,i?Math.max(Je(v),Je(d)):e)}}return s}}var wd=".",Mt="___EC__COMPONENT__CONTAINER___",mf="___EC__EXTENDED_CLASS___";function et(r){var e={main:"",sub:""};if(r){var t=r.split(wd);e.main=t[0]||"",e.sub=t[1]||""}return e}function Td(r){lt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function Cd(r){return!!(r&&r[mf])}function xo(r,e){r.$constructor=r,r.extend=function(t){var n=this,a;return Dd(n)?a=function(i){z(o,i);function o(){return i.apply(this,arguments)||this}return o}(n):(a=function(){(t.$constructor||n).apply(this,arguments)},$u(a,this)),N(a.prototype,t),a[mf]=!0,a.extend=this.extend,a.superCall=Id,a.superApply=Ld,a.superClass=n,a}}function Dd(r){return H(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function yf(r,e){r.extend=e.extend}var Md=Math.round(Math.random()*10);function Ad(r){var e=["__\0is_clz",Md++].join("_");r.prototype[e]=!0,r.isInstance=function(t){return!!(t&&t[e])}}function Id(r,e){for(var t=[],n=2;n<arguments.length;n++)t[n-2]=arguments[n];return this.superClass.prototype[e].apply(r,t)}function Ld(r,e,t){return this.superClass.prototype[e].apply(r,t)}function ia(r){var e={};r.registerClass=function(n){var a=n.type||n.prototype.type;if(a){Td(a),n.prototype.type=a;var i=et(a);if(!i.sub)e[i.main]=n;else if(i.sub!==Mt){var o=t(i);o[i.sub]=n}}return n},r.getClass=function(n,a,i){var o=e[n];if(o&&o[Mt]&&(o=a?o[a]:null),i&&!o)throw new Error(a?"Component "+n+"."+(a||"")+" is used but not imported.":n+".type should be specified.");return o},r.getClassesByMainType=function(n){var a=et(n),i=[],o=e[a.main];return o&&o[Mt]?b(o,function(s,l){l!==Mt&&i.push(s)}):i.push(o),i},r.hasClass=function(n){var a=et(n);return!!e[a.main]},r.getAllClassMainTypes=function(){var n=[];return b(e,function(a,i){n.push(i)}),n},r.hasSubTypes=function(n){var a=et(n),i=e[a.main];return i&&i[Mt]};function t(n){var a=e[n.main];return(!a||!a[Mt])&&(a=e[n.main]={},a[Mt]=!0),a}}function Yr(r,e){for(var t=0;t<r.length;t++)r[t][1]||(r[t][1]=r[t][0]);return e=e||!1,function(n,a,i){for(var o={},s=0;s<r.length;s++){var l=r[s][1];if(!(a&&ie(a,l)>=0||i&&ie(i,l)<0)){var u=n.getShallow(l,e);u!=null&&(o[r[s][0]]=u)}}return o}}var Pd=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],kd=Yr(Pd),Ed=function(){function r(){}return r.prototype.getAreaStyle=function(e,t){return kd(this,e,t)},r}(),Y=re(),Rd=function(r,e,t,n){if(n){var a=Y(n);a.dataIndex=t,a.dataType=e,a.seriesIndex=r,a.ssrType="chart",n.type==="group"&&n.traverse(function(i){var o=Y(i);o.seriesIndex=r,o.dataIndex=t,o.dataType=e,o.ssrType="chart"})}},Cs=1,Ds={},_f=re(),bo=re(),wo=0,oa=1,sa=2,Ue=["emphasis","blur","select"],Bn=["normal","emphasis","blur","select"],Od=10,Nd=9,Nt="highlight",An="downplay",Er="select",In="unselect",Rr="toggleSelect";function Xt(r){return r!=null&&r!=="none"}function la(r,e,t){r.onHoverStateChange&&(r.hoverState||0)!==t&&r.onHoverStateChange(e),r.hoverState=t}function Sf(r){la(r,"emphasis",sa)}function xf(r){r.hoverState===sa&&la(r,"normal",wo)}function To(r){la(r,"blur",oa)}function bf(r){r.hoverState===oa&&la(r,"normal",wo)}function Bd(r){r.selected=!0}function Fd(r){r.selected=!1}function Ms(r,e,t){e(r,t)}function ct(r,e,t){Ms(r,e,t),r.isGroup&&r.traverse(function(n){Ms(n,e,t)})}function As(r,e){switch(e){case"emphasis":r.hoverState=sa;break;case"normal":r.hoverState=wo;break;case"blur":r.hoverState=oa;break;case"select":r.selected=!0}}function Gd(r,e,t,n){for(var a=r.style,i={},o=0;o<e.length;o++){var s=e[o],l=a[s];i[s]=l??(n&&n[s])}for(var o=0;o<r.animators.length;o++){var u=r.animators[o];u.__fromStateTransition&&u.__fromStateTransition.indexOf(t)<0&&u.targetName==="style"&&u.saveTo(i,e)}return i}function Vd(r,e,t,n){var a=t&&ie(t,"select")>=0,i=!1;if(r instanceof Ie){var o=_f(r),s=a&&o.selectFill||o.normalFill,l=a&&o.selectStroke||o.normalStroke;if(Xt(s)||Xt(l)){n=n||{};var u=n.style||{};u.fill==="inherit"?(i=!0,n=N({},n),u=N({},u),u.fill=s):!Xt(u.fill)&&Xt(s)?(i=!0,n=N({},n),u=N({},u),u.fill=ds(s)):!Xt(u.stroke)&&Xt(l)&&(i||(n=N({},n),u=N({},u)),u.stroke=ds(l)),n.style=u}}if(n&&n.z2==null){i||(n=N({},n));var f=r.z2EmphasisLift;n.z2=r.z2+(f??Od)}return n}function zd(r,e,t){if(t&&t.z2==null){t=N({},t);var n=r.z2SelectLift;t.z2=r.z2+(n??Nd)}return t}function Hd(r,e,t){var n=ie(r.currentStates,e)>=0,a=r.style.opacity,i=n?null:Gd(r,["opacity"],e,{opacity:1});t=t||{};var o=t.style||{};return o.opacity==null&&(t=N({},t),o=N({opacity:n?a:i.opacity*.1},o),t.style=o),t}function Oa(r,e){var t=this.states[r];if(this.style){if(r==="emphasis")return Vd(this,r,e,t);if(r==="blur")return Hd(this,r,t);if(r==="select")return zd(this,r,t)}return t}function Wd(r){r.stateProxy=Oa;var e=r.getTextContent(),t=r.getTextGuideLine();e&&(e.stateProxy=Oa),t&&(t.stateProxy=Oa)}function Is(r,e){!Df(r,e)&&!r.__highByOuter&&ct(r,Sf)}function Ls(r,e){!Df(r,e)&&!r.__highByOuter&&ct(r,xf)}function Fn(r,e){r.__highByOuter|=1<<(e||0),ct(r,Sf)}function Gn(r,e){!(r.__highByOuter&=~(1<<(e||0)))&&ct(r,xf)}function Ud(r){ct(r,To)}function wf(r){ct(r,bf)}function Tf(r){ct(r,Bd)}function Cf(r){ct(r,Fd)}function Df(r,e){return r.__highDownSilentOnTouch&&e.zrByTouch}function Mf(r){var e=r.getModel(),t=[],n=[];e.eachComponent(function(a,i){var o=bo(i),s=a==="series",l=s?r.getViewOfSeriesModel(i):r.getViewOfComponentModel(i);!s&&n.push(l),o.isBlured&&(l.group.traverse(function(u){bf(u)}),s&&t.push(i)),o.isBlured=!1}),b(n,function(a){a&&a.toggleBlurSeries&&a.toggleBlurSeries(t,!1,e)})}function Mi(r,e,t,n){var a=n.getModel();t=t||"coordinateSystem";function i(u,f){for(var c=0;c<f.length;c++){var h=u.getItemGraphicEl(f[c]);h&&wf(h)}}if(r!=null&&!(!e||e==="none")){var o=a.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var l=[];a.eachSeries(function(u){var f=o===u,c=u.coordinateSystem;c&&c.master&&(c=c.master);var h=c&&s?c===s:f;if(!(t==="series"&&!f||t==="coordinateSystem"&&!h||e==="series"&&f)){var v=n.getViewOfSeriesModel(u);if(v.group.traverse(function(g){g.__highByOuter&&f&&e==="self"||To(g)}),Qn(e))i(u.getData(),e);else if(V(e))for(var d=Ye(e),p=0;p<d.length;p++)i(u.getData(d[p]),e[d[p]]);l.push(u),bo(u).isBlured=!0}}),a.eachComponent(function(u,f){if(u!=="series"){var c=n.getViewOfComponentModel(f);c&&c.toggleBlurSeries&&c.toggleBlurSeries(l,!0,a)}})}}function Ai(r,e,t){if(!(r==null||e==null)){var n=t.getModel().getComponent(r,e);if(n){bo(n).isBlured=!0;var a=t.getViewOfComponentModel(n);!a||!a.focusBlurEnabled||a.group.traverse(function(i){To(i)})}}}function Yd(r,e,t){var n=r.seriesIndex,a=r.getData(e.dataType);if(a){var i=Vt(a,e);i=(R(i)?i[0]:i)||0;var o=a.getItemGraphicEl(i);if(!o)for(var s=a.count(),l=0;!o&&l<s;)o=a.getItemGraphicEl(l++);if(o){var u=Y(o);Mi(n,u.focus,u.blurScope,t)}else{var f=r.get(["emphasis","focus"]),c=r.get(["emphasis","blurScope"]);f!=null&&Mi(n,f,c,t)}}}function Co(r,e,t,n){var a={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||e==null||t==null)return a;var i=n.getModel().getComponent(r,e);if(!i)return a;var o=n.getViewOfComponentModel(i);if(!o||!o.findHighDownDispatchers)return a;for(var s=o.findHighDownDispatchers(t),l,u=0;u<s.length;u++)if(Y(s[u]).focus==="self"){l=!0;break}return{focusSelf:l,dispatchers:s}}function Xd(r,e,t){var n=Y(r),a=Co(n.componentMainType,n.componentIndex,n.componentHighDownName,t),i=a.dispatchers,o=a.focusSelf;i?(o&&Ai(n.componentMainType,n.componentIndex,t),b(i,function(s){return Is(s,e)})):(Mi(n.seriesIndex,n.focus,n.blurScope,t),n.focus==="self"&&Ai(n.componentMainType,n.componentIndex,t),Is(r,e))}function Zd(r,e,t){Mf(t);var n=Y(r),a=Co(n.componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers;a?b(a,function(i){return Ls(i,e)}):Ls(r,e)}function $d(r,e,t){if(Pi(e)){var n=e.dataType,a=r.getData(n),i=Vt(a,e);R(i)||(i=[i]),r[e.type===Rr?"toggleSelect":e.type===Er?"select":"unselect"](i,n)}}function Ps(r){var e=r.getAllData();b(e,function(t){var n=t.data,a=t.type;n.eachItemGraphicEl(function(i,o){r.isSelected(o,a)?Tf(i):Cf(i)})})}function Kd(r){var e=[];return r.eachSeries(function(t){var n=t.getAllData();b(n,function(a){a.data;var i=a.type,o=t.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:t.seriesIndex};i!=null&&(s.dataType=i),e.push(s)}})}),e}function Vn(r,e,t){Af(r,!0),ct(r,Wd),jd(r,e,t)}function qd(r){Af(r,!1)}function zn(r,e,t,n){n?qd(r):Vn(r,e,t)}function jd(r,e,t){var n=Y(r);e!=null?(n.focus=e,n.blurScope=t):n.focus&&(n.focus=null)}var ks=["emphasis","blur","select"],Qd={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Ii(r,e,t,n){t=t||"itemStyle";for(var a=0;a<ks.length;a++){var i=ks[a],o=e.getModel([i,t]),s=r.ensureState(i);s.style=o[Qd[t]]()}}function Af(r,e){var t=e===!1,n=r;r.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!t||n.__highDownDispatcher)&&(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!t)}function Li(r){return!!(r&&r.__highDownDispatcher)}function Jd(r){var e=Ds[r];return e==null&&Cs<=32&&(e=Ds[r]=Cs++),e}function Pi(r){var e=r.type;return e===Er||e===In||e===Rr}function Es(r){var e=r.type;return e===Nt||e===An}function ep(r){var e=_f(r);e.normalFill=r.style.fill,e.normalStroke=r.style.stroke;var t=r.states.select||{};e.selectFill=t.style&&t.style.fill||null,e.selectStroke=t.style&&t.style.stroke||null}var tp=re();function rp(r,e,t,n,a){var i;if(e&&e.ecModel){var o=e.ecModel.getUpdatePayload();i=o&&o.animation}var s=e&&e.isAnimationEnabled(),l=r==="update";if(s){var u=void 0,f=void 0,c=void 0;n?(u=K(n.duration,200),f=K(n.easing,"cubicOut"),c=0):(u=e.getShallow(l?"animationDurationUpdate":"animationDuration"),f=e.getShallow(l?"animationEasingUpdate":"animationEasing"),c=e.getShallow(l?"animationDelayUpdate":"animationDelay")),i&&(i.duration!=null&&(u=i.duration),i.easing!=null&&(f=i.easing),i.delay!=null&&(c=i.delay)),H(c)&&(c=c(t,a)),H(u)&&(u=u(t));var h={duration:u||0,delay:c,easing:f};return h}else return null}function Do(r,e,t,n,a,i,o){var s=!1,l;H(a)?(o=i,i=a,a=null):V(a)&&(i=a.cb,o=a.during,s=a.isFrom,l=a.removeOpt,a=a.dataIndex);var u=r==="leave";u||e.stopAnimation("leave");var f=rp(r,n,a,u?l||{}:null,n&&n.getAnimationDelayParams?n.getAnimationDelayParams(e,a):null);if(f&&f.duration>0){var c=f.duration,h=f.delay,v=f.easing,d={duration:c,delay:h||0,easing:v,done:i,force:!!i||!!o,setToFinal:!u,scope:r,during:o};s?e.animateFrom(t,d):e.animateTo(t,d)}else e.stopAnimation(),!s&&e.attr(t),o&&o(1),i&&i()}function he(r,e,t,n,a,i){Do("update",r,e,t,n,a,i)}function Oe(r,e,t,n,a,i){Do("enter",r,e,t,n,a,i)}function tr(r){if(!r.__zr)return!0;for(var e=0;e<r.animators.length;e++){var t=r.animators[e];if(t.scope==="leave")return!0}return!1}function Hn(r,e,t,n,a,i){tr(r)||Do("leave",r,e,t,n,a,i)}function Rs(r,e,t,n){r.removeTextContent(),r.removeTextGuideLine(),Hn(r,{style:{opacity:0}},e,t,n)}function ki(r,e,t){function n(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(a){a.isGroup||Rs(a,e,t,n)}):Rs(r,e,t,n)}function If(r){tp(r).oldStyle=r.style}var Wn=Math.max,Un=Math.min,Ei={};function Lf(r){return Ie.extend(r)}var np=Th;function Pf(r,e){return np(r,e)}function Xe(r,e){Ei[r]=e}function kf(r){if(Ei.hasOwnProperty(r))return Ei[r]}function ua(r,e,t,n){var a=xh(r,e);return t&&(n==="center"&&(t=Ef(t,a.getBoundingRect())),Ao(a,t)),a}function Mo(r,e,t){var n=new sr({style:{image:r,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(a){if(t==="center"){var i={width:a.width,height:a.height};n.setStyle(Ef(e,i))}}});return n}function Ef(r,e){var t=e.width/e.height,n=r.height*t,a;n<=r.width?a=r.height:(n=r.width,a=n/t);var i=r.x+r.width/2,o=r.y+r.height/2;return{x:i-n/2,y:o-a/2,width:n,height:a}}var Rf=bh;function Ao(r,e){if(r.applyTransform){var t=r.getBoundingRect(),n=t.calculateTransform(e);r.applyTransform(n)}}function Xr(r,e){return Ch(r,r,{lineWidth:e}),r}function ap(r){return Dh(r.shape,r.shape,r.style),r}var ip=wh;function Of(r,e){for(var t=fo([]);r&&r!==e;)On(t,r.getLocalTransform(),t),r=r.parent;return t}function Io(r,e,t){return e&&!Qn(e)&&(e=Ku.getLocalTransform(e)),t&&(e=Jn([],e)),ut([],r,e)}function op(r,e,t){var n=e[4]===0||e[5]===0||e[0]===0?1:Math.abs(2*e[4]/e[0]),a=e[4]===0||e[5]===0||e[2]===0?1:Math.abs(2*e[4]/e[2]),i=[r==="left"?-n:r==="right"?n:0,r==="top"?-a:r==="bottom"?a:0];return i=Io(i,e,t),Math.abs(i[0])>Math.abs(i[1])?i[0]>0?"right":"left":i[1]>0?"bottom":"top"}function Os(r){return!r.isGroup}function sp(r){return r.shape!=null}function Nf(r,e,t){if(!r||!e)return;function n(o){var s={};return o.traverse(function(l){Os(l)&&l.anid&&(s[l.anid]=l)}),s}function a(o){var s={x:o.x,y:o.y,rotation:o.rotation};return sp(o)&&(s.shape=N({},o.shape)),s}var i=n(r);e.traverse(function(o){if(Os(o)&&o.anid){var s=i[o.anid];if(s){var l=a(o);o.attr(a(s)),he(o,l,t,Y(o).dataIndex)}}})}function Bf(r,e){return F(r,function(t){var n=t[0];n=Wn(n,e.x),n=Un(n,e.x+e.width);var a=t[1];return a=Wn(a,e.y),a=Un(a,e.y+e.height),[n,a]})}function Ff(r,e){var t=Wn(r.x,e.x),n=Un(r.x+r.width,e.x+e.width),a=Wn(r.y,e.y),i=Un(r.y+r.height,e.y+e.height);if(n>=t&&i>=a)return{x:t,y:a,width:n-t,height:i-a}}function fa(r,e,t){var n=N({rectHover:!0},e),a=n.style={strokeNoScale:!0};if(t=t||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(a.image=r.slice(8),ee(a,t),new sr(n)):ua(r.replace("path://",""),n,t,"center")}function lp(r,e,t,n,a){for(var i=0,o=a[a.length-1];i<a.length;i++){var s=a[i];if(Gf(r,e,t,n,s[0],s[1],o[0],o[1]))return!0;o=s}}function Gf(r,e,t,n,a,i,o,s){var l=t-r,u=n-e,f=o-a,c=s-i,h=Na(f,c,l,u);if(up(h))return!1;var v=r-a,d=e-i,p=Na(v,d,l,u)/h;if(p<0||p>1)return!1;var g=Na(v,d,f,c)/h;return!(g<0||g>1)}function Na(r,e,t,n){return r*n-t*e}function up(r){return r<=1e-6&&r>=-1e-6}function ca(r){var e=r.itemTooltipOption,t=r.componentModel,n=r.itemName,a=B(e)?{formatter:e}:e,i=t.mainType,o=t.componentIndex,s={componentType:i,name:n,$vars:["name"]};s[i+"Index"]=o;var l=r.formatterParamsExtra;l&&b(Ye(l),function(f){Gt(s,f)||(s[f]=l[f],s.$vars.push(f))});var u=Y(r.el);u.componentMainType=i,u.componentIndex=o,u.tooltipConfig={name:n,option:ee({content:n,encodeHTMLContent:!0,formatterParams:s},a)}}function Ns(r,e){var t;r.isGroup&&(t=e(r)),t||r.traverse(e)}function va(r,e){if(r)if(R(r))for(var t=0;t<r.length;t++)Ns(r[t],e);else Ns(r,e)}Xe("circle",ea);Xe("ellipse",co);Xe("sector",lr);Xe("ring",vo);Xe("polygon",ho);Xe("polyline",ta);Xe("rect",fe);Xe("line",wt);Xe("bezierCurve",po);Xe("arc",ra);const fp=Object.freeze(Object.defineProperty({__proto__:null,Arc:ra,BezierCurve:po,BoundingRect:Ee,Circle:ea,CompoundPath:qu,Ellipse:co,Group:me,Image:sr,IncrementalDisplayable:ju,Line:wt,LinearGradient:go,OrientedBoundingRect:Nn,Path:Ie,Point:He,Polygon:ho,Polyline:ta,RadialGradient:Qu,Rect:fe,Ring:vo,Sector:lr,Text:we,applyTransform:Io,clipPointsByRect:Bf,clipRectByRect:Ff,createIcon:fa,extendPath:Pf,extendShape:Lf,getShapeClass:kf,getTransform:Of,groupTransition:Nf,initProps:Oe,isElementRemoved:tr,lineLineIntersect:Gf,linePolygonIntersect:lp,makeImage:Mo,makePath:ua,mergePath:Rf,registerShape:Xe,removeElement:Hn,removeElementWithFadeOut:ki,resizePath:Ao,setTooltipConfig:ca,subPixelOptimize:ip,subPixelOptimizeLine:Xr,subPixelOptimizeRect:ap,transformDirection:op,traverseElements:va,updateProps:he},Symbol.toStringTag,{value:"Module"}));var ha={};function Vf(r,e){for(var t=0;t<Ue.length;t++){var n=Ue[t],a=e[n],i=r.ensureState(n);i.style=i.style||{},i.style.text=a}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:e.normal}),r.useStates(o,!0)}function Ri(r,e,t){var n=r.labelFetcher,a=r.labelDataIndex,i=r.labelDimIndex,o=e.normal,s;n&&(s=n.getFormattedLabel(a,"normal",null,i,o&&o.get("formatter"),t!=null?{interpolatedValue:t}:null)),s==null&&(s=H(r.defaultText)?r.defaultText(a,r,t):r.defaultText);for(var l={normal:s},u=0;u<Ue.length;u++){var f=Ue[u],c=e[f];l[f]=K(n?n.getFormattedLabel(a,f,null,i,c&&c.get("formatter")):null,s)}return l}function da(r,e,t,n){t=t||ha;for(var a=r instanceof we,i=!1,o=0;o<Bn.length;o++){var s=e[Bn[o]];if(s&&s.getShallow("show")){i=!0;break}}var l=a?r:r.getTextContent();if(i){a||(l||(l=new we,r.setTextContent(l)),r.stateProxy&&(l.stateProxy=r.stateProxy));var u=Ri(t,e),f=e.normal,c=!!f.getShallow("show"),h=zt(f,n&&n.normal,t,!1,!a);h.text=u.normal,a||r.setTextConfig(Bs(f,t,!1));for(var o=0;o<Ue.length;o++){var v=Ue[o],s=e[v];if(s){var d=l.ensureState(v),p=!!K(s.getShallow("show"),c);if(p!==c&&(d.ignore=!p),d.style=zt(s,n&&n[v],t,!0,!a),d.style.text=u[v],!a){var g=r.ensureState(v);g.textConfig=Bs(s,t,!0)}}}l.silent=!!f.getShallow("silent"),l.style.x!=null&&(h.x=l.style.x),l.style.y!=null&&(h.y=l.style.y),l.ignore=!c,l.useStyle(h),l.dirty(),t.enableTextSetter&&(ur(l).setLabelText=function(m){var y=Ri(t,e,m);Vf(l,y)})}else l&&(l.ignore=!0);r.dirty()}function pa(r,e){e=e||"label";for(var t={normal:r.getModel(e)},n=0;n<Ue.length;n++){var a=Ue[n];t[a]=r.getModel([a,e])}return t}function zt(r,e,t,n,a){var i={};return cp(i,r,t,n,a),e&&N(i,e),i}function Bs(r,e,t){e=e||{};var n={},a,i=r.getShallow("rotate"),o=K(r.getShallow("distance"),t?null:5),s=r.getShallow("offset");return a=r.getShallow("position")||(t?null:"inside"),a==="outside"&&(a=e.defaultOutsidePosition||"top"),a!=null&&(n.position=a),s!=null&&(n.offset=s),i!=null&&(i*=Math.PI/180,n.rotation=i),o!=null&&(n.distance=o),n.outsideFill=r.get("color")==="inherit"?e.inheritColor||null:"auto",n}function cp(r,e,t,n,a){t=t||ha;var i=e.ecModel,o=i&&i.option.textStyle,s=vp(e),l;if(s){l={};for(var u in s)if(s.hasOwnProperty(u)){var f=e.getModel(["rich",u]);zs(l[u]={},f,o,t,n,a,!1,!0)}}l&&(r.rich=l);var c=e.get("overflow");c&&(r.overflow=c);var h=e.get("minMargin");h!=null&&(r.margin=h),zs(r,e,o,t,n,a,!0,!1)}function vp(r){for(var e;r&&r!==r.ecModel;){var t=(r.option||ha).rich;if(t){e=e||{};for(var n=Ye(t),a=0;a<n.length;a++){var i=n[a];e[i]=1}}r=r.parentModel}return e}var Fs=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],Gs=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Vs=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function zs(r,e,t,n,a,i,o,s){t=!a&&t||ha;var l=n&&n.inheritColor,u=e.getShallow("color"),f=e.getShallow("textBorderColor"),c=K(e.getShallow("opacity"),t.opacity);(u==="inherit"||u==="auto")&&(l?u=l:u=null),(f==="inherit"||f==="auto")&&(l?f=l:f=null),i||(u=u||t.color,f=f||t.textBorderColor),u!=null&&(r.fill=u),f!=null&&(r.stroke=f);var h=K(e.getShallow("textBorderWidth"),t.textBorderWidth);h!=null&&(r.lineWidth=h);var v=K(e.getShallow("textBorderType"),t.textBorderType);v!=null&&(r.lineDash=v);var d=K(e.getShallow("textBorderDashOffset"),t.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!a&&c==null&&!s&&(c=n&&n.defaultOpacity),c!=null&&(r.opacity=c),!a&&!i&&r.fill==null&&n.inheritColor&&(r.fill=n.inheritColor);for(var p=0;p<Fs.length;p++){var g=Fs[p],m=K(e.getShallow(g),t[g]);m!=null&&(r[g]=m)}for(var p=0;p<Gs.length;p++){var g=Gs[p],m=e.getShallow(g);m!=null&&(r[g]=m)}if(r.verticalAlign==null){var y=e.getShallow("baseline");y!=null&&(r.verticalAlign=y)}if(!o||!n.disableBox){for(var p=0;p<Vs.length;p++){var g=Vs[p],m=e.getShallow(g);m!=null&&(r[g]=m)}var _=e.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&l&&(r.backgroundColor=l),(r.borderColor==="auto"||r.borderColor==="inherit")&&l&&(r.borderColor=l)}}function hp(r,e){var t=e&&e.getModel("textStyle");return tn([r.fontStyle||t&&t.getShallow("fontStyle")||"",r.fontWeight||t&&t.getShallow("fontWeight")||"",(r.fontSize||t&&t.getShallow("fontSize")||12)+"px",r.fontFamily||t&&t.getShallow("fontFamily")||"sans-serif"].join(" "))}var ur=re();function dp(r,e,t,n){if(r){var a=ur(r);a.prevValue=a.value,a.value=t;var i=e.normal;a.valueAnimation=i.get("valueAnimation"),a.valueAnimation&&(a.precision=i.get("precision"),a.defaultInterpolatedText=n,a.statesModels=e)}}function pp(r,e,t,n,a){var i=ur(r);if(!i.valueAnimation||i.prevValue===i.value)return;var o=i.defaultInterpolatedText,s=K(i.interpolatedValue,i.prevValue),l=i.value;function u(f){var c=gf(t,i.precision,s,l,f);i.interpolatedValue=f===1?null:c;var h=Ri({labelDataIndex:e,labelFetcher:a,defaultText:o?o(c):c+""},i.statesModels,c);Vf(r,h)}r.percent=0,(i.prevValue==null?Oe:he)(r,{percent:1},n,e,null,u)}var gp=["textStyle","color"],Ba=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Fa=new we,mp=function(){function r(){}return r.prototype.getTextColor=function(e){var t=this.ecModel;return this.getShallow("color")||(!e&&t?t.get(gp):null)},r.prototype.getFont=function(){return hp({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(e){for(var t={text:e,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<Ba.length;n++)t[Ba[n]]=this.getShallow(Ba[n]);return Fa.useStyle(t),Fa.update(),Fa.getBoundingRect()},r}(),zf=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],yp=Yr(zf),_p=function(){function r(){}return r.prototype.getLineStyle=function(e){return yp(this,e)},r}(),Hf=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Sp=Yr(Hf),xp=function(){function r(){}return r.prototype.getItemStyle=function(e,t){return Sp(this,e,t)},r}(),J=function(){function r(e,t,n){this.parentModel=t,this.ecModel=n,this.option=e}return r.prototype.init=function(e,t,n){},r.prototype.mergeOption=function(e,t){j(this.option,e,!0)},r.prototype.get=function(e,t){return e==null?this.option:this._doGet(this.parsePath(e),!t&&this.parentModel)},r.prototype.getShallow=function(e,t){var n=this.option,a=n==null?n:n[e];if(a==null&&!t){var i=this.parentModel;i&&(a=i.getShallow(e))}return a},r.prototype.getModel=function(e,t){var n=e!=null,a=n?this.parsePath(e):null,i=n?this._doGet(a):this.option;return t=t||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(a)),new r(i,t,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var e=this.constructor;return new e($(this.option))},r.prototype.parsePath=function(e){return typeof e=="string"?e.split("."):e},r.prototype.resolveParentPath=function(e){return e},r.prototype.isAnimationEnabled=function(){if(!te.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(e,t){var n=this.option;if(!e)return n;for(var a=0;a<e.length&&!(e[a]&&(n=n&&typeof n=="object"?n[e[a]]:null,n==null));a++);return n==null&&t&&(n=t._doGet(this.resolveParentPath(e),t.parentModel)),n},r}();xo(J);Ad(J);ft(J,_p);ft(J,xp);ft(J,Ed);ft(J,mp);var bp=Math.round(Math.random()*10);function ga(r){return[r||"",bp++].join("_")}function wp(r){var e={};r.registerSubTypeDefaulter=function(t,n){var a=et(t);e[a.main]=n},r.determineSubType=function(t,n){var a=n.type;if(!a){var i=et(t).main;r.hasSubTypes(t)&&e[i]&&(a=e[i](n))}return a}}function Tp(r,e){r.topologicalTravel=function(i,o,s,l){if(!i.length)return;var u=t(o),f=u.graph,c=u.noEntryList,h={};for(b(i,function(y){h[y]=!0});c.length;){var v=c.pop(),d=f[v],p=!!h[v];p&&(s.call(l,v,d.originalDeps.slice()),delete h[v]),b(d.successor,p?m:g)}b(h,function(){var y="";throw new Error(y)});function g(y){f[y].entryCount--,f[y].entryCount===0&&c.push(y)}function m(y){h[y]=!0,g(y)}};function t(i){var o={},s=[];return b(i,function(l){var u=n(o,l),f=u.originalDeps=e(l),c=a(f,i);u.entryCount=c.length,u.entryCount===0&&s.push(l),b(c,function(h){ie(u.predecessor,h)<0&&u.predecessor.push(h);var v=n(o,h);ie(v.successor,h)<0&&v.successor.push(l)})}),{graph:o,noEntryList:s}}function n(i,o){return i[o]||(i[o]={predecessor:[],successor:[]}),i[o]}function a(i,o){var s=[];return b(i,function(l){ie(o,l)>=0&&s.push(l)}),s}}function Wf(r,e){return j(j({},r,!0),e,!0)}const Cp={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},Dp={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var Yn="ZH",Lo="EN",rr=Lo,Ln={},Po={},Uf=te.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||rr).toUpperCase();return r.indexOf(Yn)>-1?Yn:rr}():rr;function ko(r,e){r=r.toUpperCase(),Po[r]=new J(e),Ln[r]=e}function Mp(r){if(B(r)){var e=Ln[r.toUpperCase()]||{};return r===Yn||r===Lo?$(e):j($(e),$(Ln[rr]),!1)}else return j($(r),$(Ln[rr]),!1)}function Ap(r){return Po[r]}function Ip(){return Po[rr]}ko(Lo,Cp);ko(Yn,Dp);var Eo=1e3,Ro=Eo*60,Or=Ro*60,ze=Or*24,Hs=ze*365,Ir={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ln="{yyyy}-{MM}-{dd}",Ws={year:"{yyyy}",month:"{yyyy}-{MM}",day:ln,hour:ln+" "+Ir.hour,minute:ln+" "+Ir.minute,second:ln+" "+Ir.second,millisecond:Ir.none},Ga=["year","month","day","hour","minute","second","millisecond"],Yf=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Se(r,e){return r+="","0000".substr(0,e-r.length)+r}function nr(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function Lp(r){return r===nr(r)}function Pp(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function an(r,e,t,n){var a=We(r),i=a[Oo(t)](),o=a[ar(t)]()+1,s=Math.floor((o-1)/3)+1,l=a[ma(t)](),u=a["get"+(t?"UTC":"")+"Day"](),f=a[Zr(t)](),c=(f-1)%12+1,h=a[ya(t)](),v=a[_a(t)](),d=a[Sa(t)](),p=f>=12?"pm":"am",g=p.toUpperCase(),m=n instanceof J?n:Ap(n||Uf)||Ip(),y=m.getModel("time"),_=y.get("month"),S=y.get("monthAbbr"),w=y.get("dayOfWeek"),x=y.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,p+"").replace(/{A}/g,g+"").replace(/{yyyy}/g,i+"").replace(/{yy}/g,Se(i%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,Se(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Se(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,w[u]).replace(/{ee}/g,x[u]).replace(/{e}/g,u+"").replace(/{HH}/g,Se(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Se(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,Se(h,2)).replace(/{m}/g,h+"").replace(/{ss}/g,Se(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Se(d,3)).replace(/{S}/g,d+"")}function kp(r,e,t,n,a){var i=null;if(B(t))i=t;else if(H(t))i=t(r.value,e,{level:r.level});else{var o=N({},Ir);if(r.level>0)for(var s=0;s<Ga.length;++s)o[Ga[s]]="{primary|"+o[Ga[s]]+"}";var l=t?t.inherit===!1?t:ee(t,o):o,u=Xf(r.value,a);if(l[u])i=l[u];else if(l.inherit){for(var f=Yf.indexOf(u),s=f-1;s>=0;--s)if(l[u]){i=l[u];break}i=i||o.none}if(R(i)){var c=r.level==null?0:r.level>=0?r.level:i.length+r.level;c=Math.min(c,i.length-1),i=i[c]}}return an(new Date(r.value),i,a,n)}function Xf(r,e){var t=We(r),n=t[ar(e)]()+1,a=t[ma(e)](),i=t[Zr(e)](),o=t[ya(e)](),s=t[_a(e)](),l=t[Sa(e)](),u=l===0,f=u&&s===0,c=f&&o===0,h=c&&i===0,v=h&&a===1,d=v&&n===1;return d?"year":v?"month":h?"day":c?"hour":f?"minute":u?"second":"millisecond"}function Us(r,e,t){var n=oe(r)?We(r):r;switch(e=e||Xf(r,t),e){case"year":return n[Oo(t)]();case"half-year":return n[ar(t)]()>=6?1:0;case"quarter":return Math.floor((n[ar(t)]()+1)/4);case"month":return n[ar(t)]();case"day":return n[ma(t)]();case"half-day":return n[Zr(t)]()/24;case"hour":return n[Zr(t)]();case"minute":return n[ya(t)]();case"second":return n[_a(t)]();case"millisecond":return n[Sa(t)]()}}function Oo(r){return r?"getUTCFullYear":"getFullYear"}function ar(r){return r?"getUTCMonth":"getMonth"}function ma(r){return r?"getUTCDate":"getDate"}function Zr(r){return r?"getUTCHours":"getHours"}function ya(r){return r?"getUTCMinutes":"getMinutes"}function _a(r){return r?"getUTCSeconds":"getSeconds"}function Sa(r){return r?"getUTCMilliseconds":"getMilliseconds"}function Ep(r){return r?"setUTCFullYear":"setFullYear"}function Zf(r){return r?"setUTCMonth":"setMonth"}function $f(r){return r?"setUTCDate":"setDate"}function Kf(r){return r?"setUTCHours":"setHours"}function qf(r){return r?"setUTCMinutes":"setMinutes"}function jf(r){return r?"setUTCSeconds":"setSeconds"}function Qf(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Rp(r,e,t,n,a,i,o,s){var l=new we({style:{text:r,font:e,align:t,verticalAlign:n,padding:a,rich:i,overflow:o?"truncate":null,lineHeight:s}});return l.getBoundingRect()}function No(r){if(!ff(r))return B(r)?r:"-";var e=(r+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}function Bo(r,e){return r=(r||"").toLowerCase().replace(/-(.)/g,function(t,n){return n.toUpperCase()}),e&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var on=Mh;function Oi(r,e,t){var n="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function a(f){return f&&tn(f)?f:"-"}function i(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=e==="time",s=r instanceof Date;if(o||s){var l=o?We(r):r;if(isNaN(+l)){if(s)return"-"}else return an(l,n,t)}if(e==="ordinal")return xi(r)?a(r):oe(r)&&i(r)?r+"":"-";var u=Wr(r);return i(u)?No(u):xi(r)?a(r):typeof r=="boolean"?r+"":"-"}var Ys=["a","b","c","d","e","f","g"],Va=function(r,e){return"{"+r+(e??"")+"}"};function Fo(r,e,t){R(e)||(e=[e]);var n=e.length;if(!n)return"";for(var a=e[0].$vars||[],i=0;i<a.length;i++){var o=Ys[i];r=r.replace(Va(o),Va(o,0))}for(var s=0;s<n;s++)for(var l=0;l<a.length;l++){var u=e[s][a[l]];r=r.replace(Va(Ys[l],s),t?xe(u):u)}return r}function Jf(r,e){var t=B(r)?{color:r,extraCssText:e}:r||{},n=t.color,a=t.type;e=t.extraCssText;var i=t.renderMode||"html";if(!n)return"";if(i==="html")return a==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+xe(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+xe(n)+";"+(e||"")+'"></span>';var o=t.markerId||"markerX";return{renderMode:i,content:"{"+o+"|}  ",style:a==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}}function Op(r,e,t){(r==="week"||r==="month"||r==="quarter"||r==="half-year"||r==="year")&&(r=`MM-dd
yyyy`);var n=We(e),a=t?"getUTC":"get",i=n[a+"FullYear"](),o=n[a+"Month"]()+1,s=n[a+"Date"](),l=n[a+"Hours"](),u=n[a+"Minutes"](),f=n[a+"Seconds"](),c=n[a+"Milliseconds"]();return r=r.replace("MM",Se(o,2)).replace("M",o).replace("yyyy",i).replace("yy",Se(i%100+"",2)).replace("dd",Se(s,2)).replace("d",s).replace("hh",Se(l,2)).replace("h",l).replace("mm",Se(u,2)).replace("m",u).replace("ss",Se(f,2)).replace("s",f).replace("SSS",Se(c,3)),r}function Np(r){return r&&r.charAt(0).toUpperCase()+r.substr(1)}function Ht(r,e){return e=e||"transparent",B(r)?r:V(r)&&r.colorStops&&(r.colorStops[0]||{}).color||e}var Pn=b,Bp=["left","right","top","bottom","width","height"],un=[["width","left","right"],["height","top","bottom"]];function Go(r,e,t,n,a){var i=0,o=0;n==null&&(n=1/0),a==null&&(a=1/0);var s=0;e.eachChild(function(l,u){var f=l.getBoundingRect(),c=e.childAt(u+1),h=c&&c.getBoundingRect(),v,d;if(r==="horizontal"){var p=f.width+(h?-h.x+f.x:0);v=i+p,v>n||l.newline?(i=0,v=p,o+=s+t,s=f.height):s=Math.max(s,f.height)}else{var g=f.height+(h?-h.y+f.y:0);d=o+g,d>a||l.newline?(i+=s+t,o=0,d=g,s=f.width):s=Math.max(s,f.width)}l.newline||(l.x=i,l.y=o,l.markRedraw(),r==="horizontal"?i=v+t:o=d+t)})}var Nr=Go;le(Go,"vertical");le(Go,"horizontal");function $r(r,e,t){t=on(t||0);var n=e.width,a=e.height,i=ve(r.left,n),o=ve(r.top,a),s=ve(r.right,n),l=ve(r.bottom,a),u=ve(r.width,n),f=ve(r.height,a),c=t[2]+t[0],h=t[1]+t[3],v=r.aspect;switch(isNaN(u)&&(u=n-s-h-i),isNaN(f)&&(f=a-l-c-o),v!=null&&(isNaN(u)&&isNaN(f)&&(v>n/a?u=n*.8:f=a*.8),isNaN(u)&&(u=v*f),isNaN(f)&&(f=u/v)),isNaN(i)&&(i=n-s-u-h),isNaN(o)&&(o=a-l-f-c),r.left||r.right){case"center":i=n/2-u/2-t[3];break;case"right":i=n-u-h;break}switch(r.top||r.bottom){case"middle":case"center":o=a/2-f/2-t[0];break;case"bottom":o=a-f-c;break}i=i||0,o=o||0,isNaN(u)&&(u=n-h-i-(s||0)),isNaN(f)&&(f=a-c-o-(l||0));var d=new Ee(i+t[3],o+t[0],u,f);return d.margin=t,d}function Kr(r){var e=r.layoutMode||r.constructor.layoutMode;return V(e)?e:e?{type:e}:null}function ir(r,e,t){var n=t&&t.ignoreSize;!R(n)&&(n=[n,n]);var a=o(un[0],0),i=o(un[1],1);u(un[0],r,a),u(un[1],r,i);function o(f,c){var h={},v=0,d={},p=0,g=2;if(Pn(f,function(_){d[_]=r[_]}),Pn(f,function(_){s(e,_)&&(h[_]=d[_]=e[_]),l(h,_)&&v++,l(d,_)&&p++}),n[c])return l(e,f[1])?d[f[2]]=null:l(e,f[2])&&(d[f[1]]=null),d;if(p===g||!v)return d;if(v>=g)return h;for(var m=0;m<f.length;m++){var y=f[m];if(!s(h,y)&&s(r,y)){h[y]=r[y];break}}return h}function s(f,c){return f.hasOwnProperty(c)}function l(f,c){return f[c]!=null&&f[c]!=="auto"}function u(f,c,h){Pn(f,function(v){c[v]=h[v]})}}function xa(r){return Fp({},r)}function Fp(r,e){return e&&r&&Pn(Bp,function(t){e.hasOwnProperty(t)&&(r[t]=e[t])}),r}var Gp=re(),X=function(r){z(e,r);function e(t,n,a){var i=r.call(this,t,n,a)||this;return i.uid=ga("ec_cpt_model"),i}return e.prototype.init=function(t,n,a){this.mergeDefaultAndTheme(t,a)},e.prototype.mergeDefaultAndTheme=function(t,n){var a=Kr(this),i=a?xa(t):{},o=n.getTheme();j(t,o.get(this.mainType)),j(t,this.getDefaultOption()),a&&ir(t,i,a)},e.prototype.mergeOption=function(t,n){j(this.option,t,!0);var a=Kr(this);a&&ir(this.option,t,a)},e.prototype.optionUpdated=function(t,n){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!Cd(t))return t.defaultOption;var n=Gp(this);if(!n.defaultOption){for(var a=[],i=t;i;){var o=i.prototype.defaultOption;o&&a.push(o),i=i.superClass}for(var s={},l=a.length-1;l>=0;l--)s=j(s,a[l],!0);n.defaultOption=s}return n.defaultOption},e.prototype.getReferringComponents=function(t,n){var a=t+"Index",i=t+"Id";return nn(this.ecModel,t,{index:this.get(a,!0),id:this.get(i,!0)},n)},e.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},e.prototype.getZLevelKey=function(){return""},e.prototype.setZLevel=function(t){this.option.zlevel=t},e.protoInitialize=function(){var t=e.prototype;t.type="component",t.id="",t.name="",t.mainType="",t.subType="",t.componentIndex=0}(),e}(J);yf(X,J);ia(X);wp(X);Tp(X,Vp);function Vp(r){var e=[];return b(X.getClassesByMainType(r),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=F(e,function(t){return et(t).main}),r!=="dataset"&&ie(e,"dataset")<=0&&e.unshift("dataset"),e}var ec="";typeof navigator<"u"&&(ec=navigator.platform||"");var Zt="rgba(0, 0, 0, 0.2)";const zp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Zt,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Zt,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Zt,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Zt,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Zt,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Zt,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:ec.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var tc=W(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Ze="original",Ae="arrayRows",rt="objectRows",vt="keyedColumns",xt="typedArray",rc="unknown",st="column",fr="row",Pe={Must:1,Might:2,Not:3},nc=re();function Hp(r){nc(r).datasetMap=W()}function Wp(r,e,t){var n={},a=ac(e);if(!a||!r)return n;var i=[],o=[],s=e.ecModel,l=nc(s).datasetMap,u=a.uid+"_"+t.seriesLayoutBy,f,c;r=r.slice(),b(r,function(p,g){var m=V(p)?p:r[g]={name:p};m.type==="ordinal"&&f==null&&(f=g,c=d(m)),n[m.name]=[]});var h=l.get(u)||l.set(u,{categoryWayDim:c,valueWayDim:0});b(r,function(p,g){var m=p.name,y=d(p);if(f==null){var _=h.valueWayDim;v(n[m],_,y),v(o,_,y),h.valueWayDim+=y}else if(f===g)v(n[m],0,y),v(i,0,y);else{var _=h.categoryWayDim;v(n[m],_,y),v(o,_,y),h.categoryWayDim+=y}});function v(p,g,m){for(var y=0;y<m;y++)p.push(g+y)}function d(p){var g=p.dimsDef;return g?g.length:1}return i.length&&(n.itemName=i),o.length&&(n.seriesName=o),n}function ac(r){var e=r.get("data",!0);if(!e)return nn(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},Qe).models[0]}function Up(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:nn(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},Qe).models}function ic(r,e){return Yp(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,e)}function Yp(r,e,t,n,a,i){var o,s=5;if(Re(r))return Pe.Not;var l,u;if(n){var f=n[i];V(f)?(l=f.name,u=f.type):B(f)&&(l=f)}if(u!=null)return u==="ordinal"?Pe.Must:Pe.Not;if(e===Ae){var c=r;if(t===fr){for(var h=c[i],v=0;v<(h||[]).length&&v<s;v++)if((o=S(h[a+v]))!=null)return o}else for(var v=0;v<c.length&&v<s;v++){var d=c[a+v];if(d&&(o=S(d[i]))!=null)return o}}else if(e===rt){var p=r;if(!l)return Pe.Not;for(var v=0;v<p.length&&v<s;v++){var g=p[v];if(g&&(o=S(g[l]))!=null)return o}}else if(e===vt){var m=r;if(!l)return Pe.Not;var h=m[l];if(!h||Re(h))return Pe.Not;for(var v=0;v<h.length&&v<s;v++)if((o=S(h[v]))!=null)return o}else if(e===Ze)for(var y=r,v=0;v<y.length&&v<s;v++){var g=y[v],_=rn(g);if(!R(_))return Pe.Not;if((o=S(_[i]))!=null)return o}function S(w){var x=B(w);if(w!=null&&Number.isFinite(Number(w))&&w!=="")return x?Pe.Might:Pe.Not;if(x&&w!=="-")return Pe.Must}return Pe.Not}var Xp=W();function Zp(r,e,t){var n=Xp.get(e);if(!n)return t;var a=n(r);return a?t.concat(a):t}var Xs=re();re();var Vo=function(){function r(){}return r.prototype.getColorFromPalette=function(e,t,n){var a=be(this.get("color",!0)),i=this.get("colorLayer",!0);return Kp(this,Xs,a,i,e,t,n)},r.prototype.clearColorPalette=function(){qp(this,Xs)},r}();function $p(r,e){for(var t=r.length,n=0;n<t;n++)if(r[n].length>e)return r[n];return r[t-1]}function Kp(r,e,t,n,a,i,o){i=i||r;var s=e(i),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(a))return u[a];var f=o==null||!n?t:$p(n,o);if(f=f||t,!(!f||!f.length)){var c=f[l];return a&&(u[a]=c),s.paletteIdx=(l+1)%f.length,c}}function qp(r,e){e(r).paletteIdx=0,e(r).paletteNameMap={}}var fn,dr,Zs,$s="\0_ec_inner",jp=1,zo=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.init=function(t,n,a,i,o,s){i=i||{},this.option=null,this._theme=new J(i),this._locale=new J(o),this._optionManager=s},e.prototype.setOption=function(t,n,a){var i=js(n);this._optionManager.setOption(t,a,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,n){return this._resetOption(t,js(n))},e.prototype._resetOption=function(t,n){var a=!1,i=this._optionManager;if(!t||t==="recreate"){var o=i.mountOption(t==="recreate");!this.option||t==="recreate"?Zs(this,o):(this.restoreData(),this._mergeOption(o,n)),a=!0}if((t==="timeline"||t==="media")&&this.restoreData(),!t||t==="recreate"||t==="timeline"){var s=i.getTimelineOption(this);s&&(a=!0,this._mergeOption(s,n))}if(!t||t==="recreate"||t==="media"){var l=i.getMediaOption(this);l.length&&b(l,function(u){a=!0,this._mergeOption(u,n)},this)}return a},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,n){var a=this.option,i=this._componentsMap,o=this._componentsCount,s=[],l=W(),u=n&&n.replaceMergeMainTypeMap;Hp(this),b(t,function(c,h){c!=null&&(X.hasClass(h)?h&&(s.push(h),l.set(h,!0)):a[h]=a[h]==null?$(c):j(a[h],c,!0))}),u&&u.each(function(c,h){X.hasClass(h)&&!l.get(h)&&(s.push(h),l.set(h,!0))}),X.topologicalTravel(s,X.getAllClassMainTypes(),f,this);function f(c){var h=Zp(this,c,be(t[c])),v=i.get(c),d=v?u&&u.get(c)?"replaceMerge":"normalMerge":"replaceAll",p=cd(v,h,d);yd(p,c,X),a[c]=null,i.set(c,null),o.set(c,0);var g=[],m=[],y=0,_;b(p,function(S,w){var x=S.existing,T=S.newOption;if(!T)x&&(x.mergeOption({},this),x.optionUpdated({},!1));else{var A=c==="series",M=X.getClass(c,S.keyInfo.subType,!A);if(!M)return;if(c==="tooltip"){if(_)return;_=!0}if(x&&x.constructor===M)x.name=S.keyInfo.name,x.mergeOption(T,this),x.optionUpdated(T,!1);else{var D=N({componentIndex:w},S.keyInfo);x=new M(T,this,this,D),N(x,D),S.brandNew&&(x.__requireNewView=!0),x.init(T,this,this),x.optionUpdated(null,!0)}}x?(g.push(x.option),m.push(x),y++):(g.push(void 0),m.push(void 0))},this),a[c]=g,i.set(c,m),o.set(c,y),c==="series"&&fn(this)}this._seriesIndices||fn(this)},e.prototype.getOption=function(){var t=$(this.option);return b(t,function(n,a){if(X.hasClass(a)){for(var i=be(n),o=i.length,s=!1,l=o-1;l>=0;l--)i[l]&&!Ur(i[l])?s=!0:(i[l]=null,!s&&o--);i.length=o,t[a]=i}}),delete t[$s],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,n){var a=this._componentsMap.get(t);if(a){var i=a[n||0];if(i)return i;if(n==null){for(var o=0;o<a.length;o++)if(a[o])return a[o]}}},e.prototype.queryComponents=function(t){var n=t.mainType;if(!n)return[];var a=t.index,i=t.id,o=t.name,s=this._componentsMap.get(n);if(!s||!s.length)return[];var l;return a!=null?(l=[],b(be(a),function(u){s[u]&&l.push(s[u])})):i!=null?l=Ks("id",i,s):o!=null?l=Ks("name",o,s):l=se(s,function(u){return!!u}),qs(l,t)},e.prototype.findComponents=function(t){var n=t.query,a=t.mainType,i=s(n),o=i?this.queryComponents(i):se(this._componentsMap.get(a),function(u){return!!u});return l(qs(o,t));function s(u){var f=a+"Index",c=a+"Id",h=a+"Name";return u&&(u[f]!=null||u[c]!=null||u[h]!=null)?{mainType:a,index:u[f],id:u[c],name:u[h]}:null}function l(u){return t.filter?se(u,t.filter):u}},e.prototype.eachComponent=function(t,n,a){var i=this._componentsMap;if(H(t)){var o=n,s=t;i.each(function(c,h){for(var v=0;c&&v<c.length;v++){var d=c[v];d&&s.call(o,h,d,d.componentIndex)}})}else for(var l=B(t)?i.get(t):V(t)?this.findComponents(t):null,u=0;l&&u<l.length;u++){var f=l[u];f&&n.call(a,f,f.componentIndex)}},e.prototype.getSeriesByName=function(t){var n=tt(t,null);return se(this._componentsMap.get("series"),function(a){return!!a&&n!=null&&a.name===n})},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return se(this._componentsMap.get("series"),function(n){return!!n&&n.subType===t})},e.prototype.getSeries=function(){return se(this._componentsMap.get("series"),function(t){return!!t})},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,n){dr(this),b(this._seriesIndices,function(a){var i=this._componentsMap.get("series")[a];t.call(n,i,a)},this)},e.prototype.eachRawSeries=function(t,n){b(this._componentsMap.get("series"),function(a){a&&t.call(n,a,a.componentIndex)})},e.prototype.eachSeriesByType=function(t,n,a){dr(this),b(this._seriesIndices,function(i){var o=this._componentsMap.get("series")[i];o.subType===t&&n.call(a,o,i)},this)},e.prototype.eachRawSeriesByType=function(t,n,a){return b(this.getSeriesByType(t),n,a)},e.prototype.isSeriesFiltered=function(t){return dr(this),this._seriesIndicesMap.get(t.componentIndex)==null},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,n){dr(this);var a=[];b(this._seriesIndices,function(i){var o=this._componentsMap.get("series")[i];t.call(n,o,i)&&a.push(i)},this),this._seriesIndices=a,this._seriesIndicesMap=W(a)},e.prototype.restoreData=function(t){fn(this);var n=this._componentsMap,a=[];n.each(function(i,o){X.hasClass(o)&&a.push(o)}),X.topologicalTravel(a,X.getAllClassMainTypes(),function(i){b(n.get(i),function(o){o&&(i!=="series"||!Qp(o,t))&&o.restoreData()})})},e.internalField=function(){fn=function(t){var n=t._seriesIndices=[];b(t._componentsMap.get("series"),function(a){a&&n.push(a.componentIndex)}),t._seriesIndicesMap=W(n)},dr=function(t){},Zs=function(t,n){t.option={},t.option[$s]=jp,t._componentsMap=W({series:[]}),t._componentsCount=W();var a=n.aria;V(a)&&a.enabled==null&&(a.enabled=!0),Jp(n,t._theme.option),j(n,zp,!1),t._mergeOption(n,null)}}(),e}(J);function Qp(r,e){if(e){var t=e.seriesIndex,n=e.seriesId,a=e.seriesName;return t!=null&&r.componentIndex!==t||n!=null&&r.id!==n||a!=null&&r.name!==a}}function Jp(r,e){var t=r.color&&!r.colorLayer;b(e,function(n,a){a==="colorLayer"&&t||X.hasClass(a)||(typeof n=="object"?r[a]=r[a]?j(r[a],n,!1):$(n):r[a]==null&&(r[a]=n))})}function Ks(r,e,t){if(R(e)){var n=W();return b(e,function(i){if(i!=null){var o=tt(i,null);o!=null&&n.set(i,!0)}}),se(t,function(i){return i&&n.get(i[r])})}else{var a=tt(e,null);return se(t,function(i){return i&&a!=null&&i[r]===a})}}function qs(r,e){return e.hasOwnProperty("subType")?se(r,function(t){return t&&t.subType===e.subType}):r}function js(r){var e=W();return r&&b(be(r.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}ft(zo,Vo);var eg=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],oc=function(){function r(e){b(eg,function(t){this[t]=Q(e[t],e)},this)}return r}(),za={},ba=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(e,t){var n=[];b(za,function(a,i){var o=a.create(e,t);n=n.concat(o||[])}),this._coordinateSystems=n},r.prototype.update=function(e,t){b(this._coordinateSystems,function(n){n.update&&n.update(e,t)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(e,t){za[e]=t},r.get=function(e){return za[e]},r}(),tg=/^(min|max)?(.+)$/,rg=function(){function r(e){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=e}return r.prototype.setOption=function(e,t,n){e&&(b(be(e.series),function(o){o&&o.data&&Re(o.data)&&bi(o.data)}),b(be(e.dataset),function(o){o&&o.source&&Re(o.source)&&bi(o.source)})),e=$(e);var a=this._optionBackup,i=ng(e,t,!a);this._newBaseOption=i.baseOption,a?(i.timelineOptions.length&&(a.timelineOptions=i.timelineOptions),i.mediaList.length&&(a.mediaList=i.mediaList),i.mediaDefault&&(a.mediaDefault=i.mediaDefault)):this._optionBackup=i},r.prototype.mountOption=function(e){var t=this._optionBackup;return this._timelineOptions=t.timelineOptions,this._mediaList=t.mediaList,this._mediaDefault=t.mediaDefault,this._currentMediaIndices=[],$(e?t.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(e){var t,n=this._timelineOptions;if(n.length){var a=e.getComponent("timeline");a&&(t=$(n[a.getCurrentIndex()]))}return t},r.prototype.getMediaOption=function(e){var t=this._api.getWidth(),n=this._api.getHeight(),a=this._mediaList,i=this._mediaDefault,o=[],s=[];if(!a.length&&!i)return s;for(var l=0,u=a.length;l<u;l++)ag(a[l].query,t,n)&&o.push(l);return!o.length&&i&&(o=[-1]),o.length&&!og(o,this._currentMediaIndices)&&(s=F(o,function(f){return $(f===-1?i.option:a[f].option)})),this._currentMediaIndices=o,s},r}();function ng(r,e,t){var n=[],a,i,o=r.baseOption,s=r.timeline,l=r.options,u=r.media,f=!!r.media,c=!!(l||s||o&&o.timeline);o?(i=o,i.timeline||(i.timeline=s)):((c||f)&&(r.options=r.media=null),i=r),f&&R(u)&&b(u,function(v){v&&v.option&&(v.query?n.push(v):a||(a=v))}),h(i),b(l,function(v){return h(v)}),b(n,function(v){return h(v.option)});function h(v){b(e,function(d){d(v,t)})}return{baseOption:i,timelineOptions:l||[],mediaDefault:a,mediaList:n}}function ag(r,e,t){var n={width:e,height:t,aspectratio:e/t},a=!0;return b(r,function(i,o){var s=o.match(tg);if(!(!s||!s[1]||!s[2])){var l=s[1],u=s[2].toLowerCase();ig(n[u],i,l)||(a=!1)}}),a}function ig(r,e,t){return t==="min"?r>=e:t==="max"?r<=e:r===e}function og(r,e){return r.join(",")===e.join(",")}var $e=b,qr=V,Qs=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ha(r){var e=r&&r.itemStyle;if(e)for(var t=0,n=Qs.length;t<n;t++){var a=Qs[t],i=e.normal,o=e.emphasis;i&&i[a]&&(r[a]=r[a]||{},r[a].normal?j(r[a].normal,i[a]):r[a].normal=i[a],i[a]=null),o&&o[a]&&(r[a]=r[a]||{},r[a].emphasis?j(r[a].emphasis,o[a]):r[a].emphasis=o[a],o[a]=null)}}function ge(r,e,t){if(r&&r[e]&&(r[e].normal||r[e].emphasis)){var n=r[e].normal,a=r[e].emphasis;n&&(t?(r[e].normal=r[e].emphasis=null,ee(r[e],n)):r[e]=n),a&&(r.emphasis=r.emphasis||{},r.emphasis[e]=a,a.focus&&(r.emphasis.focus=a.focus),a.blurScope&&(r.emphasis.blurScope=a.blurScope))}}function Lr(r){ge(r,"itemStyle"),ge(r,"lineStyle"),ge(r,"areaStyle"),ge(r,"label"),ge(r,"labelLine"),ge(r,"upperLabel"),ge(r,"edgeLabel")}function ne(r,e){var t=qr(r)&&r[e],n=qr(t)&&t.textStyle;if(n)for(var a=0,i=Ts.length;a<i;a++){var o=Ts[a];n.hasOwnProperty(o)&&(t[o]=n[o])}}function Ve(r){r&&(Lr(r),ne(r,"label"),r.emphasis&&ne(r.emphasis,"label"))}function sg(r){if(qr(r)){Ha(r),Lr(r),ne(r,"label"),ne(r,"upperLabel"),ne(r,"edgeLabel"),r.emphasis&&(ne(r.emphasis,"label"),ne(r.emphasis,"upperLabel"),ne(r.emphasis,"edgeLabel"));var e=r.markPoint;e&&(Ha(e),Ve(e));var t=r.markLine;t&&(Ha(t),Ve(t));var n=r.markArea;n&&Ve(n);var a=r.data;if(r.type==="graph"){a=a||r.nodes;var i=r.links||r.edges;if(i&&!Re(i))for(var o=0;o<i.length;o++)Ve(i[o]);b(r.categories,function(u){Lr(u)})}if(a&&!Re(a))for(var o=0;o<a.length;o++)Ve(a[o]);if(e=r.markPoint,e&&e.data)for(var s=e.data,o=0;o<s.length;o++)Ve(s[o]);if(t=r.markLine,t&&t.data)for(var l=t.data,o=0;o<l.length;o++)R(l[o])?(Ve(l[o][0]),Ve(l[o][1])):Ve(l[o]);r.type==="gauge"?(ne(r,"axisLabel"),ne(r,"title"),ne(r,"detail")):r.type==="treemap"?(ge(r.breadcrumb,"itemStyle"),b(r.levels,function(u){Lr(u)})):r.type==="tree"&&Lr(r.leaves)}}function at(r){return R(r)?r:r?[r]:[]}function Js(r){return(R(r)?r[0]:r)||{}}function lg(r,e){$e(at(r.series),function(n){qr(n)&&sg(n)});var t=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&t.push("valueAxis","categoryAxis","logAxis","timeAxis"),$e(t,function(n){$e(at(r[n]),function(a){a&&(ne(a,"axisLabel"),ne(a.axisPointer,"label"))})}),$e(at(r.parallel),function(n){var a=n&&n.parallelAxisDefault;ne(a,"axisLabel"),ne(a&&a.axisPointer,"label")}),$e(at(r.calendar),function(n){ge(n,"itemStyle"),ne(n,"dayLabel"),ne(n,"monthLabel"),ne(n,"yearLabel")}),$e(at(r.radar),function(n){ne(n,"name"),n.name&&n.axisName==null&&(n.axisName=n.name,delete n.name),n.nameGap!=null&&n.axisNameGap==null&&(n.axisNameGap=n.nameGap,delete n.nameGap)}),$e(at(r.geo),function(n){qr(n)&&(Ve(n),$e(at(n.regions),function(a){Ve(a)}))}),$e(at(r.timeline),function(n){Ve(n),ge(n,"label"),ge(n,"itemStyle"),ge(n,"controlStyle",!0);var a=n.data;R(a)&&b(a,function(i){V(i)&&(ge(i,"label"),ge(i,"itemStyle"))})}),$e(at(r.toolbox),function(n){ge(n,"iconStyle"),$e(n.feature,function(a){ge(a,"iconStyle")})}),ne(Js(r.axisPointer),"label"),ne(Js(r.tooltip).axisPointer,"label")}function ug(r,e){for(var t=e.split(","),n=r,a=0;a<t.length&&(n=n&&n[t[a]],n!=null);a++);return n}function fg(r,e,t,n){for(var a=e.split(","),i=r,o,s=0;s<a.length-1;s++)o=a[s],i[o]==null&&(i[o]={}),i=i[o];i[a[s]]==null&&(i[a[s]]=t)}function el(r){r&&b(cg,function(e){e[0]in r&&!(e[1]in r)&&(r[e[1]]=r[e[0]])})}var cg=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],vg=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Wa=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function pr(r){var e=r&&r.itemStyle;if(e)for(var t=0;t<Wa.length;t++){var n=Wa[t][1],a=Wa[t][0];e[n]!=null&&(e[a]=e[n])}}function tl(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function rl(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function hg(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function sc(r,e){if(r)for(var t=0;t<r.length;t++)e(r[t]),r[t]&&sc(r[t].children,e)}function lc(r,e){lg(r,e),r.series=be(r.series),b(r.series,function(t){if(V(t)){var n=t.type;if(n==="line")t.clipOverflow!=null&&(t.clip=t.clipOverflow);else if(n==="pie"||n==="gauge"){t.clockWise!=null&&(t.clockwise=t.clockWise),tl(t.label);var a=t.data;if(a&&!Re(a))for(var i=0;i<a.length;i++)tl(a[i]);t.hoverOffset!=null&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if(n==="gauge"){var o=ug(t,"pointer.color");o!=null&&fg(t,"itemStyle.color",o)}else if(n==="bar"){pr(t),pr(t.backgroundStyle),pr(t.emphasis);var a=t.data;if(a&&!Re(a))for(var i=0;i<a.length;i++)typeof a[i]=="object"&&(pr(a[i]),pr(a[i]&&a[i].emphasis))}else if(n==="sunburst"){var s=t.highlightPolicy;s&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=s)),rl(t),sc(t.data,rl)}else n==="graph"||n==="sankey"?hg(t):n==="map"&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&ee(t,t.mapLocation));t.hoverAnimation!=null&&(t.emphasis=t.emphasis||{},t.emphasis&&t.emphasis.scale==null&&(t.emphasis.scale=t.hoverAnimation)),el(t)}}),r.dataRange&&(r.visualMap=r.dataRange),b(vg,function(t){var n=r[t];n&&(R(n)||(n=[n]),b(n,function(a){el(a)}))})}function dg(r){var e=W();r.eachSeries(function(t){var n=t.get("stack");if(n){var a=e.get(n)||e.set(n,[]),i=t.getData(),o={stackResultDimension:i.getCalculationInfo("stackResultDimension"),stackedOverDimension:i.getCalculationInfo("stackedOverDimension"),stackedDimension:i.getCalculationInfo("stackedDimension"),stackedByDimension:i.getCalculationInfo("stackedByDimension"),isStackedByIndex:i.getCalculationInfo("isStackedByIndex"),data:i,seriesModel:t};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;a.length&&i.setCalculationInfo("stackedOnSeries",a[a.length-1].seriesModel),a.push(o)}}),e.each(pg)}function pg(r){b(r,function(e,t){var n=[],a=[NaN,NaN],i=[e.stackResultDimension,e.stackedOverDimension],o=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";o.modify(i,function(u,f,c){var h=o.get(e.stackedDimension,c);if(isNaN(h))return a;var v,d;s?d=o.getRawIndex(c):v=o.get(e.stackedByDimension,c);for(var p=NaN,g=t-1;g>=0;g--){var m=r[g];if(s||(d=m.data.rawIndexOf(m.stackedByDimension,v)),d>=0){var y=m.data.getByRawIndex(m.stackResultDimension,d);if(l==="all"||l==="positive"&&y>0||l==="negative"&&y<0||l==="samesign"&&h>=0&&y>0||l==="samesign"&&h<=0&&y<0){h=ad(h,y),p=y;break}}}return n[0]=h,n[1]=p,n})})}var wa=function(){function r(e){this.data=e.data||(e.sourceFormat===vt?{}:[]),this.sourceFormat=e.sourceFormat||rc,this.seriesLayoutBy=e.seriesLayoutBy||st,this.startIndex=e.startIndex||0,this.dimensionsDetectedCount=e.dimensionsDetectedCount,this.metaRawOption=e.metaRawOption;var t=this.dimensionsDefine=e.dimensionsDefine;if(t)for(var n=0;n<t.length;n++){var a=t[n];a.type==null&&ic(this,n)===Pe.Must&&(a.type="ordinal")}}return r}();function Ho(r){return r instanceof wa}function Ni(r,e,t){t=t||fc(r);var n=e.seriesLayoutBy,a=mg(r,t,n,e.sourceHeader,e.dimensions),i=new wa({data:r,sourceFormat:t,seriesLayoutBy:n,dimensionsDefine:a.dimensionsDefine,startIndex:a.startIndex,dimensionsDetectedCount:a.dimensionsDetectedCount,metaRawOption:$(e)});return i}function uc(r){return new wa({data:r,sourceFormat:Re(r)?xt:Ze})}function gg(r){return new wa({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:$(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function fc(r){var e=rc;if(Re(r))e=xt;else if(R(r)){r.length===0&&(e=Ae);for(var t=0,n=r.length;t<n;t++){var a=r[t];if(a!=null){if(R(a)||Re(a)){e=Ae;break}else if(V(a)){e=rt;break}}}}else if(V(r)){for(var i in r)if(Gt(r,i)&&Qn(r[i])){e=vt;break}}return e}function mg(r,e,t,n,a){var i,o;if(!r)return{dimensionsDefine:nl(a),startIndex:o,dimensionsDetectedCount:i};if(e===Ae){var s=r;n==="auto"||n==null?al(function(u){u!=null&&u!=="-"&&(B(u)?o==null&&(o=1):o=0)},t,s,10):o=oe(n)?n:n?1:0,!a&&o===1&&(a=[],al(function(u,f){a[f]=u!=null?u+"":""},t,s,1/0)),i=a?a.length:t===fr?s.length:s[0]?s[0].length:null}else if(e===rt)a||(a=yg(r));else if(e===vt)a||(a=[],b(r,function(u,f){a.push(f)}));else if(e===Ze){var l=rn(r[0]);i=R(l)&&l.length||1}return{startIndex:o,dimensionsDefine:nl(a),dimensionsDetectedCount:i}}function yg(r){for(var e=0,t;e<r.length&&!(t=r[e++]););if(t)return Ye(t)}function nl(r){if(r){var e=W();return F(r,function(t,n){t=V(t)?t:{name:t};var a={name:t.name,displayName:t.displayName,type:t.type};if(a.name==null)return a;a.name+="",a.displayName==null&&(a.displayName=a.name);var i=e.get(a.name);return i?a.name+="-"+i.count++:e.set(a.name,{count:1}),a})}}function al(r,e,t,n){if(e===fr)for(var a=0;a<t.length&&a<n;a++)r(t[a]?t[a][0]:null,a);else for(var i=t[0]||[],a=0;a<i.length&&a<n;a++)r(i[a],a)}function cc(r){var e=r.sourceFormat;return e===rt||e===vt}var At,It,Lt,il,ol,vc=function(){function r(e,t){var n=Ho(e)?e:uc(e);this._source=n;var a=this._data=n.data;n.sourceFormat===xt&&(this._offset=0,this._dimSize=t,this._data=a),ol(this,a,n)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(e,t){},r.prototype.appendData=function(e){},r.prototype.clean=function(){},r.protoInitialize=function(){var e=r.prototype;e.pure=!1,e.persistent=!0}(),r.internalField=function(){var e;ol=function(o,s,l){var u=l.sourceFormat,f=l.seriesLayoutBy,c=l.startIndex,h=l.dimensionsDefine,v=il[Wo(u,f)];if(N(o,v),u===xt)o.getItem=t,o.count=a,o.fillStorage=n;else{var d=hc(u,f);o.getItem=Q(d,null,s,c,h);var p=dc(u,f);o.count=Q(p,null,s,c,h)}};var t=function(o,s){o=o-this._offset,s=s||[];for(var l=this._data,u=this._dimSize,f=u*o,c=0;c<u;c++)s[c]=l[f+c];return s},n=function(o,s,l,u){for(var f=this._data,c=this._dimSize,h=0;h<c;h++){for(var v=u[h],d=v[0]==null?1/0:v[0],p=v[1]==null?-1/0:v[1],g=s-o,m=l[h],y=0;y<g;y++){var _=f[y*c+h];m[o+y]=_,_<d&&(d=_),_>p&&(p=_)}v[0]=d,v[1]=p}},a=function(){return this._data?this._data.length/this._dimSize:0};il=(e={},e[Ae+"_"+st]={pure:!0,appendData:i},e[Ae+"_"+fr]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},e[rt]={pure:!0,appendData:i},e[vt]={pure:!0,appendData:function(o){var s=this._data;b(o,function(l,u){for(var f=s[u]||(s[u]=[]),c=0;c<(l||[]).length;c++)f.push(l[c])})}},e[Ze]={appendData:i},e[xt]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},e);function i(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),sl=function(r,e,t,n){return r[n]},_g=(At={},At[Ae+"_"+st]=function(r,e,t,n){return r[n+e]},At[Ae+"_"+fr]=function(r,e,t,n,a){n+=e;for(var i=a||[],o=r,s=0;s<o.length;s++){var l=o[s];i[s]=l?l[n]:null}return i},At[rt]=sl,At[vt]=function(r,e,t,n,a){for(var i=a||[],o=0;o<t.length;o++){var s=t[o].name,l=r[s];i[o]=l?l[n]:null}return i},At[Ze]=sl,At);function hc(r,e){var t=_g[Wo(r,e)];return t}var ll=function(r,e,t){return r.length},Sg=(It={},It[Ae+"_"+st]=function(r,e,t){return Math.max(0,r.length-e)},It[Ae+"_"+fr]=function(r,e,t){var n=r[0];return n?Math.max(0,n.length-e):0},It[rt]=ll,It[vt]=function(r,e,t){var n=t[0].name,a=r[n];return a?a.length:0},It[Ze]=ll,It);function dc(r,e){var t=Sg[Wo(r,e)];return t}var Ua=function(r,e,t){return r[e]},xg=(Lt={},Lt[Ae]=Ua,Lt[rt]=function(r,e,t){return r[t]},Lt[vt]=Ua,Lt[Ze]=function(r,e,t){var n=rn(r);return n instanceof Array?n[e]:n},Lt[xt]=Ua,Lt);function pc(r){var e=xg[r];return e}function Wo(r,e){return r===Ae?r+"_"+e:r}function or(r,e,t){if(r){var n=r.getRawDataItem(e);if(n!=null){var a=r.getStore(),i=a.getSource().sourceFormat;if(t!=null){var o=r.getDimensionIndex(t),s=a.getDimensionProperty(o);return pc(i)(n,o,s)}else{var l=n;return i===Ze&&(l=rn(n)),l}}}}var bg=/\{@(.+?)\}/g,wg=function(){function r(){}return r.prototype.getDataParams=function(e,t){var n=this.getData(t),a=this.getRawValue(e,t),i=n.getRawIndex(e),o=n.getName(e),s=n.getRawDataItem(e),l=n.getItemVisual(e,"style"),u=l&&l[n.getItemVisual(e,"drawType")||"fill"],f=l&&l.stroke,c=this.mainType,h=c==="series",v=n.userOutput&&n.userOutput.get();return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:h?this.subType:null,seriesIndex:this.seriesIndex,seriesId:h?this.id:null,seriesName:h?this.name:null,name:o,dataIndex:i,data:s,dataType:t,value:a,color:u,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(e,t,n,a,i,o){t=t||"normal";var s=this.getData(n),l=this.getDataParams(e,n);if(o&&(l.value=o.interpolatedValue),a!=null&&R(l.value)&&(l.value=l.value[a]),!i){var u=s.getItemModel(e);i=u.get(t==="normal"?["label","formatter"]:[t,"label","formatter"])}if(H(i))return l.status=t,l.dimensionIndex=a,i(l);if(B(i)){var f=Fo(i,l);return f.replace(bg,function(c,h){var v=h.length,d=h;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var p=or(s,e,d);if(o&&R(o.interpolatedValue)){var g=s.getDimensionIndex(d);g>=0&&(p=o.interpolatedValue[g])}return p!=null?p+"":""})}},r.prototype.getRawValue=function(e,t){return or(this.getData(t),e)},r.prototype.formatTooltip=function(e,t,n){},r}();function ul(r){var e,t;return V(r)?r.type&&(t=r):e=r,{text:e,frag:t}}function Br(r){return new Tg(r)}var Tg=function(){function r(e){e=e||{},this._reset=e.reset,this._plan=e.plan,this._count=e.count,this._onDirty=e.onDirty,this._dirty=!0}return r.prototype.perform=function(e){var t=this._upstream,n=e&&e.skip;if(this._dirty&&t){var a=this.context;a.data=a.outputData=t.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var i;this._plan&&!n&&(i=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,l=f(e&&e.modBy),u=e&&e.modDataCount||0;(o!==l||s!==u)&&(i="reset");function f(y){return!(y>=1)&&(y=1),y}var c;(this._dirty||i==="reset")&&(this._dirty=!1,c=this._doReset(n)),this._modBy=l,this._modDataCount=u;var h=e&&e.step;if(t?this._dueEnd=t._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(h!=null?this._dueIndex+h:1/0,this._dueEnd);if(!n&&(c||v<d)){var p=this._progress;if(R(p))for(var g=0;g<p.length;g++)this._doProgress(p[g],v,d,l,u);else this._doProgress(p,v,d,l,u)}this._dueIndex=d;var m=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=m}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(e,t,n,a,i){fl.reset(t,n,a,i),this._callingProgress=e,this._callingProgress({start:t,end:n,count:n-t,next:fl.next},this.context)},r.prototype._doReset=function(e){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var t,n;!e&&this._reset&&(t=this._reset(this.context),t&&t.progress&&(n=t.forceFirstProgress,t=t.progress),R(t)&&!t.length&&(t=null)),this._progress=t,this._modBy=this._modDataCount=null;var a=this._downstream;return a&&a.dirty(),n},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(e){(this._downstream!==e||this._dirty)&&(this._downstream=e,e._upstream=this,e.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(e){this._outputDueEnd=this._settedOutputEnd=e},r}(),fl=function(){var r,e,t,n,a,i={reset:function(l,u,f,c){e=l,r=u,t=f,n=c,a=Math.ceil(n/t),i.next=t>1&&n>0?s:o}};return i;function o(){return e<r?e++:null}function s(){var l=e%a*t+Math.ceil(e/a),u=e>=r?null:l<n?l:e;return e++,u}}();function kn(r,e){var t=e&&e.type;return t==="ordinal"?r:(t==="time"&&!oe(r)&&r!=null&&r!=="-"&&(r=+We(r)),r==null||r===""?NaN:Number(r))}W({number:function(r){return parseFloat(r)},time:function(r){return+We(r)},trim:function(r){return B(r)?tn(r):r}});var Cg=function(){function r(e,t){var n=e==="desc";this._resultLT=n?1:-1,t==null&&(t=n?"min":"max"),this._incomparable=t==="min"?-1/0:1/0}return r.prototype.evaluate=function(e,t){var n=oe(e)?e:Wr(e),a=oe(t)?t:Wr(t),i=isNaN(n),o=isNaN(a);if(i&&(n=this._incomparable),o&&(a=this._incomparable),i&&o){var s=B(e),l=B(t);s&&(n=l?e:0),l&&(a=s?t:0)}return n<a?this._resultLT:n>a?-this._resultLT:0},r}(),Dg=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(e){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(e){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(e,t){},r.prototype.retrieveValueFromItem=function(e,t){},r.prototype.convertValue=function(e,t){return kn(e,t)},r}();function Mg(r,e){var t=new Dg,n=r.data,a=t.sourceFormat=r.sourceFormat,i=r.startIndex,o="";r.seriesLayoutBy!==st&&De(o);var s=[],l={},u=r.dimensionsDefine;if(u)b(u,function(p,g){var m=p.name,y={index:g,name:m,displayName:p.displayName};if(s.push(y),m!=null){var _="";Gt(l,m)&&De(_),l[m]=y}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var c=hc(a,st);e.__isBuiltIn&&(t.getRawDataItem=function(p){return c(n,i,s,p)},t.getRawData=Q(Ag,null,r)),t.cloneRawData=Q(Ig,null,r);var h=dc(a,st);t.count=Q(h,null,n,i,s);var v=pc(a);t.retrieveValue=function(p,g){var m=c(n,i,s,p);return d(m,g)};var d=t.retrieveValueFromItem=function(p,g){if(p!=null){var m=s[g];if(m)return v(p,g,m.name)}};return t.getDimensionInfo=Q(Lg,null,s,l),t.cloneAllDimensionInfo=Q(Pg,null,s),t}function Ag(r){var e=r.sourceFormat;if(!Uo(e)){var t="";De(t)}return r.data}function Ig(r){var e=r.sourceFormat,t=r.data;if(!Uo(e)){var n="";De(n)}if(e===Ae){for(var a=[],i=0,o=t.length;i<o;i++)a.push(t[i].slice());return a}else if(e===rt){for(var a=[],i=0,o=t.length;i<o;i++)a.push(N({},t[i]));return a}}function Lg(r,e,t){if(t!=null){if(oe(t)||!isNaN(t)&&!Gt(e,t))return r[t];if(Gt(e,t))return e[t]}}function Pg(r){return $(r)}var gc=W();function kg(r){r=$(r);var e=r.type,t="";e||De(t);var n=e.split(":");n.length!==2&&De(t);var a=!1;n[0]==="echarts"&&(e=n[1],a=!0),r.__isBuiltIn=a,gc.set(e,r)}function Eg(r,e,t){var n=be(r),a=n.length,i="";a||De(i);for(var o=0,s=a;o<s;o++){var l=n[o];e=Rg(l,e),o!==s-1&&(e.length=Math.max(e.length,1))}return e}function Rg(r,e,t,n){var a="";e.length||De(a),V(r)||De(a);var i=r.type,o=gc.get(i);o||De(a);var s=F(e,function(u){return Mg(u,o)}),l=be(o.transform({upstream:s[0],upstreamList:s,config:$(r.config)}));return F(l,function(u,f){var c="";V(u)||De(c),u.data||De(c);var h=fc(u.data);Uo(h)||De(c);var v,d=e[0];if(d&&f===0&&!u.dimensions){var p=d.startIndex;p&&(u.data=d.data.slice(0,p).concat(u.data)),v={seriesLayoutBy:st,sourceHeader:p,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:st,sourceHeader:0,dimensions:u.dimensions};return Ni(u.data,v,null)})}function Uo(r){return r===Ae||r===rt}var Ta="undefined",Og=typeof Uint32Array===Ta?Array:Uint32Array,Ng=typeof Uint16Array===Ta?Array:Uint16Array,mc=typeof Int32Array===Ta?Array:Int32Array,cl=typeof Float64Array===Ta?Array:Float64Array,yc={float:cl,int:mc,ordinal:Array,number:Array,time:cl},Ya;function $t(r){return r>65535?Og:Ng}function Kt(){return[1/0,-1/0]}function Bg(r){var e=r.constructor;return e===Array?r.slice():new e(r)}function vl(r,e,t,n,a){var i=yc[t||"float"];if(a){var o=r[e],s=o&&o.length;if(s!==n){for(var l=new i(n),u=0;u<s;u++)l[u]=o[u];r[e]=l}}else r[e]=new i(n)}var Bi=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=W()}return r.prototype.initData=function(e,t,n){this._provider=e,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var a=e.getSource(),i=this.defaultDimValueGetter=Ya[a.sourceFormat];this._dimValueGetter=n||i,this._rawExtent=[],cc(a),this._dimensions=F(t,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,e.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(e,t){var n=this._calcDimNameToIdx,a=this._dimensions,i=n.get(e);if(i!=null){if(a[i].type===t)return i}else i=a.length;return a[i]={type:t},n.set(e,i),this._chunks[i]=new yc[t||"float"](this._rawCount),this._rawExtent[i]=Kt(),i},r.prototype.collectOrdinalMeta=function(e,t){var n=this._chunks[e],a=this._dimensions[e],i=this._rawExtent,o=a.ordinalOffset||0,s=n.length;o===0&&(i[e]=Kt());for(var l=i[e],u=o;u<s;u++){var f=n[u]=t.parseAndCollect(n[u]);isNaN(f)||(l[0]=Math.min(f,l[0]),l[1]=Math.max(f,l[1]))}a.ordinalMeta=t,a.ordinalOffset=s,a.type="ordinal"},r.prototype.getOrdinalMeta=function(e){var t=this._dimensions[e],n=t.ordinalMeta;return n},r.prototype.getDimensionProperty=function(e){var t=this._dimensions[e];return t&&t.property},r.prototype.appendData=function(e){var t=this._provider,n=this.count();t.appendData(e);var a=t.count();return t.persistent||(a+=n),n<a&&this._initDataFromProvider(n,a,!0),[n,a]},r.prototype.appendValues=function(e,t){for(var n=this._chunks,a=this._dimensions,i=a.length,o=this._rawExtent,s=this.count(),l=s+Math.max(e.length,t||0),u=0;u<i;u++){var f=a[u];vl(n,u,f.type,l,!0)}for(var c=[],h=s;h<l;h++)for(var v=h-s,d=0;d<i;d++){var f=a[d],p=Ya.arrayRows.call(this,e[v]||c,f.property,v,d);n[d][h]=p;var g=o[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}return this._rawCount=this._count=l,{start:s,end:l}},r.prototype._initDataFromProvider=function(e,t,n){for(var a=this._provider,i=this._chunks,o=this._dimensions,s=o.length,l=this._rawExtent,u=F(o,function(y){return y.property}),f=0;f<s;f++){var c=o[f];l[f]||(l[f]=Kt()),vl(i,f,c.type,t,n)}if(a.fillStorage)a.fillStorage(e,t,i,l);else for(var h=[],v=e;v<t;v++){h=a.getItem(v,h);for(var d=0;d<s;d++){var p=i[d],g=this._dimValueGetter(h,u[d],v,d);p[v]=g;var m=l[d];g<m[0]&&(m[0]=g),g>m[1]&&(m[1]=g)}}!a.persistent&&a.clean&&a.clean(),this._rawCount=this._count=t,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(e,t){if(!(t>=0&&t<this._count))return NaN;var n=this._chunks[e];return n?n[this.getRawIndex(t)]:NaN},r.prototype.getValues=function(e,t){var n=[],a=[];if(t==null){t=e,e=[];for(var i=0;i<this._dimensions.length;i++)a.push(i)}else a=e;for(var i=0,o=a.length;i<o;i++)n.push(this.get(a[i],t));return n},r.prototype.getByRawIndex=function(e,t){if(!(t>=0&&t<this._rawCount))return NaN;var n=this._chunks[e];return n?n[t]:NaN},r.prototype.getSum=function(e){var t=this._chunks[e],n=0;if(t)for(var a=0,i=this.count();a<i;a++){var o=this.get(e,a);isNaN(o)||(n+=o)}return n},r.prototype.getMedian=function(e){var t=[];this.each([e],function(i){isNaN(i)||t.push(i)});var n=t.sort(function(i,o){return i-o}),a=this.count();return a===0?0:a%2===1?n[(a-1)/2]:(n[a/2]+n[a/2-1])/2},r.prototype.indexOfRawIndex=function(e){if(e>=this._rawCount||e<0)return-1;if(!this._indices)return e;var t=this._indices,n=t[e];if(n!=null&&n<this._count&&n===e)return e;for(var a=0,i=this._count-1;a<=i;){var o=(a+i)/2|0;if(t[o]<e)a=o+1;else if(t[o]>e)i=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(e,t,n){var a=this._chunks,i=a[e],o=[];if(!i)return o;n==null&&(n=1/0);for(var s=1/0,l=-1,u=0,f=0,c=this.count();f<c;f++){var h=this.getRawIndex(f),v=t-i[h],d=Math.abs(v);d<=n&&((d<s||d===s&&v>=0&&l<0)&&(s=d,l=v,u=0),v===l&&(o[u++]=f))}return o.length=u,o},r.prototype.getIndices=function(){var e,t=this._indices;if(t){var n=t.constructor,a=this._count;if(n===Array){e=new n(a);for(var i=0;i<a;i++)e[i]=t[i]}else e=new n(t.buffer,0,a)}else{var n=$t(this._rawCount);e=new n(this.count());for(var i=0;i<e.length;i++)e[i]=i}return e},r.prototype.filter=function(e,t){if(!this._count)return this;for(var n=this.clone(),a=n.count(),i=$t(n._rawCount),o=new i(a),s=[],l=e.length,u=0,f=e[0],c=n._chunks,h=0;h<a;h++){var v=void 0,d=n.getRawIndex(h);if(l===0)v=t(h);else if(l===1){var p=c[f][d];v=t(p,h)}else{for(var g=0;g<l;g++)s[g]=c[e[g]][d];s[g]=h,v=t.apply(null,s)}v&&(o[u++]=d)}return u<a&&(n._indices=o),n._count=u,n._extent=[],n._updateGetRawIdx(),n},r.prototype.selectRange=function(e){var t=this.clone(),n=t._count;if(!n)return this;var a=Ye(e),i=a.length;if(!i)return this;var o=t.count(),s=$t(t._rawCount),l=new s(o),u=0,f=a[0],c=e[f][0],h=e[f][1],v=t._chunks,d=!1;if(!t._indices){var p=0;if(i===1){for(var g=v[a[0]],m=0;m<n;m++){var y=g[m];(y>=c&&y<=h||isNaN(y))&&(l[u++]=p),p++}d=!0}else if(i===2){for(var g=v[a[0]],_=v[a[1]],S=e[a[1]][0],w=e[a[1]][1],m=0;m<n;m++){var y=g[m],x=_[m];(y>=c&&y<=h||isNaN(y))&&(x>=S&&x<=w||isNaN(x))&&(l[u++]=p),p++}d=!0}}if(!d)if(i===1)for(var m=0;m<o;m++){var T=t.getRawIndex(m),y=v[a[0]][T];(y>=c&&y<=h||isNaN(y))&&(l[u++]=T)}else for(var m=0;m<o;m++){for(var A=!0,T=t.getRawIndex(m),M=0;M<i;M++){var D=a[M],y=v[D][T];(y<e[D][0]||y>e[D][1])&&(A=!1)}A&&(l[u++]=t.getRawIndex(m))}return u<o&&(t._indices=l),t._count=u,t._extent=[],t._updateGetRawIdx(),t},r.prototype.map=function(e,t){var n=this.clone(e);return this._updateDims(n,e,t),n},r.prototype.modify=function(e,t){this._updateDims(this,e,t)},r.prototype._updateDims=function(e,t,n){for(var a=e._chunks,i=[],o=t.length,s=e.count(),l=[],u=e._rawExtent,f=0;f<t.length;f++)u[t[f]]=Kt();for(var c=0;c<s;c++){for(var h=e.getRawIndex(c),v=0;v<o;v++)l[v]=a[t[v]][h];l[o]=c;var d=n&&n.apply(null,l);if(d!=null){typeof d!="object"&&(i[0]=d,d=i);for(var f=0;f<d.length;f++){var p=t[f],g=d[f],m=u[p],y=a[p];y&&(y[h]=g),g<m[0]&&(m[0]=g),g>m[1]&&(m[1]=g)}}}},r.prototype.lttbDownSample=function(e,t){var n=this.clone([e],!0),a=n._chunks,i=a[e],o=this.count(),s=0,l=Math.floor(1/t),u=this.getRawIndex(0),f,c,h,v=new($t(this._rawCount))(Math.min((Math.ceil(o/l)+2)*2,o));v[s++]=u;for(var d=1;d<o-1;d+=l){for(var p=Math.min(d+l,o-1),g=Math.min(d+l*2,o),m=(g+p)/2,y=0,_=p;_<g;_++){var S=this.getRawIndex(_),w=i[S];isNaN(w)||(y+=w)}y/=g-p;var x=d,T=Math.min(d+l,o),A=d-1,M=i[u];f=-1,h=x;for(var D=-1,C=0,_=x;_<T;_++){var S=this.getRawIndex(_),w=i[S];if(isNaN(w)){C++,D<0&&(D=S);continue}c=Math.abs((A-m)*(w-M)-(A-_)*(y-M)),c>f&&(f=c,h=S)}C>0&&C<T-x&&(v[s++]=Math.min(D,h),h=Math.max(D,h)),v[s++]=h,u=h}return v[s++]=this.getRawIndex(o-1),n._count=s,n._indices=v,n.getRawIndex=this._getRawIdx,n},r.prototype.minmaxDownSample=function(e,t){for(var n=this.clone([e],!0),a=n._chunks,i=Math.floor(1/t),o=a[e],s=this.count(),l=new($t(this._rawCount))(Math.ceil(s/i)*2),u=0,f=0;f<s;f+=i){var c=f,h=o[this.getRawIndex(c)],v=f,d=o[this.getRawIndex(v)],p=i;f+i>s&&(p=s-f);for(var g=0;g<p;g++){var m=this.getRawIndex(f+g),y=o[m];y<h&&(h=y,c=f+g),y>d&&(d=y,v=f+g)}var _=this.getRawIndex(c),S=this.getRawIndex(v);c<v?(l[u++]=_,l[u++]=S):(l[u++]=S,l[u++]=_)}return n._count=u,n._indices=l,n._updateGetRawIdx(),n},r.prototype.downSample=function(e,t,n,a){for(var i=this.clone([e],!0),o=i._chunks,s=[],l=Math.floor(1/t),u=o[e],f=this.count(),c=i._rawExtent[e]=Kt(),h=new($t(this._rawCount))(Math.ceil(f/l)),v=0,d=0;d<f;d+=l){l>f-d&&(l=f-d,s.length=l);for(var p=0;p<l;p++){var g=this.getRawIndex(d+p);s[p]=u[g]}var m=n(s),y=this.getRawIndex(Math.min(d+a(s,m)||0,f-1));u[y]=m,m<c[0]&&(c[0]=m),m>c[1]&&(c[1]=m),h[v++]=y}return i._count=v,i._indices=h,i._updateGetRawIdx(),i},r.prototype.each=function(e,t){if(this._count)for(var n=e.length,a=this._chunks,i=0,o=this.count();i<o;i++){var s=this.getRawIndex(i);switch(n){case 0:t(i);break;case 1:t(a[e[0]][s],i);break;case 2:t(a[e[0]][s],a[e[1]][s],i);break;default:for(var l=0,u=[];l<n;l++)u[l]=a[e[l]][s];u[l]=i,t.apply(null,u)}}},r.prototype.getDataExtent=function(e){var t=this._chunks[e],n=Kt();if(!t)return n;var a=this.count(),i=!this._indices,o;if(i)return this._rawExtent[e].slice();if(o=this._extent[e],o)return o.slice();o=n;for(var s=o[0],l=o[1],u=0;u<a;u++){var f=this.getRawIndex(u),c=t[f];c<s&&(s=c),c>l&&(l=c)}return o=[s,l],this._extent[e]=o,o},r.prototype.getRawDataItem=function(e){var t=this.getRawIndex(e);if(this._provider.persistent)return this._provider.getItem(t);for(var n=[],a=this._chunks,i=0;i<a.length;i++)n.push(a[i][t]);return n},r.prototype.clone=function(e,t){var n=new r,a=this._chunks,i=e&&Vr(e,function(s,l){return s[l]=!0,s},{});if(i)for(var o=0;o<a.length;o++)n._chunks[o]=i[o]?Bg(a[o]):a[o];else n._chunks=a;return this._copyCommonProps(n),t||(n._indices=this._cloneIndices()),n._updateGetRawIdx(),n},r.prototype._copyCommonProps=function(e){e._count=this._count,e._rawCount=this._rawCount,e._provider=this._provider,e._dimensions=this._dimensions,e._extent=$(this._extent),e._rawExtent=$(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var e=this._indices.constructor,t=void 0;if(e===Array){var n=this._indices.length;t=new e(n);for(var a=0;a<n;a++)t[a]=this._indices[a]}else t=new e(this._indices);return t}return null},r.prototype._getRawIdxIdentity=function(e){return e},r.prototype._getRawIdx=function(e){return e<this._count&&e>=0?this._indices[e]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function e(t,n,a,i){return kn(t[i],this._dimensions[i])}Ya={arrayRows:e,objectRows:function(t,n,a,i){return kn(t[n],this._dimensions[i])},keyedColumns:e,original:function(t,n,a,i){var o=t&&(t.value==null?t:t.value);return kn(o instanceof Array?o[i]:o,this._dimensions[i])},typedArray:function(t,n,a,i){return t[i]}}}(),r}(),Fg=function(){function r(e){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=e}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(e,t){this._sourceList=e,this._upstreamSignList=t,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var e=this._sourceHost,t=this._getUpstreamSourceManagers(),n=!!t.length,a,i;if(cn(e)){var o=e,s=void 0,l=void 0,u=void 0;if(n){var f=t[0];f.prepareSource(),u=f.getSource(),s=u.data,l=u.sourceFormat,i=[f._getVersionSign()]}else s=o.get("data",!0),l=Re(s)?xt:Ze,i=[];var c=this._getSourceMetaRawOption()||{},h=u&&u.metaRawOption||{},v=K(c.seriesLayoutBy,h.seriesLayoutBy)||null,d=K(c.sourceHeader,h.sourceHeader),p=K(c.dimensions,h.dimensions),g=v!==h.seriesLayoutBy||!!d!=!!h.sourceHeader||p;a=g?[Ni(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:p},l)]:[]}else{var m=e;if(n){var y=this._applyTransform(t);a=y.sourceList,i=y.upstreamSignList}else{var _=m.get("source",!0);a=[Ni(_,this._getSourceMetaRawOption(),null)],i=[]}}this._setLocalSource(a,i)},r.prototype._applyTransform=function(e){var t=this._sourceHost,n=t.get("transform",!0),a=t.get("fromTransformResult",!0);if(a!=null){var i="";e.length!==1&&hl(i)}var o,s=[],l=[];return b(e,function(u){u.prepareSource();var f=u.getSource(a||0),c="";a!=null&&!f&&hl(c),s.push(f),l.push(u._getVersionSign())}),n?o=Eg(n,s,{datasetIndex:t.componentIndex}):a!=null&&(o=[gg(s[0])]),{sourceList:o,upstreamSignList:l}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var e=this._getUpstreamSourceManagers(),t=0;t<e.length;t++){var n=e[t];if(n._isDirty()||this._upstreamSignList[t]!==n._getVersionSign())return!0}},r.prototype.getSource=function(e){e=e||0;var t=this._sourceList[e];if(!t){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(e)}return t},r.prototype.getSharedDataStore=function(e){var t=e.makeStoreSchema();return this._innerGetDataStore(t.dimensions,e.source,t.hash)},r.prototype._innerGetDataStore=function(e,t,n){var a=0,i=this._storeList,o=i[a];o||(o=i[a]={});var s=o[n];if(!s){var l=this._getUpstreamSourceManagers()[0];cn(this._sourceHost)&&l?s=l._innerGetDataStore(e,t,n):(s=new Bi,s.initData(new vc(t,e.length),e)),o[n]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var e=this._sourceHost;if(cn(e)){var t=ac(e);return t?[t.getSourceManager()]:[]}else return F(Up(e),function(n){return n.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var e=this._sourceHost,t,n,a;if(cn(e))t=e.get("seriesLayoutBy",!0),n=e.get("sourceHeader",!0),a=e.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var i=e;t=i.get("seriesLayoutBy",!0),n=i.get("sourceHeader",!0),a=i.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:n,dimensions:a}},r}();function cn(r){return r.mainType==="series"}function hl(r){throw new Error(r)}var Gg="line-height:1";function _c(r){var e=r.lineHeight;return e==null?Gg:"line-height:"+xe(e+"")+"px"}function Sc(r,e){var t=r.color||"#6e7079",n=r.fontSize||12,a=r.fontWeight||"400",i=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return e==="html"?{nameStyle:"font-size:"+xe(n+"")+"px;color:"+xe(t)+";font-weight:"+xe(a+""),valueStyle:"font-size:"+xe(o+"")+"px;color:"+xe(i)+";font-weight:"+xe(s+"")}:{nameStyle:{fontSize:n,fill:t,fontWeight:a},valueStyle:{fontSize:o,fill:i,fontWeight:s}}}var Vg=[0,10,20,30],zg=["",`
`,`

`,`


`];function jr(r,e){return e.type=r,e}function Fi(r){return r.type==="section"}function xc(r){return Fi(r)?Hg:Wg}function bc(r){if(Fi(r)){var e=0,t=r.blocks.length,n=t>1||t>0&&!r.noHeader;return b(r.blocks,function(a){var i=bc(a);i>=e&&(e=i+ +(n&&(!i||Fi(a)&&!a.noHeader)))}),e}return 0}function Hg(r,e,t,n){var a=e.noHeader,i=Ug(bc(e)),o=[],s=e.blocks||[];lt(!s||R(s)),s=s||[];var l=r.orderMode;if(e.sortBlocks&&l){s=s.slice();var u={valueAsc:"asc",valueDesc:"desc"};if(Gt(u,l)){var f=new Cg(u[l],null);s.sort(function(p,g){return f.evaluate(p.sortParam,g.sortParam)})}else l==="seriesDesc"&&s.reverse()}b(s,function(p,g){var m=e.valueFormatter,y=xc(p)(m?N(N({},r),{valueFormatter:m}):r,p,g>0?i.html:0,n);y!=null&&o.push(y)});var c=r.renderMode==="richText"?o.join(i.richText):Gi(n,o.join(""),a?t:i.html);if(a)return c;var h=Oi(e.header,"ordinal",r.useUTC),v=Sc(n,r.renderMode).nameStyle,d=_c(n);return r.renderMode==="richText"?wc(r,h,v)+i.richText+c:Gi(n,'<div style="'+v+";"+d+';">'+xe(h)+"</div>"+c,t)}function Wg(r,e,t,n){var a=r.renderMode,i=e.noName,o=e.noValue,s=!e.markerType,l=e.name,u=r.useUTC,f=e.valueFormatter||r.valueFormatter||function(S){return S=R(S)?S:[S],F(S,function(w,x){return Oi(w,R(v)?v[x]:v,u)})};if(!(i&&o)){var c=s?"":r.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",a),h=i?"":Oi(l,"ordinal",u),v=e.valueType,d=o?[]:f(e.value,e.dataIndex),p=!s||!i,g=!s&&i,m=Sc(n,a),y=m.nameStyle,_=m.valueStyle;return a==="richText"?(s?"":c)+(i?"":wc(r,h,y))+(o?"":Zg(r,d,p,g,_)):Gi(n,(s?"":c)+(i?"":Yg(h,!s,y))+(o?"":Xg(d,p,g,_)),t)}}function dl(r,e,t,n,a,i){if(r){var o=xc(r),s={useUTC:a,renderMode:t,orderMode:n,markupStyleCreator:e,valueFormatter:r.valueFormatter};return o(s,r,0,i)}}function Ug(r){return{html:Vg[r],richText:zg[r]}}function Gi(r,e,t){var n='<div style="clear:both"></div>',a="margin: "+t+"px 0 0",i=_c(r);return'<div style="'+a+";"+i+';">'+e+n+"</div>"}function Yg(r,e,t){var n=e?"margin-left:2px":"";return'<span style="'+t+";"+n+'">'+xe(r)+"</span>"}function Xg(r,e,t,n){var a=t?"10px":"20px",i=e?"float:right;margin-left:"+a:"";return r=R(r)?r:[r],'<span style="'+i+";"+n+'">'+F(r,function(o){return xe(o)}).join("&nbsp;&nbsp;")+"</span>"}function wc(r,e,t){return r.markupStyleCreator.wrapRichTextStyle(e,t)}function Zg(r,e,t,n,a){var i=[a],o=n?10:20;return t&&i.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(R(e)?e.join("  "):e,i)}function $g(r,e){var t=r.getData().getItemVisual(e,"style"),n=t[r.visualDrawType];return Ht(n)}function Tc(r,e){var t=r.get("padding");return t??(e==="richText"?[8,10]:10)}var Xa=function(){function r(){this.richTextStyles={},this._nextStyleNameId=cf()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(e,t,n){var a=n==="richText"?this._generateStyleName():null,i=Jf({color:t,type:e,renderMode:n,markerId:a});return B(i)?i:(this.richTextStyles[a]=i.style,i.content)},r.prototype.wrapRichTextStyle=function(e,t){var n={};R(t)?b(t,function(i){return N(n,i)}):N(n,t);var a=this._generateStyleName();return this.richTextStyles[a]=n,"{"+a+"|"+e+"}"},r}();function Kg(r){var e=r.series,t=r.dataIndex,n=r.multipleSeries,a=e.getData(),i=a.mapDimensionsAll("defaultedTooltip"),o=i.length,s=e.getRawValue(t),l=R(s),u=$g(e,t),f,c,h,v;if(o>1||l&&!o){var d=qg(s,e,t,i,u);f=d.inlineValues,c=d.inlineValueTypes,h=d.blocks,v=d.inlineValues[0]}else if(o){var p=a.getDimensionInfo(i[0]);v=f=or(a,t,i[0]),c=p.type}else v=f=l?s[0]:s;var g=_o(e),m=g&&e.name||"",y=a.getName(t),_=n?m:y;return jr("section",{header:m,noHeader:n||!g,sortParam:v,blocks:[jr("nameValue",{markerType:"item",markerColor:u,name:_,noName:!tn(_),value:f,valueType:c,dataIndex:t})].concat(h||[])})}function qg(r,e,t,n,a){var i=e.getData(),o=Vr(r,function(c,h,v){var d=i.getDimensionInfo(v);return c=c||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],l=[],u=[];n.length?b(n,function(c){f(or(i,t,c),c)}):b(r,f);function f(c,h){var v=i.getDimensionInfo(h);!v||v.otherDims.tooltip===!1||(o?u.push(jr("nameValue",{markerType:"subItem",markerColor:a,name:v.displayName,value:c,valueType:v.type})):(s.push(c),l.push(v.type)))}return{inlineValues:s,inlineValueTypes:l,blocks:u}}var ht=re();function vn(r,e){return r.getName(e)||r.getId(e)}var jg="__universalTransitionEnabled",Ne=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}return e.prototype.init=function(t,n,a){this.seriesIndex=this.componentIndex,this.dataTask=Br({count:Jg,reset:em}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,a);var i=ht(this).sourceManager=new Fg(this);i.prepareSource();var o=this.getInitialData(t,a);gl(o,this),this.dataTask.context.data=o,ht(this).dataBeforeProcessed=o,pl(this),this._initSelectedMapFromData(o)},e.prototype.mergeDefaultAndTheme=function(t,n){var a=Kr(this),i=a?xa(t):{},o=this.subType;X.hasClass(o)&&(o+="Series"),j(t,n.getTheme().get(this.subType)),j(t,this.getDefaultOption()),ws(t,"label",["show"]),this.fillDataTextStyle(t.data),a&&ir(t,i,a)},e.prototype.mergeOption=function(t,n){t=j(this.option,t,!0),this.fillDataTextStyle(t.data);var a=Kr(this);a&&ir(this.option,t,a);var i=ht(this).sourceManager;i.dirty(),i.prepareSource();var o=this.getInitialData(t,n);gl(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,ht(this).dataBeforeProcessed=o,pl(this),this._initSelectedMapFromData(o)},e.prototype.fillDataTextStyle=function(t){if(t&&!Re(t))for(var n=["show"],a=0;a<t.length;a++)t[a]&&t[a].label&&ws(t[a],"label",n)},e.prototype.getInitialData=function(t,n){},e.prototype.appendData=function(t){var n=this.getRawData();n.appendData(t.data)},e.prototype.getData=function(t){var n=Vi(this);if(n){var a=n.context.data;return t==null||!a.getLinkedData?a:a.getLinkedData(t)}else return ht(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var n=Vi(this);if(n){var a=n.context;a.outputData=t,n!==this.dataTask&&(a.data=t)}ht(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return W(t)},e.prototype.getSourceManager=function(){return ht(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return ht(this).dataBeforeProcessed},e.prototype.getColorBy=function(){var t=this.get("colorBy");return t||"series"},e.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.formatTooltip=function(t,n,a){return Kg({series:this,dataIndex:t,multipleSeries:n})},e.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(te.node&&!(t&&t.ssr))return!1;var n=this.getShallow("animation");return n&&this.getData().count()>this.getShallow("animationThreshold")&&(n=!1),!!n},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,n,a){var i=this.ecModel,o=Vo.prototype.getColorFromPalette.call(this,t,n,a);return o||(o=i.getColorFromPalette(t,n,a)),o},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,n){this._innerSelect(this.getData(n),t)},e.prototype.unselect=function(t,n){var a=this.option.selectedMap;if(a){var i=this.option.selectedMode,o=this.getData(n);if(i==="series"||a==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<t.length;s++){var l=t[s],u=vn(o,l);a[u]=!1,this._selectedDataIndicesMap[u]=-1}}},e.prototype.toggleSelect=function(t,n){for(var a=[],i=0;i<t.length;i++)a[0]=t[i],this.isSelected(t[i],n)?this.unselect(a,n):this.select(a,n)},e.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,n=Ye(t),a=[],i=0;i<n.length;i++){var o=t[n[i]];o>=0&&a.push(o)}return a},e.prototype.isSelected=function(t,n){var a=this.option.selectedMap;if(!a)return!1;var i=this.getData(n);return(a==="all"||a[vn(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},e.prototype.isUniversalTransitionEnabled=function(){if(this[jg])return!0;var t=this.option.universalTransition;return t?t===!0?!0:t&&t.enabled:!1},e.prototype._innerSelect=function(t,n){var a,i,o=this.option,s=o.selectedMode,l=n.length;if(!(!s||!l)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){V(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,f=0;f<l;f++){var c=n[f],h=vn(t,c);u[h]=!0,this._selectedDataIndicesMap[h]=t.getRawIndex(c)}}else if(s==="single"||s===!0){var v=n[l-1],h=vn(t,v);o.selectedMap=(a={},a[h]=!0,a),this._selectedDataIndicesMap=(i={},i[h]=t.getRawIndex(v),i)}}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var n=[];t.hasItemOption&&t.each(function(a){var i=t.getRawDataItem(a);i&&i.selected&&n.push(a)}),n.length>0&&this._innerSelect(t,n)}},e.registerClass=function(t){return X.registerClass(t)},e.protoInitialize=function(){var t=e.prototype;t.type="series.__base__",t.seriesIndex=0,t.ignoreStyleOnData=!1,t.hasSymbolVisual=!1,t.defaultSymbol="circle",t.visualStyleAccessPath="itemStyle",t.visualDrawType="fill"}(),e}(X);ft(Ne,wg);ft(Ne,Vo);yf(Ne,X);function pl(r){var e=r.name;_o(r)||(r.name=Qg(r)||e)}function Qg(r){var e=r.getRawData(),t=e.mapDimensionsAll("seriesName"),n=[];return b(t,function(a){var i=e.getDimensionInfo(a);i.displayName&&n.push(i.displayName)}),n.join(" ")}function Jg(r){return r.model.getRawData().count()}function em(r){var e=r.model;return e.setData(e.getRawData().cloneShallow()),tm}function tm(r,e){e.outputData&&r.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function gl(r,e){b(Ah(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(t){r.wrapMethod(t,le(rm,e))})}function rm(r,e){var t=Vi(r);return t&&t.setOutputEnd((e||this).count()),e}function Vi(r){var e=(r.ecModel||{}).scheduler,t=e&&e.getPipeline(r.uid);if(t){var n=t.currentTask;if(n){var a=n.agentStubMap;a&&(n=a.get(r.uid))}return n}}var Be=function(){function r(){this.group=new me,this.uid=ga("viewComponent")}return r.prototype.init=function(e,t){},r.prototype.render=function(e,t,n,a){},r.prototype.dispose=function(e,t){},r.prototype.updateView=function(e,t,n,a){},r.prototype.updateLayout=function(e,t,n,a){},r.prototype.updateVisual=function(e,t,n,a){},r.prototype.toggleBlurSeries=function(e,t,n){},r.prototype.eachRendered=function(e){var t=this.group;t&&t.traverse(e)},r}();xo(Be);ia(Be);function Yo(){var r=re();return function(e){var t=r(e),n=e.pipelineContext,a=!!t.large,i=!!t.progressiveRender,o=t.large=!!(n&&n.large),s=t.progressiveRender=!!(n&&n.progressiveRender);return(a!==o||i!==s)&&"reset"}}var Cc=re(),nm=Yo(),Me=function(){function r(){this.group=new me,this.uid=ga("viewChart"),this.renderTask=Br({plan:am,reset:im}),this.renderTask.context={view:this}}return r.prototype.init=function(e,t){},r.prototype.render=function(e,t,n,a){},r.prototype.highlight=function(e,t,n,a){var i=e.getData(a&&a.dataType);i&&yl(i,a,"emphasis")},r.prototype.downplay=function(e,t,n,a){var i=e.getData(a&&a.dataType);i&&yl(i,a,"normal")},r.prototype.remove=function(e,t){this.group.removeAll()},r.prototype.dispose=function(e,t){},r.prototype.updateView=function(e,t,n,a){this.render(e,t,n,a)},r.prototype.updateLayout=function(e,t,n,a){this.render(e,t,n,a)},r.prototype.updateVisual=function(e,t,n,a){this.render(e,t,n,a)},r.prototype.eachRendered=function(e){va(this.group,e)},r.markUpdateMethod=function(e,t){Cc(e).updateMethod=t},r.protoInitialize=function(){var e=r.prototype;e.type="chart"}(),r}();function ml(r,e,t){r&&Li(r)&&(e==="emphasis"?Fn:Gn)(r,t)}function yl(r,e,t){var n=Vt(r,e),a=e&&e.highlightKey!=null?Jd(e.highlightKey):null;n!=null?b(be(n),function(i){ml(r.getItemGraphicEl(i),t,a)}):r.eachItemGraphicEl(function(i){ml(i,t,a)})}xo(Me);ia(Me);function am(r){return nm(r.model)}function im(r){var e=r.model,t=r.ecModel,n=r.api,a=r.payload,i=e.pipelineContext.progressiveRender,o=r.view,s=a&&Cc(a).updateMethod,l=i?"incrementalPrepareRender":s&&o[s]?s:"render";return l!=="render"&&o[l](e,t,n,a),om[l]}var om={incrementalPrepareRender:{progress:function(r,e){e.view.incrementalRender(r,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(r,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},Xn="\0__throttleOriginMethod",_l="\0__throttleRate",Sl="\0__throttleType";function Ca(r,e,t){var n,a=0,i=0,o=null,s,l,u,f;e=e||0;function c(){i=new Date().getTime(),o=null,r.apply(l,u||[])}var h=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];n=new Date().getTime(),l=this,u=v;var p=f||e,g=f||t;f=null,s=n-(g?a:i)-p,clearTimeout(o),g?o=setTimeout(c,p):s>=0?c():o=setTimeout(c,-s),a=n};return h.clear=function(){o&&(clearTimeout(o),o=null)},h.debounceNextCall=function(v){f=v},h}function Dc(r,e,t,n){var a=r[e];if(a){var i=a[Xn]||a,o=a[Sl],s=a[_l];if(s!==t||o!==n){if(t==null)return r[e]=i;a=r[e]=Ca(i,t,n==="debounce"),a[Xn]=i,a[Sl]=n,a[_l]=t}return a}}function zi(r,e){var t=r[e];t&&t[Xn]&&(t.clear&&t.clear(),r[e]=t[Xn])}var xl=re(),bl={itemStyle:Yr(Hf,!0),lineStyle:Yr(zf,!0)},sm={lineStyle:"stroke",itemStyle:"fill"};function Mc(r,e){var t=r.visualStyleMapper||bl[e];return t||(console.warn("Unknown style type '"+e+"'."),bl.itemStyle)}function Ac(r,e){var t=r.visualDrawType||sm[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var lm={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,e){var t=r.getData(),n=r.visualStyleAccessPath||"itemStyle",a=r.getModel(n),i=Mc(r,n),o=i(a),s=a.getShallow("decal");s&&(t.setVisual("decal",s),s.dirty=!0);var l=Ac(r,n),u=o[l],f=H(u)?u:null,c=o.fill==="auto"||o.stroke==="auto";if(!o[l]||f||c){var h=r.getColorFromPalette(r.name,null,e.getSeriesCount());o[l]||(o[l]=h,t.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||H(o.fill)?h:o.fill,o.stroke=o.stroke==="auto"||H(o.stroke)?h:o.stroke}if(t.setVisual("style",o),t.setVisual("drawType",l),!e.isSeriesFiltered(r)&&f)return t.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var p=r.getDataParams(d),g=N({},o);g[l]=f(p),v.setItemVisual(d,"style",g)}}}},gr=new J,um={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,e){if(!(r.ignoreStyleOnData||e.isSeriesFiltered(r))){var t=r.getData(),n=r.visualStyleAccessPath||"itemStyle",a=Mc(r,n),i=t.getVisual("drawType");return{dataEach:t.hasItemOption?function(o,s){var l=o.getRawDataItem(s);if(l&&l[n]){gr.option=l[n];var u=a(gr),f=o.ensureUniqueItemVisual(s,"style");N(f,u),gr.option.decal&&(o.setItemVisual(s,"decal",gr.option.decal),gr.option.decal.dirty=!0),i in u&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},fm={performRawSeries:!0,overallReset:function(r){var e=W();r.eachSeries(function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var a=t.type+"-"+n,i=e.get(a);i||(i={},e.set(a,i)),xl(t).scope=i}}),r.eachSeries(function(t){if(!(t.isColorBySeries()||r.isSeriesFiltered(t))){var n=t.getRawData(),a={},i=t.getData(),o=xl(t).scope,s=t.visualStyleAccessPath||"itemStyle",l=Ac(t,s);i.each(function(u){var f=i.getRawIndex(u);a[f]=u}),n.each(function(u){var f=a[u],c=i.getItemVisual(f,"colorFromPalette");if(c){var h=i.ensureUniqueItemVisual(f,"style"),v=n.getName(u)||u+"",d=n.count();h[l]=t.getColorFromPalette(v,o,d)}})}})}},hn=Math.PI;function cm(r,e){e=e||{},ee(e,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var t=new me,n=new fe({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});t.add(n);var a=new we({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),i=new fe({style:{fill:"none"},textContent:a,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});t.add(i);var o;return e.showSpinner&&(o=new ra({shape:{startAngle:-hn/2,endAngle:-hn/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:hn*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:hn*3/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var s=a.getBoundingRect().width,l=e.showSpinner?e.spinnerRadius:0,u=(r.getWidth()-l*2-(e.showSpinner&&s?10:0)-s)/2-(e.showSpinner&&s?0:5+s/2)+(e.showSpinner?0:s/2)+(s?0:l),f=r.getHeight()/2;e.showSpinner&&o.setShape({cx:u,cy:f}),i.setShape({x:u-l,y:f-l,width:l*2,height:l*2}),n.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},t.resize(),t}var Ic=function(){function r(e,t,n,a){this._stageTaskMap=W(),this.ecInstance=e,this.api=t,n=this._dataProcessorHandlers=n.slice(),a=this._visualHandlers=a.slice(),this._allHandlers=n.concat(a)}return r.prototype.restoreData=function(e,t){e.restoreData(t),this._stageTaskMap.each(function(n){var a=n.overallTask;a&&a.dirty()})},r.prototype.getPerformArgs=function(e,t){if(e.__pipeline){var n=this._pipelineMap.get(e.__pipeline.id),a=n.context,i=!t&&n.progressiveEnabled&&(!a||a.progressiveRender)&&e.__idxInPipeline>n.blockIndex,o=i?n.step:null,s=a&&a.modDataCount,l=s!=null?Math.ceil(s/o):null;return{step:o,modBy:l,modDataCount:s}}},r.prototype.getPipeline=function(e){return this._pipelineMap.get(e)},r.prototype.updateStreamModes=function(e,t){var n=this._pipelineMap.get(e.uid),a=e.getData(),i=a.count(),o=n.progressiveEnabled&&t.incrementalPrepareRender&&i>=n.threshold,s=e.get("large")&&i>=e.get("largeThreshold"),l=e.get("progressiveChunkMode")==="mod"?i:null;e.pipelineContext=n.context={progressiveRender:o,modDataCount:l,large:s}},r.prototype.restorePipelines=function(e){var t=this,n=t._pipelineMap=W();e.eachSeries(function(a){var i=a.getProgressive(),o=a.uid;n.set(o,{id:o,head:null,tail:null,threshold:a.getProgressiveThreshold(),progressiveEnabled:i&&!(a.preventIncremental&&a.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),t._pipe(a,a.dataTask)})},r.prototype.prepareStageTasks=function(){var e=this._stageTaskMap,t=this.api.getModel(),n=this.api;b(this._allHandlers,function(a){var i=e.get(a.uid)||e.set(a.uid,{}),o="";lt(!(a.reset&&a.overallReset),o),a.reset&&this._createSeriesStageTask(a,i,t,n),a.overallReset&&this._createOverallStageTask(a,i,t,n)},this)},r.prototype.prepareView=function(e,t,n,a){var i=e.renderTask,o=i.context;o.model=t,o.ecModel=n,o.api=a,i.__block=!e.incrementalPrepareRender,this._pipe(t,i)},r.prototype.performDataProcessorTasks=function(e,t){this._performStageTasks(this._dataProcessorHandlers,e,t,{block:!0})},r.prototype.performVisualTasks=function(e,t,n){this._performStageTasks(this._visualHandlers,e,t,n)},r.prototype._performStageTasks=function(e,t,n,a){a=a||{};var i=!1,o=this;b(e,function(l,u){if(!(a.visualType&&a.visualType!==l.visualType)){var f=o._stageTaskMap.get(l.uid),c=f.seriesTaskMap,h=f.overallTask;if(h){var v,d=h.agentStubMap;d.each(function(g){s(a,g)&&(g.dirty(),v=!0)}),v&&h.dirty(),o.updatePayload(h,n);var p=o.getPerformArgs(h,a.block);d.each(function(g){g.perform(p)}),h.perform(p)&&(i=!0)}else c&&c.each(function(g,m){s(a,g)&&g.dirty();var y=o.getPerformArgs(g,a.block);y.skip=!l.performRawSeries&&t.isSeriesFiltered(g.context.model),o.updatePayload(g,n),g.perform(y)&&(i=!0)})}});function s(l,u){return l.setDirty&&(!l.dirtyMap||l.dirtyMap.get(u.__pipeline.id))}this.unfinished=i||this.unfinished},r.prototype.performSeriesTasks=function(e){var t;e.eachSeries(function(n){t=n.dataTask.perform()||t}),this.unfinished=t||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(e){var t=e.tail;do{if(t.__block){e.blockIndex=t.__idxInPipeline;break}t=t.getUpstream()}while(t)})},r.prototype.updatePayload=function(e,t){t!=="remain"&&(e.context.payload=t)},r.prototype._createSeriesStageTask=function(e,t,n,a){var i=this,o=t.seriesTaskMap,s=t.seriesTaskMap=W(),l=e.seriesType,u=e.getTargetSeries;e.createOnAllSeries?n.eachRawSeries(f):l?n.eachRawSeriesByType(l,f):u&&u(n,a).each(f);function f(c){var h=c.uid,v=s.set(h,o&&o.get(h)||Br({plan:gm,reset:mm,count:_m}));v.context={model:c,ecModel:n,api:a,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:i},i._pipe(c,v)}},r.prototype._createOverallStageTask=function(e,t,n,a){var i=this,o=t.overallTask=t.overallTask||Br({reset:vm});o.context={ecModel:n,api:a,overallReset:e.overallReset,scheduler:i};var s=o.agentStubMap,l=o.agentStubMap=W(),u=e.seriesType,f=e.getTargetSeries,c=!0,h=!1,v="";lt(!e.createOnAllSeries,v),u?n.eachRawSeriesByType(u,d):f?f(n,a).each(d):(c=!1,b(n.getSeries(),d));function d(p){var g=p.uid,m=l.set(g,s&&s.get(g)||(h=!0,Br({reset:hm,onDirty:pm})));m.context={model:p,overallProgress:c},m.agent=o,m.__block=c,i._pipe(p,m)}h&&o.dirty()},r.prototype._pipe=function(e,t){var n=e.uid,a=this._pipelineMap.get(n);!a.head&&(a.head=t),a.tail&&a.tail.pipe(t),a.tail=t,t.__idxInPipeline=a.count++,t.__pipeline=a},r.wrapStageHandler=function(e,t){return H(e)&&(e={overallReset:e,seriesType:Sm(e)}),e.uid=ga("stageHandler"),t&&(e.visualType=t),e},r}();function vm(r){r.overallReset(r.ecModel,r.api,r.payload)}function hm(r){return r.overallProgress&&dm}function dm(){this.agent.dirty(),this.getDownstream().dirty()}function pm(){this.agent&&this.agent.dirty()}function gm(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function mm(r){r.useClearVisual&&r.data.clearAllVisual();var e=r.resetDefines=be(r.reset(r.model,r.ecModel,r.api,r.payload));return e.length>1?F(e,function(t,n){return Lc(n)}):ym}var ym=Lc(0);function Lc(r){return function(e,t){var n=t.data,a=t.resetDefines[r];if(a&&a.dataEach)for(var i=e.start;i<e.end;i++)a.dataEach(n,i);else a&&a.progress&&a.progress(e,n)}}function _m(r){return r.data.count()}function Sm(r){Zn=null;try{r(Qr,Pc)}catch{}return Zn}var Qr={},Pc={},Zn;kc(Qr,zo);kc(Pc,oc);Qr.eachSeriesByType=Qr.eachRawSeriesByType=function(r){Zn=r};Qr.eachComponent=function(r){r.mainType==="series"&&r.subType&&(Zn=r.subType)};function kc(r,e){for(var t in e.prototype)r[t]=Tt}var wl=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const xm={color:wl,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],wl]};var pe="#B9B8CE",Tl="#100C2A",dn=function(){return{axisLine:{lineStyle:{color:pe}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},Cl=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Ec={darkMode:!0,color:Cl,backgroundColor:Tl,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:pe},pageTextStyle:{color:pe}},textStyle:{color:pe},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:pe}},dataZoom:{borderColor:"#71708A",textStyle:{color:pe},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:pe}},timeline:{lineStyle:{color:pe},label:{color:pe},controlStyle:{color:pe,borderColor:pe}},calendar:{itemStyle:{color:Tl},dayLabel:{color:pe},monthLabel:{color:pe},yearLabel:{color:pe}},timeAxis:dn(),logAxis:dn(),valueAxis:dn(),categoryAxis:dn(),line:{symbol:"circle"},graph:{color:Cl},gauge:{title:{color:pe},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:pe},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Ec.categoryAxis.splitLine.show=!1;var bm=function(){function r(){}return r.prototype.normalizeQuery=function(e){var t={},n={},a={};if(B(e)){var i=et(e);t.mainType=i.main||null,t.subType=i.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};b(e,function(l,u){for(var f=!1,c=0;c<o.length;c++){var h=o[c],v=u.lastIndexOf(h);if(v>0&&v===u.length-h.length){var d=u.slice(0,v);d!=="data"&&(t.mainType=d,t[h.toLowerCase()]=l,f=!0)}}s.hasOwnProperty(u)&&(n[u]=l,f=!0),f||(a[u]=l)})}return{cptQuery:t,dataQuery:n,otherQuery:a}},r.prototype.filter=function(e,t){var n=this.eventInfo;if(!n)return!0;var a=n.targetEl,i=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var l=t.cptQuery,u=t.dataQuery;return f(l,o,"mainType")&&f(l,o,"subType")&&f(l,o,"index","componentIndex")&&f(l,o,"name")&&f(l,o,"id")&&f(u,i,"name")&&f(u,i,"dataIndex")&&f(u,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(e,t.otherQuery,a,i));function f(c,h,v,d){return c[v]==null||h[d||v]===c[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),Hi=["symbol","symbolSize","symbolRotate","symbolOffset"],Dl=Hi.concat(["symbolKeepAspect"]),wm={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,e){var t=r.getData();if(r.legendIcon&&t.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var n={},a={},i=!1,o=0;o<Hi.length;o++){var s=Hi[o],l=r.get(s);H(l)?(i=!0,a[s]=l):n[s]=l}if(n.symbol=n.symbol||r.defaultSymbol,t.setVisual(N({legendIcon:r.legendIcon||n.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},n)),e.isSeriesFiltered(r))return;var u=Ye(a);function f(c,h){for(var v=r.getRawValue(h),d=r.getDataParams(h),p=0;p<u.length;p++){var g=u[p];c.setItemVisual(h,g,a[g](v,d))}}return{dataEach:i?f:null}}},Tm={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,e){if(!r.hasSymbolVisual||e.isSeriesFiltered(r))return;var t=r.getData();function n(a,i){for(var o=a.getItemModel(i),s=0;s<Dl.length;s++){var l=Dl[s],u=o.getShallow(l,!0);u!=null&&a.setItemVisual(i,l,u)}}return{dataEach:t.hasItemOption?n:null}}};function Cm(r,e,t){switch(t){case"color":var n=r.getItemVisual(e,"style");return n[r.getVisual("drawType")];case"opacity":return r.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(e,t)}}function Dm(r,e){switch(e){case"color":var t=r.getVisual("style");return t[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(e)}}function qt(r,e,t,n,a){var i=r+e;t.isSilent(i)||n.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,l=o.option.selectedMap,u=a.selected,f=0;f<u.length;f++)if(u[f].seriesIndex===s){var c=o.getData(),h=Vt(c,a.fromActionPayload);t.trigger(i,{type:i,seriesId:o.id,name:R(h)?c.getName(h[0]):c.getName(h),selected:B(l)?l:N({},l)})}})}function Mm(r,e,t){r.on("selectchanged",function(n){var a=t.getModel();n.isFromClick?(qt("map","selectchanged",e,a,n),qt("pie","selectchanged",e,a,n)):n.fromAction==="select"?(qt("map","selected",e,a,n),qt("pie","selected",e,a,n)):n.fromAction==="unselect"&&(qt("map","unselected",e,a,n),qt("pie","unselected",e,a,n))})}function Pr(r,e,t){for(var n;r&&!(e(r)&&(n=r,t));)r=r.__hostTarget||r.parent;return n}var Am=Ie.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,e){var t=e.cx,n=e.cy,a=e.width/2,i=e.height/2;r.moveTo(t,n-i),r.lineTo(t+a,n+i),r.lineTo(t-a,n+i),r.closePath()}}),Im=Ie.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,e){var t=e.cx,n=e.cy,a=e.width/2,i=e.height/2;r.moveTo(t,n-i),r.lineTo(t+a,n),r.lineTo(t,n+i),r.lineTo(t-a,n),r.closePath()}}),Lm=Ie.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,e){var t=e.x,n=e.y,a=e.width/5*3,i=Math.max(a,e.height),o=a/2,s=o*o/(i-o),l=n-i+o+s,u=Math.asin(s/o),f=Math.cos(u)*o,c=Math.sin(u),h=Math.cos(u),v=o*.6,d=o*.7;r.moveTo(t-f,l+s),r.arc(t,l,o,Math.PI-u,Math.PI*2+u),r.bezierCurveTo(t+f-c*v,l+s+h*v,t,n-d,t,n),r.bezierCurveTo(t,n-d,t-f+c*v,l+s+h*v,t-f,l+s),r.closePath()}}),Pm=Ie.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,e){var t=e.height,n=e.width,a=e.x,i=e.y,o=n/3*2;r.moveTo(a,i),r.lineTo(a+o,i+t),r.lineTo(a,i+t/4*3),r.lineTo(a-o,i+t),r.lineTo(a,i),r.closePath()}}),km={line:wt,rect:fe,roundRect:fe,square:fe,circle:ea,diamond:Im,pin:Lm,arrow:Pm,triangle:Am},Em={line:function(r,e,t,n,a){a.x1=r,a.y1=e+n/2,a.x2=r+t,a.y2=e+n/2},rect:function(r,e,t,n,a){a.x=r,a.y=e,a.width=t,a.height=n},roundRect:function(r,e,t,n,a){a.x=r,a.y=e,a.width=t,a.height=n,a.r=Math.min(t,n)/4},square:function(r,e,t,n,a){var i=Math.min(t,n);a.x=r,a.y=e,a.width=i,a.height=i},circle:function(r,e,t,n,a){a.cx=r+t/2,a.cy=e+n/2,a.r=Math.min(t,n)/2},diamond:function(r,e,t,n,a){a.cx=r+t/2,a.cy=e+n/2,a.width=t,a.height=n},pin:function(r,e,t,n,a){a.x=r+t/2,a.y=e+n/2,a.width=t,a.height=n},arrow:function(r,e,t,n,a){a.x=r+t/2,a.y=e+n/2,a.width=t,a.height=n},triangle:function(r,e,t,n,a){a.cx=r+t/2,a.cy=e+n/2,a.width=t,a.height=n}},Wi={};b(km,function(r,e){Wi[e]=new r});var Rm=Ie.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,e,t){var n=wi(r,e,t),a=this.shape;return a&&a.symbolType==="pin"&&e.position==="inside"&&(n.y=t.y+t.height*.4),n},buildPath:function(r,e,t){var n=e.symbolType;if(n!=="none"){var a=Wi[n];a||(n="rect",a=Wi[n]),Em[n](e.x,e.y,e.width,e.height,a.shape),a.buildPath(r,a.shape,t)}}});function Om(r,e){if(this.type!=="image"){var t=this.style;this.__isEmptyBrush?(t.stroke=r,t.fill=e||"#fff",t.lineWidth=2):this.shape.symbolType==="line"?t.stroke=r:t.fill=r,this.markRedraw()}}function Wt(r,e,t,n,a,i,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var l;return r.indexOf("image://")===0?l=Mo(r.slice(8),new Ee(e,t,n,a),o?"center":"cover"):r.indexOf("path://")===0?l=ua(r.slice(7),{},new Ee(e,t,n,a),o?"center":"cover"):l=new Rm({shape:{symbolType:r,x:e,y:t,width:n,height:a}}),l.__isEmptyBrush=s,l.setColor=Om,i&&l.setColor(i),l}function Nm(r){return R(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function Rc(r,e){if(r!=null)return R(r)||(r=[r,r]),[ve(r[0],e[0])||0,ve(K(r[1],r[0]),e[1])||0]}var Za=new Ih,Ml=new Lh(100),Al=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Ui(r,e){if(r==="none")return null;var t=e.getDevicePixelRatio(),n=e.getZr(),a=n.painter.type==="svg";r.dirty&&Za.delete(r);var i=Za.get(r);if(i)return i;var o=ee(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return l(s),s.rotation=o.rotation,s.scaleX=s.scaleY=a?1:1/t,Za.set(r,s),r.dirty=!1,s;function l(u){for(var f=[t],c=!0,h=0;h<Al.length;++h){var v=o[Al[h]];if(v!=null&&!R(v)&&!B(v)&&!oe(v)&&typeof v!="boolean"){c=!1;break}f.push(v)}var d;if(c){d=f.join(",")+(a?"-svg":"");var p=Ml.get(d);p&&(a?u.svgElement=p:u.image=p)}var g=Nc(o.dashArrayX),m=Bm(o.dashArrayY),y=Oc(o.symbol),_=Fm(g),S=Bc(m),w=!a&&Ju.createCanvas(),x=a&&{tag:"g",attrs:{},key:"dcl",children:[]},T=M(),A;w&&(w.width=T.width*t,w.height=T.height*t,A=w.getContext("2d")),D(),c&&Ml.put(d,w||x),u.image=w,u.svgElement=x,u.svgWidth=T.width,u.svgHeight=T.height;function M(){for(var C=1,L=0,I=_.length;L<I;++L)C=xs(C,_[L]);for(var P=1,L=0,I=y.length;L<I;++L)P=xs(P,y[L].length);C*=P;var k=S*_.length*y.length;return{width:Math.max(1,Math.min(C,o.maxTileWidth)),height:Math.max(1,Math.min(k,o.maxTileHeight))}}function D(){A&&(A.clearRect(0,0,w.width,w.height),o.backgroundColor&&(A.fillStyle=o.backgroundColor,A.fillRect(0,0,w.width,w.height)));for(var C=0,L=0;L<m.length;++L)C+=m[L];if(C<=0)return;for(var I=-S,P=0,k=0,G=0;I<T.height;){if(P%2===0){for(var Z=k/2%y.length,E=0,O=0,U=0;E<T.width*2;){for(var q=0,L=0;L<g[G].length;++L)q+=g[G][L];if(q<=0)break;if(O%2===0){var Te=(1-o.symbolSize)*.5,ce=E+g[G][O]*Te,Le=I+m[P]*Te,ye=g[G][O]*o.symbolSize,hr=m[P]*o.symbolSize,dh=U/2%y[Z].length;ph(ce,Le,ye,hr,y[Z][dh])}E+=g[G][O],++U,++O,O===g[G].length&&(O=0)}++G,G===g.length&&(G=0)}I+=m[P],++k,++P,P===m.length&&(P=0)}function ph(gh,mh,yh,_h,Sh){var sn=a?1:t,vs=Wt(Sh,gh*sn,mh*sn,yh*sn,_h*sn,o.color,o.symbolKeepAspect);if(a){var hs=n.painter.renderOneToVNode(vs);hs&&x.children.push(hs)}else ef(A,vs)}}}}function Oc(r){if(!r||r.length===0)return[["rect"]];if(B(r))return[[r]];for(var e=!0,t=0;t<r.length;++t)if(!B(r[t])){e=!1;break}if(e)return Oc([r]);for(var n=[],t=0;t<r.length;++t)B(r[t])?n.push([r[t]]):n.push(r[t]);return n}function Nc(r){if(!r||r.length===0)return[[0,0]];if(oe(r)){var e=Math.ceil(r);return[[e,e]]}for(var t=!0,n=0;n<r.length;++n)if(!oe(r[n])){t=!1;break}if(t)return Nc([r]);for(var a=[],n=0;n<r.length;++n)if(oe(r[n])){var e=Math.ceil(r[n]);a.push([e,e])}else{var e=F(r[n],function(s){return Math.ceil(s)});e.length%2===1?a.push(e.concat(e)):a.push(e)}return a}function Bm(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(oe(r)){var e=Math.ceil(r);return[e,e]}var t=F(r,function(n){return Math.ceil(n)});return r.length%2?t.concat(t):t}function Fm(r){return F(r,function(e){return Bc(e)})}function Bc(r){for(var e=0,t=0;t<r.length;++t)e+=r[t];return r.length%2===1?e*2:e}function Gm(r,e){r.eachRawSeries(function(t){if(!r.isSeriesFiltered(t)){var n=t.getData();n.hasItemVisual()&&n.each(function(o){var s=n.getItemVisual(o,"decal");if(s){var l=n.ensureUniqueItemVisual(o,"style");l.decal=Ui(s,e)}});var a=n.getVisual("decal");if(a){var i=n.getVisual("style");i.decal=Ui(a,e)}}})}var qe=new na,Fc={};function Vm(r,e){Fc[r]=e}function Gc(r){return Fc[r]}var zm="5.6.0",Hm={zrender:"5.6.1"},Wm=1,Um=800,Ym=900,Xm=1e3,Zm=2e3,$m=5e3,Vc=1e3,Km=1100,Xo=2e3,zc=3e3,qm=4e3,Da=4500,jm=4600,Qm=5e3,Jm=6e3,Hc=7e3,Wc={PROCESSOR:{FILTER:Xm,SERIES_FILTER:Um,STATISTIC:$m},VISUAL:{LAYOUT:Vc,PROGRESSIVE_LAYOUT:Km,GLOBAL:Xo,CHART:zc,POST_CHART_LAYOUT:jm,COMPONENT:qm,BRUSH:Qm,CHART_ITEM:Da,ARIA:Jm,DECAL:Hc}},de="__flagInMainProcess",Ce="__pendingUpdate",$a="__needsUpdateStatus",Il=/^[a-zA-Z0-9_]+$/,Ka="__connectUpdateStatus",Ll=0,ey=1,ty=2;function Uc(r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(this.isDisposed()){this.id;return}return Xc(this,r,e)}}function Yc(r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Xc(this,r,e)}}function Xc(r,e,t){return t[0]=t[0]&&t[0].toLowerCase(),na.prototype[e].apply(r,t)}var Zc=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e}(na),$c=Zc.prototype;$c.on=Yc("on");$c.off=Yc("off");var jt,qa,pn,dt,ja,Qa,Ja,mr,yr,Pl,kl,ei,El,gn,Rl,Kc,Fe,Ol,$n=function(r){z(e,r);function e(t,n,a){var i=r.call(this,new bm)||this;i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],a=a||{},B(n)&&(n=qc[n]),i._dom=t;var o="canvas",s="auto",l=!1;a.ssr&&Ph(function(h){var v=Y(h),d=v.dataIndex;if(d!=null){var p=W();return p.set("series_index",v.seriesIndex),p.set("data_index",d),v.ssrType&&p.set("ssr_type",v.ssrType),p}});var u=i._zr=ps(t,{renderer:a.renderer||o,devicePixelRatio:a.devicePixelRatio,width:a.width,height:a.height,ssr:a.ssr,useDirtyRect:K(a.useDirtyRect,l),useCoarsePointer:K(a.useCoarsePointer,s),pointerSize:a.pointerSize});i._ssr=a.ssr,i._throttledZrFlush=Ca(Q(u.flush,u),17),n=$(n),n&&lc(n,!0),i._theme=n,i._locale=Mp(a.locale||Uf),i._coordSysMgr=new ba;var f=i._api=Rl(i);function c(h,v){return h.__prio-v.__prio}return ka(qn,c),ka(Yi,c),i._scheduler=new Ic(i,f,Yi,qn),i._messageCenter=new Zc,i._initEvents(),i.resize=Q(i.resize,i),u.animation.on("frame",i._onframe,i),Pl(u,i),kl(u,i),bi(i),i}return e.prototype._onframe=function(){if(!this._disposed){Ol(this);var t=this._scheduler;if(this[Ce]){var n=this[Ce].silent;this[de]=!0;try{jt(this),dt.update.call(this,null,this[Ce].updateParams)}catch(l){throw this[de]=!1,this[Ce]=null,l}this._zr.flush(),this[de]=!1,this[Ce]=null,mr.call(this,n),yr.call(this,n)}else if(t.unfinished){var a=Wm,i=this._model,o=this._api;t.unfinished=!1;do{var s=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Qa(this,i),t.performVisualTasks(i),gn(this,this._model,o,"remain",{}),a-=+new Date-s}while(a>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,n,a){if(!this[de]){if(this._disposed){this.id;return}var i,o,s;if(V(n)&&(a=n.lazyUpdate,i=n.silent,o=n.replaceMerge,s=n.transition,n=n.notMerge),this[de]=!0,!this._model||n){var l=new rg(this._api),u=this._theme,f=this._model=new zo;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,u,this._locale,l)}this._model.setOption(t,{replaceMerge:o},Xi);var c={seriesTransition:s,optionChanged:!0};if(a)this[Ce]={silent:i,updateParams:c},this[de]=!1,this.getZr().wakeUp();else{try{jt(this),dt.update.call(this,null,c)}catch(h){throw this[Ce]=null,this[de]=!1,h}this._ssr||this._zr.flush(),this[Ce]=null,this[de]=!1,mr.call(this,i),yr.call(this,i)}}},e.prototype.setTheme=function(){},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||te.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var n=this._zr.painter;return n.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var n=this._zr.painter;return n.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(te.svgSupported){var t=this._zr,n=t.storage.getDisplayList();return b(n,function(a){a.stopAnimation(null,!0)}),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(this._disposed){this.id;return}t=t||{};var n=t.excludeComponents,a=this._model,i=[],o=this;b(n,function(l){a.eachComponent({mainType:l},function(u){var f=o._componentsMap[u.__viewId];f.group.ignore||(i.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return b(i,function(l){l.group.ignore=!1}),s},e.prototype.getConnectedDataURL=function(t){if(this._disposed){this.id;return}var n=t.type==="svg",a=this.group,i=Math.min,o=Math.max,s=1/0;if(jn[a]){var l=s,u=s,f=-s,c=-s,h=[],v=t&&t.pixelRatio||this.getDevicePixelRatio();b(Bt,function(_,S){if(_.group===a){var w=n?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas($(t)),x=_.getDom().getBoundingClientRect();l=i(x.left,l),u=i(x.top,u),f=o(x.right,f),c=o(x.bottom,c),h.push({dom:w,left:x.left,top:x.top})}}),l*=v,u*=v,f*=v,c*=v;var d=f-l,p=c-u,g=Ju.createCanvas(),m=ps(g,{renderer:n?"svg":"canvas"});if(m.resize({width:d,height:p}),n){var y="";return b(h,function(_){var S=_.left-l,w=_.top-u;y+='<g transform="translate('+S+","+w+')">'+_.dom+"</g>"}),m.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&m.painter.setBackgroundColor(t.connectedBackgroundColor),m.refreshImmediately(),m.painter.toDataURL()}else return t.connectedBackgroundColor&&m.add(new fe({shape:{x:0,y:0,width:d,height:p},style:{fill:t.connectedBackgroundColor}})),b(h,function(_){var S=new sr({style:{x:_.left*v-l,y:_.top*v-u,image:_.dom}});m.add(S)}),m.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}else return this.getDataURL(t)},e.prototype.convertToPixel=function(t,n){return ja(this,"convertToPixel",t,n)},e.prototype.convertFromPixel=function(t,n){return ja(this,"convertFromPixel",t,n)},e.prototype.containPixel=function(t,n){if(this._disposed){this.id;return}var a=this._model,i,o=Ra(a,t);return b(o,function(s,l){l.indexOf("Models")>=0&&b(s,function(u){var f=u.coordinateSystem;if(f&&f.containPoint)i=i||!!f.containPoint(n);else if(l==="seriesModels"){var c=this._chartsMap[u.__viewId];c&&c.containPoint&&(i=i||c.containPoint(n,u))}},this)},this),!!i},e.prototype.getVisual=function(t,n){var a=this._model,i=Ra(a,t,{defaultMainType:"series"}),o=i.seriesModel,s=o.getData(),l=i.hasOwnProperty("dataIndexInside")?i.dataIndexInside:i.hasOwnProperty("dataIndex")?s.indexOfRawIndex(i.dataIndex):null;return l!=null?Cm(s,l,n):Dm(s,n)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t=this;b(ry,function(n){var a=function(i){var o=t.getModel(),s=i.target,l,u=n==="globalout";if(u?l={}:s&&Pr(s,function(d){var p=Y(d);if(p&&p.dataIndex!=null){var g=p.dataModel||o.getSeriesByIndex(p.seriesIndex);return l=g&&g.getDataParams(p.dataIndex,p.dataType,s)||{},!0}else if(p.eventData)return l=N({},p.eventData),!0},!0),l){var f=l.componentType,c=l.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",c=l.seriesIndex);var h=f&&c!=null&&o.getComponent(f,c),v=h&&t[h.mainType==="series"?"_chartsMap":"_componentsMap"][h.__viewId];l.event=i,l.type=n,t._$eventProcessor.eventInfo={targetEl:s,packedEvent:l,model:h,view:v},t.trigger(n,l)}};a.zrEventfulCallAtLast=!0,t._zr.on(n,a,t)}),b(Fr,function(n,a){t._messageCenter.on(a,function(i){this.trigger(a,i)},t)}),b(["selectchanged"],function(n){t._messageCenter.on(n,function(a){this.trigger(n,a)},t)}),Mm(this._messageCenter,this,this._api)},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var t=this.getDom();t&&pf(this.getDom(),$o,"");var n=this,a=n._api,i=n._model;b(n._componentsViews,function(o){o.dispose(i,a)}),b(n._chartsViews,function(o){o.dispose(i,a)}),n._zr.dispose(),n._dom=n._model=n._chartsMap=n._componentsMap=n._chartsViews=n._componentsViews=n._scheduler=n._api=n._zr=n._throttledZrFlush=n._theme=n._coordSysMgr=n._messageCenter=null,delete Bt[n.id]},e.prototype.resize=function(t){if(!this[de]){if(this._disposed){this.id;return}this._zr.resize(t);var n=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!n){var a=n.resetOption("media"),i=t&&t.silent;this[Ce]&&(i==null&&(i=this[Ce].silent),a=!0,this[Ce]=null),this[de]=!0;try{a&&jt(this),dt.update.call(this,{type:"resize",animation:N({duration:0},t&&t.animation)})}catch(o){throw this[de]=!1,o}this[de]=!1,mr.call(this,i),yr.call(this,i)}}},e.prototype.showLoading=function(t,n){if(this._disposed){this.id;return}if(V(t)&&(n=t,t=""),t=t||"default",this.hideLoading(),!!Zi[t]){var a=Zi[t](this._api,n),i=this._zr;this._loadingFX=a,i.add(a)}},e.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},e.prototype.makeActionFromEvent=function(t){var n=N({},t);return n.type=Fr[t.type],n},e.prototype.dispatchAction=function(t,n){if(this._disposed){this.id;return}if(V(n)||(n={silent:!!n}),!!Kn[t.type]&&this._model){if(this[de]){this._pendingActions.push(t);return}var a=n.silent;Ja.call(this,t,a);var i=n.flush;i?this._zr.flush():i!==!1&&te.browser.weChat&&this._throttledZrFlush(),mr.call(this,a),yr.call(this,a)}},e.prototype.updateLabelLayout=function(){qe.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed){this.id;return}var n=t.seriesIndex,a=this.getModel(),i=a.getSeriesByIndex(n);i.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()},e.internalField=function(){jt=function(c){var h=c._scheduler;h.restorePipelines(c._model),h.prepareStageTasks(),qa(c,!0),qa(c,!1),h.plan()},qa=function(c,h){for(var v=c._model,d=c._scheduler,p=h?c._componentsViews:c._chartsViews,g=h?c._componentsMap:c._chartsMap,m=c._zr,y=c._api,_=0;_<p.length;_++)p[_].__alive=!1;h?v.eachComponent(function(x,T){x!=="series"&&S(T)}):v.eachSeries(S);function S(x){var T=x.__requireNewView;x.__requireNewView=!1;var A="_ec_"+x.id+"_"+x.type,M=!T&&g[A];if(!M){var D=et(x.type),C=h?Be.getClass(D.main,D.sub):Me.getClass(D.sub);M=new C,M.init(v,y),g[A]=M,p.push(M),m.add(M.group)}x.__viewId=M.__id=A,M.__alive=!0,M.__model=x,M.group.__ecComponentInfo={mainType:x.mainType,index:x.componentIndex},!h&&d.prepareView(M,x,v,y)}for(var _=0;_<p.length;){var w=p[_];w.__alive?_++:(!h&&w.renderTask.dispose(),m.remove(w.group),w.dispose(v,y),p.splice(_,1),g[w.__id]===w&&delete g[w.__id],w.__id=w.group.__ecComponentInfo=null)}},pn=function(c,h,v,d,p){var g=c._model;if(g.setUpdatePayload(v),!d){b([].concat(c._componentsViews).concat(c._chartsViews),w);return}var m={};m[d+"Id"]=v[d+"Id"],m[d+"Index"]=v[d+"Index"],m[d+"Name"]=v[d+"Name"];var y={mainType:d,query:m};p&&(y.subType=p);var _=v.excludeSeriesId,S;_!=null&&(S=W(),b(be(_),function(x){var T=tt(x,null);T!=null&&S.set(T,!0)})),g&&g.eachComponent(y,function(x){var T=S&&S.get(x.id)!=null;if(!T)if(Es(v))if(x instanceof Ne)v.type===Nt&&!v.notBlur&&!x.get(["emphasis","disabled"])&&Yd(x,v,c._api);else{var A=Co(x.mainType,x.componentIndex,v.name,c._api),M=A.focusSelf,D=A.dispatchers;v.type===Nt&&M&&!v.notBlur&&Ai(x.mainType,x.componentIndex,c._api),D&&b(D,function(C){v.type===Nt?Fn(C):Gn(C)})}else Pi(v)&&x instanceof Ne&&($d(x,v,c._api),Ps(x),Fe(c))},c),g&&g.eachComponent(y,function(x){var T=S&&S.get(x.id)!=null;T||w(c[d==="series"?"_chartsMap":"_componentsMap"][x.__viewId])},c);function w(x){x&&x.__alive&&x[h]&&x[h](x.__model,g,c._api,v)}},dt={prepareAndUpdate:function(c){jt(this),dt.update.call(this,c,{optionChanged:c.newOption!=null})},update:function(c,h){var v=this._model,d=this._api,p=this._zr,g=this._coordSysMgr,m=this._scheduler;if(v){v.setUpdatePayload(c),m.restoreData(v,c),m.performSeriesTasks(v),g.create(v,d),m.performDataProcessorTasks(v,c),Qa(this,v),g.update(v,d),t(v),m.performVisualTasks(v,c),ei(this,v,d,c,h);var y=v.get("backgroundColor")||"transparent",_=v.get("darkMode");p.setBackgroundColor(y),_!=null&&_!=="auto"&&p.setDarkMode(_),qe.trigger("afterupdate",v,d)}},updateTransform:function(c){var h=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(c);var p=[];v.eachComponent(function(m,y){if(m!=="series"){var _=h.getViewOfComponentModel(y);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(y,v,d,c);S&&S.update&&p.push(_)}else p.push(_)}});var g=W();v.eachSeries(function(m){var y=h._chartsMap[m.__viewId];if(y.updateTransform){var _=y.updateTransform(m,v,d,c);_&&_.update&&g.set(m.uid,1)}else g.set(m.uid,1)}),t(v),this._scheduler.performVisualTasks(v,c,{setDirty:!0,dirtyMap:g}),gn(this,v,d,c,{},g),qe.trigger("afterupdate",v,d)}},updateView:function(c){var h=this._model;h&&(h.setUpdatePayload(c),Me.markUpdateMethod(c,"updateView"),t(h),this._scheduler.performVisualTasks(h,c,{setDirty:!0}),ei(this,h,this._api,c,{}),qe.trigger("afterupdate",h,this._api))},updateVisual:function(c){var h=this,v=this._model;v&&(v.setUpdatePayload(c),v.eachSeries(function(d){d.getData().clearAllVisual()}),Me.markUpdateMethod(c,"updateVisual"),t(v),this._scheduler.performVisualTasks(v,c,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,p){if(d!=="series"){var g=h.getViewOfComponentModel(p);g&&g.__alive&&g.updateVisual(p,v,h._api,c)}}),v.eachSeries(function(d){var p=h._chartsMap[d.__viewId];p.updateVisual(d,v,h._api,c)}),qe.trigger("afterupdate",v,this._api))},updateLayout:function(c){dt.update.call(this,c)}},ja=function(c,h,v,d){if(c._disposed){c.id;return}for(var p=c._model,g=c._coordSysMgr.getCoordinateSystems(),m,y=Ra(p,v),_=0;_<g.length;_++){var S=g[_];if(S[h]&&(m=S[h](p,y,d))!=null)return m}},Qa=function(c,h){var v=c._chartsMap,d=c._scheduler;h.eachSeries(function(p){d.updateStreamModes(p,v[p.__viewId])})},Ja=function(c,h){var v=this,d=this.getModel(),p=c.type,g=c.escapeConnect,m=Kn[p],y=m.actionInfo,_=(y.update||"update").split(":"),S=_.pop(),w=_[0]!=null&&et(_[0]);this[de]=!0;var x=[c],T=!1;c.batch&&(T=!0,x=F(c.batch,function(P){return P=ee(N({},P),c),P.batch=null,P}));var A=[],M,D=Pi(c),C=Es(c);if(C&&Mf(this._api),b(x,function(P){if(M=m.action(P,v._model,v._api),M=M||N({},P),M.type=y.event||M.type,A.push(M),C){var k=So(c),G=k.queryOptionMap,Z=k.mainTypeSpecified,E=Z?G.keys()[0]:"series";pn(v,S,P,E),Fe(v)}else D?(pn(v,S,P,"series"),Fe(v)):w&&pn(v,S,P,w.main,w.sub)}),S!=="none"&&!C&&!D&&!w)try{this[Ce]?(jt(this),dt.update.call(this,c),this[Ce]=null):dt[S].call(this,c)}catch(P){throw this[de]=!1,P}if(T?M={type:y.event||p,escapeConnect:g,batch:A}:M=A[0],this[de]=!1,!h){var L=this._messageCenter;if(L.trigger(M.type,M),D){var I={type:"selectchanged",escapeConnect:g,selected:Kd(d),isFromClick:c.isFromClick||!1,fromAction:c.type,fromActionPayload:c};L.trigger(I.type,I)}}},mr=function(c){for(var h=this._pendingActions;h.length;){var v=h.shift();Ja.call(this,v,c)}},yr=function(c){!c&&this.trigger("updated")},Pl=function(c,h){c.on("rendered",function(v){h.trigger("rendered",v),c.animation.isFinished()&&!h[Ce]&&!h._scheduler.unfinished&&!h._pendingActions.length&&h.trigger("finished")})},kl=function(c,h){c.on("mouseover",function(v){var d=v.target,p=Pr(d,Li);p&&(Xd(p,v,h._api),Fe(h))}).on("mouseout",function(v){var d=v.target,p=Pr(d,Li);p&&(Zd(p,v,h._api),Fe(h))}).on("click",function(v){var d=v.target,p=Pr(d,function(y){return Y(y).dataIndex!=null},!0);if(p){var g=p.selected?"unselect":"select",m=Y(p);h._api.dispatchAction({type:g,dataType:m.dataType,dataIndexInside:m.dataIndex,seriesIndex:m.seriesIndex,isFromClick:!0})}})};function t(c){c.clearColorPalette(),c.eachSeries(function(h){h.clearColorPalette()})}function n(c){var h=[],v=[],d=!1;if(c.eachComponent(function(y,_){var S=_.get("zlevel")||0,w=_.get("z")||0,x=_.getZLevelKey();d=d||!!x,(y==="series"?v:h).push({zlevel:S,z:w,idx:_.componentIndex,type:y,key:x})}),d){var p=h.concat(v),g,m;ka(p,function(y,_){return y.zlevel===_.zlevel?y.z-_.z:y.zlevel-_.zlevel}),b(p,function(y){var _=c.getComponent(y.type,y.idx),S=y.zlevel,w=y.key;g!=null&&(S=Math.max(g,S)),w?(S===g&&w!==m&&S++,m=w):m&&(S===g&&S++,m=""),g=S,_.setZLevel(S)})}}ei=function(c,h,v,d,p){n(h),El(c,h,v,d,p),b(c._chartsViews,function(g){g.__alive=!1}),gn(c,h,v,d,p),b(c._chartsViews,function(g){g.__alive||g.remove(h,v)})},El=function(c,h,v,d,p,g){b(g||c._componentsViews,function(m){var y=m.__model;u(y,m),m.render(y,h,v,d),s(y,m),f(y,m)})},gn=function(c,h,v,d,p,g){var m=c._scheduler;p=N(p||{},{updatedSeries:h.getSeries()}),qe.trigger("series:beforeupdate",h,v,p);var y=!1;h.eachSeries(function(_){var S=c._chartsMap[_.__viewId];S.__alive=!0;var w=S.renderTask;m.updatePayload(w,d),u(_,S),g&&g.get(_.uid)&&w.dirty(),w.perform(m.getPerformArgs(w))&&(y=!0),S.group.silent=!!_.get("silent"),o(_,S),Ps(_)}),m.unfinished=y||m.unfinished,qe.trigger("series:layoutlabels",h,v,p),qe.trigger("series:transition",h,v,p),h.eachSeries(function(_){var S=c._chartsMap[_.__viewId];s(_,S),f(_,S)}),i(c,h),qe.trigger("series:afterupdate",h,v,p)},Fe=function(c){c[$a]=!0,c.getZr().wakeUp()},Ol=function(c){c[$a]&&(c.getZr().storage.traverse(function(h){tr(h)||a(h)}),c[$a]=!1)};function a(c){for(var h=[],v=c.currentStates,d=0;d<v.length;d++){var p=v[d];p==="emphasis"||p==="blur"||p==="select"||h.push(p)}c.selected&&c.states.select&&h.push("select"),c.hoverState===sa&&c.states.emphasis?h.push("emphasis"):c.hoverState===oa&&c.states.blur&&h.push("blur"),c.useStates(h)}function i(c,h){var v=c._zr,d=v.storage,p=0;d.traverse(function(g){g.isGroup||p++}),p>h.get("hoverLayerThreshold")&&!te.node&&!te.worker&&h.eachSeries(function(g){if(!g.preventUsingHoverLayer){var m=c._chartsMap[g.__viewId];m.__alive&&m.eachRendered(function(y){y.states.emphasis&&(y.states.emphasis.hoverLayer=!0)})}})}function o(c,h){var v=c.get("blendMode")||null;h.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(c,h){if(!c.preventAutoZ){var v=c.get("z")||0,d=c.get("zlevel")||0;h.eachRendered(function(p){return l(p,v,d,-1/0),!0})}}function l(c,h,v,d){var p=c.getTextContent(),g=c.getTextGuideLine(),m=c.isGroup;if(m)for(var y=c.childrenRef(),_=0;_<y.length;_++)d=Math.max(l(y[_],h,v,d),d);else c.z=h,c.zlevel=v,d=Math.max(c.z2,d);if(p&&(p.z=h,p.zlevel=v,isFinite(d)&&(p.z2=d+2)),g){var S=c.textGuideLineConfig;g.z=h,g.zlevel=v,isFinite(d)&&(g.z2=d+(S&&S.showAbove?1:-1))}return d}function u(c,h){h.eachRendered(function(v){if(!tr(v)){var d=v.getTextContent(),p=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),p&&p.stateTransition&&(p.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(c,h){var v=c.getModel("stateAnimation"),d=c.isAnimationEnabled(),p=v.get("duration"),g=p>0?{duration:p,delay:v.get("delay"),easing:v.get("easing")}:null;h.eachRendered(function(m){if(m.states&&m.states.emphasis){if(tr(m))return;if(m instanceof Ie&&ep(m),m.__dirty){var y=m.prevStates;y&&m.useStates(y)}if(d){m.stateTransition=g;var _=m.getTextContent(),S=m.getTextGuideLine();_&&(_.stateTransition=g),S&&(S.stateTransition=g)}m.__dirty&&a(m)}})}Rl=function(c){return new(function(h){z(v,h);function v(){return h!==null&&h.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return c._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var p=d.__ecComponentInfo;if(p!=null)return c._model.getComponent(p.mainType,p.index);d=d.parent}},v.prototype.enterEmphasis=function(d,p){Fn(d,p),Fe(c)},v.prototype.leaveEmphasis=function(d,p){Gn(d,p),Fe(c)},v.prototype.enterBlur=function(d){Ud(d),Fe(c)},v.prototype.leaveBlur=function(d){wf(d),Fe(c)},v.prototype.enterSelect=function(d){Tf(d),Fe(c)},v.prototype.leaveSelect=function(d){Cf(d),Fe(c)},v.prototype.getModel=function(){return c.getModel()},v.prototype.getViewOfComponentModel=function(d){return c.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return c.getViewOfSeriesModel(d)},v}(oc))(c)},Kc=function(c){function h(v,d){for(var p=0;p<v.length;p++){var g=v[p];g[Ka]=d}}b(Fr,function(v,d){c._messageCenter.on(d,function(p){if(jn[c.group]&&c[Ka]!==Ll){if(p&&p.escapeConnect)return;var g=c.makeActionFromEvent(p),m=[];b(Bt,function(y){y!==c&&y.group===c.group&&m.push(y)}),h(m,Ll),b(m,function(y){y[Ka]!==ey&&y.dispatchAction(g)}),h(m,ty)}})})}}(),e}(na),Zo=$n.prototype;Zo.on=Uc("on");Zo.off=Uc("off");Zo.one=function(r,e,t){var n=this;function a(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];e&&e.apply&&e.apply(this,i),n.off(r,a)}this.on.call(this,r,a,t)};var ry=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var Kn={},Fr={},Yi=[],Xi=[],qn=[],qc={},Zi={},Bt={},jn={},ny=+new Date-0,ay=+new Date-0,$o="_echarts_instance_";function iy(r,e,t){var n=!(t&&t.ssr);if(n){var a=Ko(r);if(a)return a}var i=new $n(r,e,t);return i.id="ec_"+ny++,Bt[i.id]=i,n&&pf(r,$o,i.id),Kc(i),qe.trigger("afterinit",i),i}function oy(r){if(R(r)){var e=r;r=null,b(e,function(t){t.group!=null&&(r=t.group)}),r=r||"g_"+ay++,b(e,function(t){t.group=r})}return jn[r]=!0,r}function jc(r){jn[r]=!1}var sy=jc;function ly(r){B(r)?r=Bt[r]:r instanceof $n||(r=Ko(r)),r instanceof $n&&!r.isDisposed()&&r.dispose()}function Ko(r){return Bt[xd(r,$o)]}function uy(r){return Bt[r]}function qo(r,e){qc[r]=e}function jo(r){ie(Xi,r)<0&&Xi.push(r)}function Qo(r,e){Jo(Yi,r,e,Zm)}function Qc(r){Ma("afterinit",r)}function Jc(r){Ma("afterupdate",r)}function Ma(r,e){qe.on(r,e)}function Yt(r,e,t){H(e)&&(t=e,e="");var n=V(r)?r.type:[r,r={event:e}][0];r.event=(r.event||n).toLowerCase(),e=r.event,!Fr[e]&&(lt(Il.test(n)&&Il.test(e)),Kn[n]||(Kn[n]={action:t,actionInfo:r}),Fr[e]=n)}function ev(r,e){ba.register(r,e)}function fy(r){var e=ba.get(r);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()}function tv(r,e){Jo(qn,r,e,Vc,"layout")}function Dt(r,e){Jo(qn,r,e,zc,"visual")}var Nl=[];function Jo(r,e,t,n,a){if((H(e)||V(e))&&(t=e,e=n),!(ie(Nl,t)>=0)){Nl.push(t);var i=Ic.wrapStageHandler(t,a);i.__prio=e,i.__raw=t,r.push(i)}}function es(r,e){Zi[r]=e}function cy(r){tf({createCanvas:r})}function rv(r,e,t){var n=Gc("registerMap");n&&n(r,e,t)}function vy(r){var e=Gc("getMap");return e&&e(r)}var nv=kg;Dt(Xo,lm);Dt(Da,um);Dt(Da,fm);Dt(Xo,wm);Dt(Da,Tm);Dt(Hc,Gm);jo(lc);Qo(Ym,dg);es("default",cm);Yt({type:Nt,event:Nt,update:Nt},Tt);Yt({type:An,event:An,update:An},Tt);Yt({type:Er,event:Er,update:Er},Tt);Yt({type:In,event:In,update:In},Tt);Yt({type:Rr,event:Rr,update:Rr},Tt);qo("light",xm);qo("dark",Ec);var hy={};function _r(r){return r==null?0:r.length||1}function Bl(r){return r}var dy=function(){function r(e,t,n,a,i,o){this._old=e,this._new=t,this._oldKeyGetter=n||Bl,this._newKeyGetter=a||Bl,this.context=i,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(e){return this._add=e,this},r.prototype.update=function(e){return this._update=e,this},r.prototype.updateManyToOne=function(e){return this._updateManyToOne=e,this},r.prototype.updateOneToMany=function(e){return this._updateOneToMany=e,this},r.prototype.updateManyToMany=function(e){return this._updateManyToMany=e,this},r.prototype.remove=function(e){return this._remove=e,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var e=this._old,t=this._new,n={},a=new Array(e.length),i=new Array(t.length);this._initIndexMap(e,null,a,"_oldKeyGetter"),this._initIndexMap(t,n,i,"_newKeyGetter");for(var o=0;o<e.length;o++){var s=a[o],l=n[s],u=_r(l);if(u>1){var f=l.shift();l.length===1&&(n[s]=l[0]),this._update&&this._update(f,o)}else u===1?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(i,n)},r.prototype._executeMultiple=function(){var e=this._old,t=this._new,n={},a={},i=[],o=[];this._initIndexMap(e,n,i,"_oldKeyGetter"),this._initIndexMap(t,a,o,"_newKeyGetter");for(var s=0;s<i.length;s++){var l=i[s],u=n[l],f=a[l],c=_r(u),h=_r(f);if(c>1&&h===1)this._updateManyToOne&&this._updateManyToOne(f,u),a[l]=null;else if(c===1&&h>1)this._updateOneToMany&&this._updateOneToMany(f,u),a[l]=null;else if(c===1&&h===1)this._update&&this._update(f,u),a[l]=null;else if(c>1&&h>1)this._updateManyToMany&&this._updateManyToMany(f,u),a[l]=null;else if(c>1)for(var v=0;v<c;v++)this._remove&&this._remove(u[v]);else this._remove&&this._remove(u)}this._performRestAdd(o,a)},r.prototype._performRestAdd=function(e,t){for(var n=0;n<e.length;n++){var a=e[n],i=t[a],o=_r(i);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(i[s]);else o===1&&this._add&&this._add(i);t[a]=null}},r.prototype._initIndexMap=function(e,t,n,a){for(var i=this._diffModeMultiple,o=0;o<e.length;o++){var s="_ec_"+this[a](e[o],o);if(i||(n[o]=s),!!t){var l=t[s],u=_r(l);u===0?(t[s]=o,i&&n.push(s)):u===1?t[s]=[l,o]:l.push(o)}}},r}(),py=function(){function r(e,t){this._encode=e,this._schema=t}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function gy(r,e){var t={},n=t.encode={},a=W(),i=[],o=[],s={};b(r.dimensions,function(h){var v=r.getDimensionInfo(h),d=v.coordDim;if(d){var p=v.coordDimIndex;ti(n,d)[p]=h,v.isExtraCoord||(a.set(d,1),yy(v.type)&&(i[0]=h),ti(s,d)[p]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(h)}tc.each(function(g,m){var y=ti(n,m),_=v.otherDims[m];_!=null&&_!==!1&&(y[_]=v.name)})});var l=[],u={};a.each(function(h,v){var d=n[v];u[v]=d[0],l=l.concat(d)}),t.dataDimsOnCoord=l,t.dataDimIndicesOnCoord=F(l,function(h){return r.getDimensionInfo(h).storeDimIndex}),t.encodeFirstDimNotExtra=u;var f=n.label;f&&f.length&&(i=f.slice());var c=n.tooltip;return c&&c.length?o=c.slice():o.length||(o=i.slice()),n.defaultedLabel=i,n.defaultedTooltip=o,t.userOutput=new py(s,e),t}function ti(r,e){return r.hasOwnProperty(e)||(r[e]=[]),r[e]}function my(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function yy(r){return!(r==="ordinal"||r==="time")}var En=function(){function r(e){this.otherDims={},e!=null&&N(this,e)}return r}(),_y=re(),Sy={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},av=function(){function r(e){this.dimensions=e.dimensions,this._dimOmitted=e.dimensionOmitted,this.source=e.source,this._fullDimCount=e.fullDimensionCount,this._updateDimOmitted(e.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(e){this._dimOmitted=e,e&&(this._dimNameMap||(this._dimNameMap=sv(this.source)))},r.prototype.getSourceDimensionIndex=function(e){return K(this._dimNameMap.get(e),-1)},r.prototype.getSourceDimension=function(e){var t=this.source.dimensionsDefine;if(t)return t[e]},r.prototype.makeStoreSchema=function(){for(var e=this._fullDimCount,t=cc(this.source),n=!lv(e),a="",i=[],o=0,s=0;o<e;o++){var l=void 0,u=void 0,f=void 0,c=this.dimensions[s];if(c&&c.storeDimIndex===o)l=t?c.name:null,u=c.type,f=c.ordinalMeta,s++;else{var h=this.getSourceDimension(o);h&&(l=t?h.name:null,u=h.type)}i.push({property:l,type:u,ordinalMeta:f}),t&&l!=null&&(!c||!c.isCalculationCoord)&&(a+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),a+="$",a+=Sy[u]||"f",f&&(a+=f.uid),a+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,a].join("$$");return{dimensions:i,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var e=[],t=0,n=0;t<this._fullDimCount;t++){var a=void 0,i=this.dimensions[n];if(i&&i.storeDimIndex===t)i.isCalculationCoord||(a=i.name),n++;else{var o=this.getSourceDimension(t);o&&(a=o.name)}e.push(a)}return e},r.prototype.appendCalculationDimension=function(e){this.dimensions.push(e),e.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function iv(r){return r instanceof av}function ov(r){for(var e=W(),t=0;t<(r||[]).length;t++){var n=r[t],a=V(n)?n.name:n;a!=null&&e.get(a)==null&&e.set(a,t)}return e}function sv(r){var e=_y(r);return e.dimNameMap||(e.dimNameMap=ov(r.dimensionsDefine))}function lv(r){return r>30}var Sr=V,pt=F,xy=typeof Int32Array>"u"?Array:Int32Array,by="e\0\0",Fl=-1,wy=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Ty=["_approximateExtent"],Gl,mn,xr,br,ri,wr,ni,uv=function(){function r(e,t){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var n,a=!1;iv(e)?(n=e.dimensions,this._dimOmitted=e.isDimensionOmitted(),this._schema=e):(a=!0,n=e),n=n||["x","y"];for(var i={},o=[],s={},l=!1,u={},f=0;f<n.length;f++){var c=n[f],h=B(c)?new En({name:c}):c instanceof En?c:new En(c),v=h.name;h.type=h.type||"float",h.coordDim||(h.coordDim=v,h.coordDimIndex=0);var d=h.otherDims=h.otherDims||{};o.push(v),i[v]=h,u[v]!=null&&(l=!0),h.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),a&&(h.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=i,this._initGetDimensionInfo(l),this.hostModel=t,this._invertedIndicesMap=s,this._dimOmitted){var p=this._dimIdxToName=W();b(o,function(g){p.set(i[g].storeDimIndex,g)})}}return r.prototype.getDimension=function(e){var t=this._recognizeDimIndex(e);if(t==null)return e;if(t=e,!this._dimOmitted)return this.dimensions[t];var n=this._dimIdxToName.get(t);if(n!=null)return n;var a=this._schema.getSourceDimension(t);if(a)return a.name},r.prototype.getDimensionIndex=function(e){var t=this._recognizeDimIndex(e);if(t!=null)return t;if(e==null)return-1;var n=this._getDimInfo(e);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(e):-1},r.prototype._recognizeDimIndex=function(e){if(oe(e)||e!=null&&!isNaN(e)&&!this._getDimInfo(e)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(e)<0))return+e},r.prototype._getStoreDimIndex=function(e){var t=this.getDimensionIndex(e);return t},r.prototype.getDimensionInfo=function(e){return this._getDimInfo(this.getDimension(e))},r.prototype._initGetDimensionInfo=function(e){var t=this._dimInfos;this._getDimInfo=e?function(n){return t.hasOwnProperty(n)?t[n]:void 0}:function(n){return t[n]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(e,t){var n=this._dimSummary;if(t==null)return n.encodeFirstDimNotExtra[e];var a=n.encode[e];return a?a[t]:null},r.prototype.mapDimensionsAll=function(e){var t=this._dimSummary,n=t.encode[e];return(n||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(e,t,n){var a=this,i;if(e instanceof Bi&&(i=e),!i){var o=this.dimensions,s=Ho(e)||Qn(e)?new vc(e,o.length):e;i=new Bi;var l=pt(o,function(u){return{type:a._dimInfos[u].type,property:u}});i.initData(s,l,n)}this._store=i,this._nameList=(t||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=gy(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(e){var t=this._store.appendData(e);this._doInit(t[0],t[1])},r.prototype.appendValues=function(e,t){var n=this._store.appendValues(e,t&&t.length),a=n.start,i=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),t)for(var s=a;s<i;s++){var l=s-a;this._nameList[s]=t[l],o&&ni(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var e=this._store,t=this.dimensions,n=0;n<t.length;n++){var a=this._dimInfos[t[n]];a.ordinalMeta&&e.collectOrdinalMeta(a.storeDimIndex,a.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var e=this._store.getProvider();return this._idDimIdx==null&&e.getSource().sourceFormat!==xt&&!e.fillStorage},r.prototype._doInit=function(e,t){if(!(e>=t)){var n=this._store,a=n.getProvider();this._updateOrdinalMeta();var i=this._nameList,o=this._idList,s=a.getSource().sourceFormat,l=s===Ze;if(l&&!a.pure)for(var u=[],f=e;f<t;f++){var c=a.getItem(f,u);if(!this.hasItemOption&&fd(c)&&(this.hasItemOption=!0),c){var h=c.name;i[f]==null&&h!=null&&(i[f]=tt(h,null));var v=c.id;o[f]==null&&v!=null&&(o[f]=tt(v,null))}}if(this._shouldMakeIdFromName())for(var f=e;f<t;f++)ni(this,f);Gl(this)}},r.prototype.getApproximateExtent=function(e){return this._approximateExtent[e]||this._store.getDataExtent(this._getStoreDimIndex(e))},r.prototype.setApproximateExtent=function(e,t){t=this.getDimension(t),this._approximateExtent[t]=e.slice()},r.prototype.getCalculationInfo=function(e){return this._calculationInfo[e]},r.prototype.setCalculationInfo=function(e,t){Sr(e)?N(this._calculationInfo,e):this._calculationInfo[e]=t},r.prototype.getName=function(e){var t=this.getRawIndex(e),n=this._nameList[t];return n==null&&this._nameDimIdx!=null&&(n=xr(this,this._nameDimIdx,t)),n==null&&(n=""),n},r.prototype._getCategory=function(e,t){var n=this._store.get(e,t),a=this._store.getOrdinalMeta(e);return a?a.categories[n]:n},r.prototype.getId=function(e){return mn(this,this.getRawIndex(e))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(e,t){var n=this._store,a=this._dimInfos[e];if(a)return n.get(a.storeDimIndex,t)},r.prototype.getByRawIndex=function(e,t){var n=this._store,a=this._dimInfos[e];if(a)return n.getByRawIndex(a.storeDimIndex,t)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(e){return this._store.getDataExtent(this._getStoreDimIndex(e))},r.prototype.getSum=function(e){return this._store.getSum(this._getStoreDimIndex(e))},r.prototype.getMedian=function(e){return this._store.getMedian(this._getStoreDimIndex(e))},r.prototype.getValues=function(e,t){var n=this,a=this._store;return R(e)?a.getValues(pt(e,function(i){return n._getStoreDimIndex(i)}),t):a.getValues(e)},r.prototype.hasValue=function(e){for(var t=this._dimSummary.dataDimIndicesOnCoord,n=0,a=t.length;n<a;n++)if(isNaN(this._store.get(t[n],e)))return!1;return!0},r.prototype.indexOfName=function(e){for(var t=0,n=this._store.count();t<n;t++)if(this.getName(t)===e)return t;return-1},r.prototype.getRawIndex=function(e){return this._store.getRawIndex(e)},r.prototype.indexOfRawIndex=function(e){return this._store.indexOfRawIndex(e)},r.prototype.rawIndexOf=function(e,t){var n=e&&this._invertedIndicesMap[e],a=n&&n[t];return a==null||isNaN(a)?Fl:a},r.prototype.indicesOfNearest=function(e,t,n){return this._store.indicesOfNearest(this._getStoreDimIndex(e),t,n)},r.prototype.each=function(e,t,n){H(e)&&(n=t,t=e,e=[]);var a=n||this,i=pt(br(e),this._getStoreDimIndex,this);this._store.each(i,a?Q(t,a):t)},r.prototype.filterSelf=function(e,t,n){H(e)&&(n=t,t=e,e=[]);var a=n||this,i=pt(br(e),this._getStoreDimIndex,this);return this._store=this._store.filter(i,a?Q(t,a):t),this},r.prototype.selectRange=function(e){var t=this,n={},a=Ye(e);return b(a,function(i){var o=t._getStoreDimIndex(i);n[o]=e[i]}),this._store=this._store.selectRange(n),this},r.prototype.mapArray=function(e,t,n){H(e)&&(n=t,t=e,e=[]),n=n||this;var a=[];return this.each(e,function(){a.push(t&&t.apply(this,arguments))},n),a},r.prototype.map=function(e,t,n,a){var i=n||a||this,o=pt(br(e),this._getStoreDimIndex,this),s=wr(this);return s._store=this._store.map(o,i?Q(t,i):t),s},r.prototype.modify=function(e,t,n,a){var i=n||a||this,o=pt(br(e),this._getStoreDimIndex,this);this._store.modify(o,i?Q(t,i):t)},r.prototype.downSample=function(e,t,n,a){var i=wr(this);return i._store=this._store.downSample(this._getStoreDimIndex(e),t,n,a),i},r.prototype.minmaxDownSample=function(e,t){var n=wr(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(e),t),n},r.prototype.lttbDownSample=function(e,t){var n=wr(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(e),t),n},r.prototype.getRawDataItem=function(e){return this._store.getRawDataItem(e)},r.prototype.getItemModel=function(e){var t=this.hostModel,n=this.getRawDataItem(e);return new J(n,t,t&&t.ecModel)},r.prototype.diff=function(e){var t=this;return new dy(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(n){return mn(e,n)},function(n){return mn(t,n)})},r.prototype.getVisual=function(e){var t=this._visual;return t&&t[e]},r.prototype.setVisual=function(e,t){this._visual=this._visual||{},Sr(e)?N(this._visual,e):this._visual[e]=t},r.prototype.getItemVisual=function(e,t){var n=this._itemVisuals[e],a=n&&n[t];return a??this.getVisual(t)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(e,t){var n=this._itemVisuals,a=n[e];a||(a=n[e]={});var i=a[t];return i==null&&(i=this.getVisual(t),R(i)?i=i.slice():Sr(i)&&(i=N({},i)),a[t]=i),i},r.prototype.setItemVisual=function(e,t,n){var a=this._itemVisuals[e]||{};this._itemVisuals[e]=a,Sr(t)?N(a,t):a[t]=n},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(e,t){Sr(e)?N(this._layout,e):this._layout[e]=t},r.prototype.getLayout=function(e){return this._layout[e]},r.prototype.getItemLayout=function(e){return this._itemLayouts[e]},r.prototype.setItemLayout=function(e,t,n){this._itemLayouts[e]=n?N(this._itemLayouts[e]||{},t):t},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(e,t){var n=this.hostModel&&this.hostModel.seriesIndex;Rd(n,this.dataType,e,t),this._graphicEls[e]=t},r.prototype.getItemGraphicEl=function(e){return this._graphicEls[e]},r.prototype.eachItemGraphicEl=function(e,t){b(this._graphicEls,function(n,a){n&&e&&e.call(t,n,a)})},r.prototype.cloneShallow=function(e){return e||(e=new r(this._schema?this._schema:pt(this.dimensions,this._getDimInfo,this),this.hostModel)),ri(e,this),e._store=this._store,e},r.prototype.wrapMethod=function(e,t){var n=this[e];H(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(e),this[e]=function(){var a=n.apply(this,arguments);return t.apply(this,[a].concat(kh(arguments)))})},r.internalField=function(){Gl=function(e){var t=e._invertedIndicesMap;b(t,function(n,a){var i=e._dimInfos[a],o=i.ordinalMeta,s=e._store;if(o){n=t[a]=new xy(o.categories.length);for(var l=0;l<n.length;l++)n[l]=Fl;for(var l=0;l<s.count();l++)n[s.get(i.storeDimIndex,l)]=l}})},xr=function(e,t,n){return tt(e._getCategory(t,n),null)},mn=function(e,t){var n=e._idList[t];return n==null&&e._idDimIdx!=null&&(n=xr(e,e._idDimIdx,t)),n==null&&(n=by+t),n},br=function(e){return R(e)||(e=e!=null?[e]:[]),e},wr=function(e){var t=new r(e._schema?e._schema:pt(e.dimensions,e._getDimInfo,e),e.hostModel);return ri(t,e),t},ri=function(e,t){b(wy.concat(t.__wrappedMethods||[]),function(n){t.hasOwnProperty(n)&&(e[n]=t[n])}),e.__wrappedMethods=t.__wrappedMethods,b(Ty,function(n){e[n]=$(t[n])}),e._calculationInfo=N({},t._calculationInfo)},ni=function(e,t){var n=e._nameList,a=e._idList,i=e._nameDimIdx,o=e._idDimIdx,s=n[t],l=a[t];if(s==null&&i!=null&&(n[t]=s=xr(e,i,t)),l==null&&o!=null&&(a[t]=l=xr(e,o,t)),l==null&&s!=null){var u=e._nameRepeatCount,f=u[s]=(u[s]||0)+1;l=s,f>1&&(l+="__ec__"+f),a[t]=l}}}(),r}();function Cy(r,e){return fv(r,e).dimensions}function fv(r,e){Ho(r)||(r=uc(r)),e=e||{};var t=e.coordDimensions||[],n=e.dimensionsDefine||r.dimensionsDefine||[],a=W(),i=[],o=My(r,t,n,e.dimensionsCount),s=e.canOmitUnusedDimensions&&lv(o),l=n===r.dimensionsDefine,u=l?sv(r):ov(n),f=e.encodeDefine;!f&&e.encodeDefaulter&&(f=e.encodeDefaulter(r,o));for(var c=W(f),h=new mc(o),v=0;v<h.length;v++)h[v]=-1;function d(M){var D=h[M];if(D<0){var C=n[M],L=V(C)?C:{name:C},I=new En,P=L.name;P!=null&&u.get(P)!=null&&(I.name=I.displayName=P),L.type!=null&&(I.type=L.type),L.displayName!=null&&(I.displayName=L.displayName);var k=i.length;return h[M]=k,I.storeDimIndex=M,i.push(I),I}return i[D]}if(!s)for(var v=0;v<o;v++)d(v);c.each(function(M,D){var C=be(M).slice();if(C.length===1&&!B(C[0])&&C[0]<0){c.set(D,!1);return}var L=c.set(D,[]);b(C,function(I,P){var k=B(I)?u.get(I):I;k!=null&&k<o&&(L[P]=k,g(d(k),D,P))})});var p=0;b(t,function(M){var D,C,L,I;if(B(M))D=M,I={};else{I=M,D=I.name;var P=I.ordinalMeta;I.ordinalMeta=null,I=N({},I),I.ordinalMeta=P,C=I.dimsDef,L=I.otherDims,I.name=I.coordDim=I.coordDimIndex=I.dimsDef=I.otherDims=null}var k=c.get(D);if(k!==!1){if(k=be(k),!k.length)for(var G=0;G<(C&&C.length||1);G++){for(;p<o&&d(p).coordDim!=null;)p++;p<o&&k.push(p++)}b(k,function(Z,E){var O=d(Z);if(l&&I.type!=null&&(O.type=I.type),g(ee(O,I),D,E),O.name==null&&C){var U=C[E];!V(U)&&(U={name:U}),O.name=O.displayName=U.name,O.defaultTooltip=U.defaultTooltip}L&&ee(O.otherDims,L)})}});function g(M,D,C){tc.get(D)!=null?M.otherDims[D]=C:(M.coordDim=D,M.coordDimIndex=C,a.set(D,!0))}var m=e.generateCoord,y=e.generateCoordCount,_=y!=null;y=m?y||1:0;var S=m||"value";function w(M){M.name==null&&(M.name=M.coordDim)}if(s)b(i,function(M){w(M)}),i.sort(function(M,D){return M.storeDimIndex-D.storeDimIndex});else for(var x=0;x<o;x++){var T=d(x),A=T.coordDim;A==null&&(T.coordDim=Ay(S,a,_),T.coordDimIndex=0,(!m||y<=0)&&(T.isExtraCoord=!0),y--),w(T),T.type==null&&(ic(r,x)===Pe.Must||T.isExtraCoord&&(T.otherDims.itemName!=null||T.otherDims.seriesName!=null))&&(T.type="ordinal")}return Dy(i),new av({source:r,dimensions:i,fullDimensionCount:o,dimensionOmitted:s})}function Dy(r){for(var e=W(),t=0;t<r.length;t++){var n=r[t],a=n.name,i=e.get(a)||0;i>0&&(n.name=a+(i-1)),i++,e.set(a,i)}}function My(r,e,t,n){var a=Math.max(r.dimensionsDetectedCount||1,e.length,t.length,n||0);return b(e,function(i){var o;V(i)&&(o=i.dimsDef)&&(a=Math.max(a,o.length))}),a}function Ay(r,e,t){if(t||e.hasKey(r)){for(var n=0;e.hasKey(r+n);)n++;r+=n}return e.set(r,!0),r}var Iy=function(){function r(e){this.coordSysDims=[],this.axisMap=W(),this.categoryAxisMap=W(),this.coordSysName=e}return r}();function Ly(r){var e=r.get("coordinateSystem"),t=new Iy(e),n=Py[e];if(n)return n(r,t,t.axisMap,t.categoryAxisMap),t}var Py={cartesian2d:function(r,e,t,n){var a=r.getReferringComponents("xAxis",Qe).models[0],i=r.getReferringComponents("yAxis",Qe).models[0];e.coordSysDims=["x","y"],t.set("x",a),t.set("y",i),Qt(a)&&(n.set("x",a),e.firstCategoryDimIndex=0),Qt(i)&&(n.set("y",i),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},singleAxis:function(r,e,t,n){var a=r.getReferringComponents("singleAxis",Qe).models[0];e.coordSysDims=["single"],t.set("single",a),Qt(a)&&(n.set("single",a),e.firstCategoryDimIndex=0)},polar:function(r,e,t,n){var a=r.getReferringComponents("polar",Qe).models[0],i=a.findAxisModel("radiusAxis"),o=a.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],t.set("radius",i),t.set("angle",o),Qt(i)&&(n.set("radius",i),e.firstCategoryDimIndex=0),Qt(o)&&(n.set("angle",o),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},geo:function(r,e,t,n){e.coordSysDims=["lng","lat"]},parallel:function(r,e,t,n){var a=r.ecModel,i=a.getComponent("parallel",r.get("parallelIndex")),o=e.coordSysDims=i.dimensions.slice();b(i.parallelAxisIndex,function(s,l){var u=a.getComponent("parallelAxis",s),f=o[l];t.set(f,u),Qt(u)&&(n.set(f,u),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=l))})}};function Qt(r){return r.get("type")==="category"}function cv(r,e,t){t=t||{};var n=t.byIndex,a=t.stackedCoordDimension,i,o,s;ky(e)?i=e:(o=e.schema,i=o.dimensions,s=e.store);var l=!!(r&&r.get("stack")),u,f,c,h;if(b(i,function(y,_){B(y)&&(i[_]=y={name:y}),l&&!y.isExtraCoord&&(!n&&!u&&y.ordinalMeta&&(u=y),!f&&y.type!=="ordinal"&&y.type!=="time"&&(!a||a===y.coordDim)&&(f=y))}),f&&!n&&!u&&(n=!0),f){c="__\0ecstackresult_"+r.id,h="__\0ecstackedover_"+r.id,u&&(u.createInvertedIndices=!0);var v=f.coordDim,d=f.type,p=0;b(i,function(y){y.coordDim===v&&p++});var g={name:c,coordDim:v,coordDimIndex:p,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},m={name:h,coordDim:h,coordDimIndex:p+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};o?(s&&(g.storeDimIndex=s.ensureCalculationDimension(h,d),m.storeDimIndex=s.ensureCalculationDimension(c,d)),o.appendCalculationDimension(g),o.appendCalculationDimension(m)):(i.push(g),i.push(m))}return{stackedDimension:f&&f.name,stackedByDimension:u&&u.name,isStackedByIndex:n,stackedOverDimension:h,stackResultDimension:c}}function ky(r){return!iv(r.schema)}function Ut(r,e){return!!e&&e===r.getCalculationInfo("stackedDimension")}function vv(r,e){return Ut(r,e)?r.getCalculationInfo("stackResultDimension"):e}function Ey(r,e){var t=r.get("coordinateSystem"),n=ba.get(t),a;return e&&e.coordSysDims&&(a=F(e.coordSysDims,function(i){var o={name:i},s=e.axisMap.get(i);if(s){var l=s.get("type");o.type=my(l)}return o})),a||(a=n&&(n.getDimensionsInfo?n.getDimensionsInfo():n.dimensions.slice())||["x","y"]),a}function Ry(r,e,t){var n,a;return t&&b(r,function(i,o){var s=i.coordDim,l=t.categoryAxisMap.get(s);l&&(n==null&&(n=o),i.ordinalMeta=l.getOrdinalMeta(),e&&(i.createInvertedIndices=!0)),i.otherDims.itemName!=null&&(a=!0)}),!a&&n!=null&&(r[n].otherDims.itemName=0),n}function Aa(r,e,t){t=t||{};var n=e.getSourceManager(),a,i=!1;a=n.getSource(),i=a.sourceFormat===Ze;var o=Ly(e),s=Ey(e,o),l=t.useEncodeDefaulter,u=H(l)?l:l?le(Wp,s,e):null,f={coordDimensions:s,generateCoord:t.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!i},c=fv(a,f),h=Ry(c.dimensions,t.createInvertedIndices,o),v=i?null:n.getSharedDataStore(c),d=cv(e,{schema:c,store:v}),p=new uv(c,e);p.setCalculationInfo(d);var g=h!=null&&Oy(a)?function(m,y,_,S){return S===h?_:this.defaultDimValueGetter(m,y,_,S)}:null;return p.hasItemOption=!1,p.initData(i?a:v,null,g),p}function Oy(r){if(r.sourceFormat===Ze){var e=Ny(r.data||[]);return!R(rn(e))}}function Ny(r){for(var e=0;e<r.length&&r[e]==null;)e++;return r[e]}var nt=function(){function r(e){this._setting=e||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(e){return this._setting[e]},r.prototype.unionExtent=function(e){var t=this._extent;e[0]<t[0]&&(t[0]=e[0]),e[1]>t[1]&&(t[1]=e[1])},r.prototype.unionExtentFromData=function(e,t){this.unionExtent(e.getApproximateExtent(t))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(e,t){var n=this._extent;isNaN(e)||(n[0]=e),isNaN(t)||(n[1]=t)},r.prototype.isInExtentRange=function(e){return this._extent[0]<=e&&this._extent[1]>=e},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(e){this._isBlank=e},r}();ia(nt);var By=0,$i=function(){function r(e){this.categories=e.categories||[],this._needCollect=e.needCollect,this._deduplication=e.deduplication,this.uid=++By}return r.createByAxisModel=function(e){var t=e.option,n=t.data,a=n&&F(n,Fy);return new r({categories:a,needCollect:!a,deduplication:t.dedplication!==!1})},r.prototype.getOrdinal=function(e){return this._getOrCreateMap().get(e)},r.prototype.parseAndCollect=function(e){var t,n=this._needCollect;if(!B(e)&&!n)return e;if(n&&!this._deduplication)return t=this.categories.length,this.categories[t]=e,t;var a=this._getOrCreateMap();return t=a.get(e),t==null&&(n?(t=this.categories.length,this.categories[t]=e,a.set(e,t)):t=NaN),t},r.prototype._getOrCreateMap=function(){return this._map||(this._map=W(this.categories))},r}();function Fy(r){return V(r)&&r.value!=null?r.value:r+""}function Ki(r){return r.type==="interval"||r.type==="log"}function Gy(r,e,t,n){var a={},i=r[1]-r[0],o=a.interval=yo(i/e,!0);t!=null&&o<t&&(o=a.interval=t),n!=null&&o>n&&(o=a.interval=n);var s=a.intervalPrecision=hv(o),l=a.niceTickExtent=[ae(Math.ceil(r[0]/o)*o,s),ae(Math.floor(r[1]/o)*o,s)];return Vy(l,r),a}function ai(r){var e=Math.pow(10,aa(r)),t=r/e;return t?t===2?t=3:t===3?t=5:t*=2:t=1,ae(t*e)}function hv(r){return Je(r)+2}function Vl(r,e,t){r[e]=Math.max(Math.min(r[e],t[1]),t[0])}function Vy(r,e){!isFinite(r[0])&&(r[0]=e[0]),!isFinite(r[1])&&(r[1]=e[1]),Vl(r,0,e),Vl(r,1,e),r[0]>r[1]&&(r[0]=r[1])}function Ia(r,e){return r>=e[0]&&r<=e[1]}function La(r,e){return e[1]===e[0]?.5:(r-e[0])/(e[1]-e[0])}function Pa(r,e){return r*(e[1]-e[0])+e[0]}var ts=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;n.type="ordinal";var a=n.getSetting("ordinalMeta");return a||(a=new $i({})),R(a)&&(a=new $i({categories:F(a,function(i){return V(i)?i.value:i})})),n._ordinalMeta=a,n._extent=n.getSetting("extent")||[0,a.categories.length-1],n}return e.prototype.parse=function(t){return t==null?NaN:B(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return t=this.parse(t),Ia(t,this._extent)&&this._ordinalMeta.categories[t]!=null},e.prototype.normalize=function(t){return t=this._getTickNumber(this.parse(t)),La(t,this._extent)},e.prototype.scale=function(t){return t=Math.round(Pa(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],n=this._extent,a=n[0];a<=n[1];)t.push({value:a}),a++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(t==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var n=t.ordinalNumbers,a=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,l=Math.min(s,n.length);o<l;++o){var u=n[o];a[o]=u,i[u]=o}for(var f=0;o<s;++o){for(;i[f]!=null;)f++;a.push(f),i[f]=o}},e.prototype._getTickNumber=function(t){var n=this._ticksByOrdinalNumber;return n&&t>=0&&t<n.length?n[t]:t},e.prototype.getRawOrdinalNumber=function(t){var n=this._ordinalNumbersByTick;return n&&t>=0&&t<n.length?n[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var n=this.getRawOrdinalNumber(t.value),a=this._ordinalMeta.categories[n];return a==null?"":a+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,n){this.unionExtent(t.getApproximateExtent(n))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(nt);nt.registerClass(ts);var Pt=ae,cr=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}return e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return Ia(t,this._extent)},e.prototype.normalize=function(t){return La(t,this._extent)},e.prototype.scale=function(t){return Pa(t,this._extent)},e.prototype.setExtent=function(t,n){var a=this._extent;isNaN(t)||(a[0]=parseFloat(t)),isNaN(n)||(a[1]=parseFloat(n))},e.prototype.unionExtent=function(t){var n=this._extent;t[0]<n[0]&&(n[0]=t[0]),t[1]>n[1]&&(n[1]=t[1]),this.setExtent(n[0],n[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=hv(t)},e.prototype.getTicks=function(t){var n=this._interval,a=this._extent,i=this._niceExtent,o=this._intervalPrecision,s=[];if(!n)return s;var l=1e4;a[0]<i[0]&&(t?s.push({value:Pt(i[0]-n,o)}):s.push({value:a[0]}));for(var u=i[0];u<=i[1]&&(s.push({value:u}),u=Pt(u+n,o),u!==s[s.length-1].value);)if(s.length>l)return[];var f=s.length?s[s.length-1].value:i[1];return a[1]>f&&(t?s.push({value:Pt(f+n,o)}):s.push({value:a[1]})),s},e.prototype.getMinorTicks=function(t){for(var n=this.getTicks(!0),a=[],i=this.getExtent(),o=1;o<n.length;o++){for(var s=n[o],l=n[o-1],u=0,f=[],c=s.value-l.value,h=c/t;u<t-1;){var v=Pt(l.value+(u+1)*h);v>i[0]&&v<i[1]&&f.push(v),u++}a.push(f)}return a},e.prototype.getLabel=function(t,n){if(t==null)return"";var a=n&&n.precision;a==null?a=Je(t.value)||0:a==="auto"&&(a=this._intervalPrecision);var i=Pt(t.value,a,!0);return No(i)},e.prototype.calcNiceTicks=function(t,n,a){t=t||5;var i=this._extent,o=i[1]-i[0];if(isFinite(o)){o<0&&(o=-o,i.reverse());var s=Gy(i,t,n,a);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var n=this._extent;if(n[0]===n[1])if(n[0]!==0){var a=Math.abs(n[0]);t.fixMax||(n[1]+=a/2),n[0]-=a/2}else n[1]=1;var i=n[1]-n[0];isFinite(i)||(n[0]=0,n[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var o=this._interval;t.fixMin||(n[0]=Pt(Math.floor(n[0]/o)*o)),t.fixMax||(n[1]=Pt(Math.ceil(n[1]/o)*o))},e.prototype.setNiceExtent=function(t,n){this._niceExtent=[t,n]},e.type="interval",e}(nt);nt.registerClass(cr);var dv=typeof Float32Array<"u",zy=dv?Float32Array:Array;function it(r){return R(r)?dv?new Float32Array(r):r:new zy(r)}var Hy="__ec_stack_";function pv(r){return r.get("stack")||Hy+r.seriesIndex}function rs(r){return r.dim+r.index}function gv(r,e){var t=[];return e.eachSeriesByType(r,function(n){yv(n)&&t.push(n)}),t}function Wy(r){var e={};b(r,function(l){var u=l.coordinateSystem,f=u.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var c=l.getData(),h=f.dim+"_"+f.index,v=c.getDimensionIndex(c.mapDimension(f.dim)),d=c.getStore(),p=0,g=d.count();p<g;++p){var m=d.get(v,p);e[h]?e[h].push(m):e[h]=[m]}});var t={};for(var n in e)if(e.hasOwnProperty(n)){var a=e[n];if(a){a.sort(function(l,u){return l-u});for(var i=null,o=1;o<a.length;++o){var s=a[o]-a[o-1];s>0&&(i=i===null?s:Math.min(i,s))}t[n]=i}}return t}function mv(r){var e=Wy(r),t=[];return b(r,function(n){var a=n.coordinateSystem,i=a.getBaseAxis(),o=i.getExtent(),s;if(i.type==="category")s=i.getBandWidth();else if(i.type==="value"||i.type==="time"){var l=i.dim+"_"+i.index,u=e[l],f=Math.abs(o[1]-o[0]),c=i.scale.getExtent(),h=Math.abs(c[1]-c[0]);s=u?f/h*u:f}else{var v=n.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=ve(n.get("barWidth"),s),p=ve(n.get("barMaxWidth"),s),g=ve(n.get("barMinWidth")||(_v(n)?.5:1),s),m=n.get("barGap"),y=n.get("barCategoryGap");t.push({bandWidth:s,barWidth:d,barMaxWidth:p,barMinWidth:g,barGap:m,barCategoryGap:y,axisKey:rs(i),stackId:pv(n)})}),Uy(t)}function Uy(r){var e={};b(r,function(n,a){var i=n.axisKey,o=n.bandWidth,s=e[i]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},l=s.stacks;e[i]=s;var u=n.stackId;l[u]||s.autoWidthCount++,l[u]=l[u]||{width:0,maxWidth:0};var f=n.barWidth;f&&!l[u].width&&(l[u].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var c=n.barMaxWidth;c&&(l[u].maxWidth=c);var h=n.barMinWidth;h&&(l[u].minWidth=h);var v=n.barGap;v!=null&&(s.gap=v);var d=n.barCategoryGap;d!=null&&(s.categoryGap=d)});var t={};return b(e,function(n,a){t[a]={};var i=n.stacks,o=n.bandWidth,s=n.categoryGap;if(s==null){var l=Ye(i).length;s=Math.max(35-l*4,15)+"%"}var u=ve(s,o),f=ve(n.gap,1),c=n.remainedWidth,h=n.autoWidthCount,v=(c-u)/(h+(h-1)*f);v=Math.max(v,0),b(i,function(m){var y=m.maxWidth,_=m.minWidth;if(m.width){var S=m.width;y&&(S=Math.min(S,y)),_&&(S=Math.max(S,_)),m.width=S,c-=S+f*S,h--}else{var S=v;y&&y<S&&(S=Math.min(y,c)),_&&_>S&&(S=_),S!==v&&(m.width=S,c-=S+f*S,h--)}}),v=(c-u)/(h+(h-1)*f),v=Math.max(v,0);var d=0,p;b(i,function(m,y){m.width||(m.width=v),p=m,d+=m.width*(1+f)}),p&&(d-=p.width*f);var g=-d/2;b(i,function(m,y){t[a][y]=t[a][y]||{bandWidth:o,offset:g,width:m.width},g+=m.width*(1+f)})}),t}function Yy(r,e,t){if(r&&e){var n=r[rs(e)];return n}}function Xy(r,e){var t=gv(r,e),n=mv(t);b(t,function(a){var i=a.getData(),o=a.coordinateSystem,s=o.getBaseAxis(),l=pv(a),u=n[rs(s)][l],f=u.offset,c=u.width;i.setLayout({bandWidth:u.bandWidth,offset:f,size:c})})}function Zy(r){return{seriesType:r,plan:Yo(),reset:function(e){if(yv(e)){var t=e.getData(),n=e.coordinateSystem,a=n.getBaseAxis(),i=n.getOtherAxis(a),o=t.getDimensionIndex(t.mapDimension(i.dim)),s=t.getDimensionIndex(t.mapDimension(a.dim)),l=e.get("showBackground",!0),u=t.mapDimension(i.dim),f=t.getCalculationInfo("stackResultDimension"),c=Ut(t,u)&&!!t.getCalculationInfo("stackedOnSeries"),h=i.isHorizontal(),v=$y(a,i),d=_v(e),p=e.get("barMinHeight")||0,g=f&&t.getDimensionIndex(f),m=t.getLayout("size"),y=t.getLayout("offset");return{progress:function(_,S){for(var w=_.count,x=d&&it(w*3),T=d&&l&&it(w*3),A=d&&it(w),M=n.master.getRect(),D=h?M.width:M.height,C,L=S.getStore(),I=0;(C=_.next())!=null;){var P=L.get(c?g:o,C),k=L.get(s,C),G=v,Z=void 0;c&&(Z=+P-L.get(o,C));var E=void 0,O=void 0,U=void 0,q=void 0;if(h){var Te=n.dataToPoint([P,k]);if(c){var ce=n.dataToPoint([Z,k]);G=ce[0]}E=G,O=Te[1]+y,U=Te[0]-G,q=m,Math.abs(U)<p&&(U=(U<0?-1:1)*p)}else{var Te=n.dataToPoint([k,P]);if(c){var ce=n.dataToPoint([k,Z]);G=ce[1]}E=Te[0]+y,O=G,U=m,q=Te[1]-G,Math.abs(q)<p&&(q=(q<=0?-1:1)*p)}d?(x[I]=E,x[I+1]=O,x[I+2]=h?U:q,T&&(T[I]=h?M.x:E,T[I+1]=h?O:M.y,T[I+2]=D),A[C]=C):S.setItemLayout(C,{x:E,y:O,width:U,height:q}),I+=3}d&&S.setLayout({largePoints:x,largeDataIndices:A,largeBackgroundPoints:T,valueAxisHorizontal:h})}}}}}}function yv(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function _v(r){return r.pipelineContext&&r.pipelineContext.large}function $y(r,e){var t=e.model.get("startValue");return t||(t=0),e.toGlobalCoord(e.dataToCoord(e.type==="log"?t>0?t:1:t))}var Ky=function(r,e,t,n){for(;t<n;){var a=t+n>>>1;r[a][1]<e?t=a+1:n=a}return t},Sv=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;return n.type="time",n}return e.prototype.getLabel=function(t){var n=this.getSetting("useUTC");return an(t.value,Ws[Pp(nr(this._minLevelUnit))]||Ws.second,n,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,n,a){var i=this.getSetting("useUTC"),o=this.getSetting("locale");return kp(t,n,a,o,i)},e.prototype.getTicks=function(){var t=this._interval,n=this._extent,a=[];if(!t)return a;a.push({value:n[0],level:0});var i=this.getSetting("useUTC"),o=r_(this._minLevelUnit,this._approxInterval,i,n);return a=a.concat(o),a.push({value:n[1],level:0}),a},e.prototype.calcNiceExtent=function(t){var n=this._extent;if(n[0]===n[1]&&(n[0]-=ze,n[1]+=ze),n[1]===-1/0&&n[0]===1/0){var a=new Date;n[1]=+new Date(a.getFullYear(),a.getMonth(),a.getDate()),n[0]=n[1]-ze}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,n,a){t=t||10;var i=this._extent,o=i[1]-i[0];this._approxInterval=o/t,n!=null&&this._approxInterval<n&&(this._approxInterval=n),a!=null&&this._approxInterval>a&&(this._approxInterval=a);var s=yn.length,l=Math.min(Ky(yn,this._approxInterval,0,s),s-1);this._interval=yn[l][1],this._minLevelUnit=yn[Math.max(l-1,0)][0]},e.prototype.parse=function(t){return oe(t)?t:+We(t)},e.prototype.contain=function(t){return Ia(this.parse(t),this._extent)},e.prototype.normalize=function(t){return La(this.parse(t),this._extent)},e.prototype.scale=function(t){return Pa(t,this._extent)},e.type="time",e}(cr),yn=[["second",Eo],["minute",Ro],["hour",Or],["quarter-day",Or*6],["half-day",Or*12],["day",ze*1.2],["half-week",ze*3.5],["week",ze*7],["month",ze*31],["quarter",ze*95],["half-year",Hs/2],["year",Hs]];function qy(r,e,t,n){var a=We(e),i=We(t),o=function(d){return Us(a,d,n)===Us(i,d,n)},s=function(){return o("year")},l=function(){return s()&&o("month")},u=function(){return l()&&o("day")},f=function(){return u()&&o("hour")},c=function(){return f()&&o("minute")},h=function(){return c()&&o("second")},v=function(){return h()&&o("millisecond")};switch(r){case"year":return s();case"month":return l();case"day":return u();case"hour":return f();case"minute":return c();case"second":return h();case"millisecond":return v()}}function jy(r,e){return r/=ze,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function Qy(r){var e=30*ze;return r/=e,r>6?6:r>3?3:r>2?2:1}function Jy(r){return r/=Or,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function zl(r,e){return r/=e?Ro:Eo,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function e_(r){return yo(r,!0)}function t_(r,e,t){var n=new Date(r);switch(nr(e)){case"year":case"month":n[Zf(t)](0);case"day":n[$f(t)](1);case"hour":n[Kf(t)](0);case"minute":n[qf(t)](0);case"second":n[jf(t)](0),n[Qf(t)](0)}return n.getTime()}function r_(r,e,t,n){var a=1e4,i=Yf,o=0;function s(D,C,L,I,P,k,G){for(var Z=new Date(C),E=C,O=Z[I]();E<L&&E<=n[1];)G.push({value:E}),O+=D,Z[P](O),E=Z.getTime();G.push({value:E,notAdd:!0})}function l(D,C,L){var I=[],P=!C.length;if(!qy(nr(D),n[0],n[1],t)){P&&(C=[{value:t_(new Date(n[0]),D,t)},{value:n[1]}]);for(var k=0;k<C.length-1;k++){var G=C[k].value,Z=C[k+1].value;if(G!==Z){var E=void 0,O=void 0,U=void 0,q=!1;switch(D){case"year":E=Math.max(1,Math.round(e/ze/365)),O=Oo(t),U=Ep(t);break;case"half-year":case"quarter":case"month":E=Qy(e),O=ar(t),U=Zf(t);break;case"week":case"half-week":case"day":E=jy(e),O=ma(t),U=$f(t),q=!0;break;case"half-day":case"quarter-day":case"hour":E=Jy(e),O=Zr(t),U=Kf(t);break;case"minute":E=zl(e,!0),O=ya(t),U=qf(t);break;case"second":E=zl(e,!1),O=_a(t),U=jf(t);break;case"millisecond":E=e_(e),O=Sa(t),U=Qf(t);break}s(E,G,Z,O,U,q,I),D==="year"&&L.length>1&&k===0&&L.unshift({value:L[0].value-E})}}for(var k=0;k<I.length;k++)L.push(I[k]);return I}}for(var u=[],f=[],c=0,h=0,v=0;v<i.length&&o++<a;++v){var d=nr(i[v]);if(Lp(i[v])){l(i[v],u[u.length-1]||[],f);var p=i[v+1]?nr(i[v+1]):null;if(d!==p){if(f.length){h=c,f.sort(function(D,C){return D.value-C.value});for(var g=[],m=0;m<f.length;++m){var y=f[m].value;(m===0||f[m-1].value!==y)&&(g.push(f[m]),y>=n[0]&&y<=n[1]&&c++)}var _=(n[1]-n[0])/e;if(c>_*1.5&&h>_/1.5||(u.push(g),c>_||r===i[v]))break}f=[]}}}for(var S=se(F(u,function(D){return se(D,function(C){return C.value>=n[0]&&C.value<=n[1]&&!C.notAdd})}),function(D){return D.length>0}),w=[],x=S.length-1,v=0;v<S.length;++v)for(var T=S[v],A=0;A<T.length;++A)w.push({value:T[A].value,level:x-v});w.sort(function(D,C){return D.value-C.value});for(var M=[],v=0;v<w.length;++v)(v===0||w[v].value!==w[v-1].value)&&M.push(w[v]);return M}nt.registerClass(Sv);var Hl=nt.prototype,Gr=cr.prototype,n_=ae,a_=Math.floor,i_=Math.ceil,_n=Math.pow,Ke=Math.log,ns=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new cr,t._interval=0,t}return e.prototype.getTicks=function(t){var n=this._originalScale,a=this._extent,i=n.getExtent(),o=Gr.getTicks.call(this,t);return F(o,function(s){var l=s.value,u=ae(_n(this.base,l));return u=l===a[0]&&this._fixMin?Sn(u,i[0]):u,u=l===a[1]&&this._fixMax?Sn(u,i[1]):u,{value:u}},this)},e.prototype.setExtent=function(t,n){var a=Ke(this.base);t=Ke(Math.max(0,t))/a,n=Ke(Math.max(0,n))/a,Gr.setExtent.call(this,t,n)},e.prototype.getExtent=function(){var t=this.base,n=Hl.getExtent.call(this);n[0]=_n(t,n[0]),n[1]=_n(t,n[1]);var a=this._originalScale,i=a.getExtent();return this._fixMin&&(n[0]=Sn(n[0],i[0])),this._fixMax&&(n[1]=Sn(n[1],i[1])),n},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var n=this.base;t[0]=Ke(t[0])/Ke(n),t[1]=Ke(t[1])/Ke(n),Hl.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,n){this.unionExtent(t.getApproximateExtent(n))},e.prototype.calcNiceTicks=function(t){t=t||10;var n=this._extent,a=n[1]-n[0];if(!(a===1/0||a<=0)){var i=uf(a),o=t/a*i;for(o<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var s=[ae(i_(n[0]/i)*i),ae(a_(n[1]/i)*i)];this._interval=i,this._niceExtent=s}},e.prototype.calcNiceExtent=function(t){Gr.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return t=Ke(t)/Ke(this.base),Ia(t,this._extent)},e.prototype.normalize=function(t){return t=Ke(t)/Ke(this.base),La(t,this._extent)},e.prototype.scale=function(t){return t=Pa(t,this._extent),_n(this.base,t)},e.type="log",e}(nt),xv=ns.prototype;xv.getMinorTicks=Gr.getMinorTicks;xv.getLabel=Gr.getLabel;function Sn(r,e){return n_(r,Je(e))}nt.registerClass(ns);var o_=function(){function r(e,t,n){this._prepareParams(e,t,n)}return r.prototype._prepareParams=function(e,t,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var a=this._isOrdinal=e.type==="ordinal";this._needCrossZero=e.type==="interval"&&t.getNeedCrossZero&&t.getNeedCrossZero();var i=t.get("min",!0);i==null&&(i=t.get("startValue",!0));var o=this._modelMinRaw=i;H(o)?this._modelMinNum=xn(e,o({min:n[0],max:n[1]})):o!=="dataMin"&&(this._modelMinNum=xn(e,o));var s=this._modelMaxRaw=t.get("max",!0);if(H(s)?this._modelMaxNum=xn(e,s({min:n[0],max:n[1]})):s!=="dataMax"&&(this._modelMaxNum=xn(e,s)),a)this._axisDataLen=t.getCategories().length;else{var l=t.get("boundaryGap"),u=R(l)?l:[l||0,l||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Ti(u[0],1),Ti(u[1],1)]}},r.prototype.calculate=function(){var e=this._isOrdinal,t=this._dataMin,n=this._dataMax,a=this._axisDataLen,i=this._boundaryGapInner,o=e?null:n-t||Math.abs(t),s=this._modelMinRaw==="dataMin"?t:this._modelMinNum,l=this._modelMaxRaw==="dataMax"?n:this._modelMaxNum,u=s!=null,f=l!=null;s==null&&(s=e?a?0:NaN:t-i[0]*o),l==null&&(l=e?a?a-1:NaN:n+i[1]*o),(s==null||!isFinite(s))&&(s=NaN),(l==null||!isFinite(l))&&(l=NaN);var c=Ci(s)||Ci(l)||e&&!a;this._needCrossZero&&(s>0&&l>0&&!u&&(s=0),s<0&&l<0&&!f&&(l=0));var h=this._determinedMin,v=this._determinedMax;return h!=null&&(s=h,u=!0),v!=null&&(l=v,f=!0),{min:s,max:l,minFixed:u,maxFixed:f,isBlank:c}},r.prototype.modifyDataMinMax=function(e,t){this[l_[e]]=t},r.prototype.setDeterminedMinMax=function(e,t){var n=s_[e];this[n]=t},r.prototype.freeze=function(){this.frozen=!0},r}(),s_={min:"_determinedMin",max:"_determinedMax"},l_={min:"_dataMin",max:"_dataMax"};function u_(r,e,t){var n=r.rawExtentInfo;return n||(n=new o_(r,e,t),r.rawExtentInfo=n,n)}function xn(r,e){return e==null?null:Ci(e)?NaN:r.parse(e)}function bv(r,e){var t=r.type,n=u_(r,e,r.getExtent()).calculate();r.setBlank(n.isBlank);var a=n.min,i=n.max,o=e.ecModel;if(o&&t==="time"){var s=gv("bar",o),l=!1;if(b(s,function(c){l=l||c.getBaseAxis()===e.axis}),l){var u=mv(s),f=f_(a,i,e,u);a=f.min,i=f.max}}return{extent:[a,i],fixMin:n.minFixed,fixMax:n.maxFixed}}function f_(r,e,t,n){var a=t.axis.getExtent(),i=Math.abs(a[1]-a[0]),o=Yy(n,t.axis);if(o===void 0)return{min:r,max:e};var s=1/0;b(o,function(v){s=Math.min(v.offset,s)});var l=-1/0;b(o,function(v){l=Math.max(v.offset+v.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,f=e-r,c=1-(s+l)/i,h=f/c-f;return e+=h*(l/u),r-=h*(s/u),{min:r,max:e}}function qi(r,e){var t=e,n=bv(r,t),a=n.extent,i=t.get("splitNumber");r instanceof ns&&(r.base=t.get("logBase"));var o=r.type,s=t.get("interval"),l=o==="interval"||o==="time";r.setExtent(a[0],a[1]),r.calcNiceExtent({splitNumber:i,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:l?t.get("minInterval"):null,maxInterval:l?t.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function wv(r,e){if(e=e||r.get("type"),e)switch(e){case"category":return new ts({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new Sv({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(nt.getClass(e)||cr)}}function c_(r){var e=r.scale.getExtent(),t=e[0],n=e[1];return!(t>0&&n>0||t<0&&n<0)}function vr(r){var e=r.getLabelModel().get("formatter"),t=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(n){return function(a,i){return r.scale.getFormattedLabel(a,i,n)}}(e):B(e)?function(n){return function(a){var i=r.scale.getLabel(a),o=n.replace("{value}",i??"");return o}}(e):H(e)?function(n){return function(a,i){return t!=null&&(i=a.value-t),n(as(r,a),i,a.level!=null?{level:a.level}:null)}}(e):function(n){return r.scale.getLabel(n)}}function as(r,e){return r.type==="category"?r.scale.getLabel(e):e.value}function v_(r){var e=r.model,t=r.scale;if(!(!e.get(["axisLabel","show"])||t.isBlank())){var n,a,i=t.getExtent();t instanceof ts?a=t.count():(n=t.getTicks(),a=n.length);var o=r.getLabelModel(),s=vr(r),l,u=1;a>40&&(u=Math.ceil(a/40));for(var f=0;f<a;f+=u){var c=n?n[f]:{value:i[0]+f},h=s(c,f),v=o.getTextRect(h),d=h_(v,o.get("rotate")||0);l?l.union(d):l=d}return l}}function h_(r,e){var t=e*Math.PI/180,n=r.width,a=r.height,i=n*Math.abs(Math.cos(t))+Math.abs(a*Math.sin(t)),o=n*Math.abs(Math.sin(t))+Math.abs(a*Math.cos(t)),s=new Ee(r.x,r.y,i,o);return s}function is(r){var e=r.get("interval");return e??"auto"}function Tv(r){return r.type==="category"&&is(r.getLabelModel())===0}function d_(r,e){var t={};return b(r.mapDimensionsAll(e),function(n){t[vv(r,n)]=!0}),Ye(t)}var Cv=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var e=this.option;return!e.scale},r.prototype.getCoordSysModel=function(){},r}();function p_(r){return Aa(null,r)}var g_={isDimensionStacked:Ut,enableDataStack:cv,getStackedDimension:vv};function m_(r,e){var t=e;e instanceof J||(t=new J(e));var n=wv(t);return n.setExtent(r[0],r[1]),qi(n,t),n}function y_(r){ft(r,Cv)}function __(r,e){return e=e||{},zt(r,null,null,e.state!=="normal")}const S_=Object.freeze(Object.defineProperty({__proto__:null,createDimensions:Cy,createList:p_,createScale:m_,createSymbol:Wt,createTextStyle:__,dataStack:g_,enableHoverEmphasis:Vn,getECData:Y,getLayoutRect:$r,mixinAxisModelCommonMethods:y_},Symbol.toStringTag,{value:"Module"}));var Wl=[],x_={registerPreprocessor:jo,registerProcessor:Qo,registerPostInit:Qc,registerPostUpdate:Jc,registerUpdateLifecycle:Ma,registerAction:Yt,registerCoordinateSystem:ev,registerLayout:tv,registerVisual:Dt,registerTransform:nv,registerLoading:es,registerMap:rv,registerImpl:Vm,PRIORITY:Wc,ComponentModel:X,ComponentView:Be,SeriesModel:Ne,ChartView:Me,registerComponentModel:function(r){X.registerClass(r)},registerComponentView:function(r){Be.registerClass(r)},registerSeriesModel:function(r){Ne.registerClass(r)},registerChartView:function(r){Me.registerClass(r)},registerSubTypeDefaulter:function(r,e){X.registerSubTypeDefaulter(r,e)},registerPainter:function(r,e){Eh(r,e)}};function Ct(r){if(R(r)){b(r,function(e){Ct(e)});return}ie(Wl,r)>=0||(Wl.push(r),H(r)&&(r={install:r}),r.install(x_))}var b_=[];function ii(r,e){for(var t=0;t<r.length;t++)ut(r[t],r[t],e)}function Ul(r,e,t,n){for(var a=0;a<r.length;a++){var i=r[a];n&&(i=n.project(i)),i&&isFinite(i[0])&&isFinite(i[1])&&(Rh(e,e,i),Oh(t,t,i))}}function w_(r){for(var e=0,t=0,n=0,a=r.length,i=r[a-1][0],o=r[a-1][1],s=0;s<a;s++){var l=r[s][0],u=r[s][1],f=i*u-l*o;e+=f,t+=(i+l)*f,n+=(o+u)*f,i=l,o=u}return e?[t/e/3,n/e/3,e]:[r[0][0]||0,r[0][1]||0]}var Dv=function(){function r(e){this.name=e}return r.prototype.setCenter=function(e){this._center=e},r.prototype.getCenter=function(){var e=this._center;return e||(e=this._center=this.calcCenter()),e},r}(),Yl=function(){function r(e,t){this.type="polygon",this.exterior=e,this.interiors=t}return r}(),Xl=function(){function r(e){this.type="linestring",this.points=e}return r}(),T_=function(r){z(e,r);function e(t,n,a){var i=r.call(this,t)||this;return i.type="geoJSON",i.geometries=n,i._center=a&&[a[0],a[1]],i}return e.prototype.calcCenter=function(){for(var t=this.geometries,n,a=0,i=0;i<t.length;i++){var o=t[i],s=o.exterior,l=s&&s.length;l>a&&(n=o,a=l)}if(n)return w_(n.exterior);var u=this.getBoundingRect();return[u.x+u.width/2,u.y+u.height/2]},e.prototype.getBoundingRect=function(t){var n=this._rect;if(n&&!t)return n;var a=[1/0,1/0],i=[-1/0,-1/0],o=this.geometries;return b(o,function(s){s.type==="polygon"?Ul(s.exterior,a,i,t):b(s.points,function(l){Ul(l,a,i,t)})}),isFinite(a[0])&&isFinite(a[1])&&isFinite(i[0])&&isFinite(i[1])||(a[0]=a[1]=i[0]=i[1]=0),n=new Ee(a[0],a[1],i[0]-a[0],i[1]-a[1]),t||(this._rect=n),n},e.prototype.contain=function(t){var n=this.getBoundingRect(),a=this.geometries;if(!n.contain(t[0],t[1]))return!1;e:for(var i=0,o=a.length;i<o;i++){var s=a[i];if(s.type==="polygon"){var l=s.exterior,u=s.interiors;if(gs(l,t[0],t[1])){for(var f=0;f<(u?u.length:0);f++)if(gs(u[f],t[0],t[1]))continue e;return!0}}}return!1},e.prototype.transformTo=function(t,n,a,i){var o=this.getBoundingRect(),s=o.width/o.height;a?i||(i=a/s):a=s*i;for(var l=new Ee(t,n,a,i),u=o.calculateTransform(l),f=this.geometries,c=0;c<f.length;c++){var h=f[c];h.type==="polygon"?(ii(h.exterior,u),b(h.interiors,function(v){ii(v,u)})):b(h.points,function(v){ii(v,u)})}o=this._rect,o.copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},e.prototype.cloneShallow=function(t){t==null&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e}(Dv);(function(r){z(e,r);function e(t,n){var a=r.call(this,t)||this;return a.type="geoSVG",a._elOnlyForCalculate=n,a}return e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,n=t.getBoundingRect(),a=[n.x+n.width/2,n.y+n.height/2],i=fo(b_),o=t;o&&!o.isGeoSVGGraphicRoot;)On(i,o.getLocalTransform(),i),o=o.parent;return Jn(i,i),ut(a,a,i),a},e})(Dv);function C_(r){if(!r.UTF8Encoding)return r;var e=r,t=e.UTF8Scale;t==null&&(t=1024);var n=e.features;return b(n,function(a){var i=a.geometry,o=i.encodeOffsets,s=i.coordinates;if(o)switch(i.type){case"LineString":i.coordinates=Mv(s,o,t);break;case"Polygon":oi(s,o,t);break;case"MultiLineString":oi(s,o,t);break;case"MultiPolygon":b(s,function(l,u){return oi(l,o[u],t)})}}),e.UTF8Encoding=!1,e}function oi(r,e,t){for(var n=0;n<r.length;n++)r[n]=Mv(r[n],e[n],t)}function Mv(r,e,t){for(var n=[],a=e[0],i=e[1],o=0;o<r.length;o+=2){var s=r.charCodeAt(o)-64,l=r.charCodeAt(o+1)-64;s=s>>1^-(s&1),l=l>>1^-(l&1),s+=a,l+=i,a=s,i=l,n.push([s/t,l/t])}return n}function Zl(r,e){return r=C_(r),F(se(r.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var n=t.properties,a=t.geometry,i=[];switch(a.type){case"Polygon":var o=a.coordinates;i.push(new Yl(o[0],o.slice(1)));break;case"MultiPolygon":b(a.coordinates,function(l){l[0]&&i.push(new Yl(l[0],l.slice(1)))});break;case"LineString":i.push(new Xl([a.coordinates]));break;case"MultiLineString":i.push(new Xl(a.coordinates))}var s=new T_(n[e||"name"],i,n.cp);return s.properties=n,s})}const D_=Object.freeze(Object.defineProperty({__proto__:null,MAX_SAFE_INTEGER:id,asc:td,getPercentWithPrecision:rd,getPixelPrecision:lf,getPrecision:Je,getPrecisionSafe:sf,isNumeric:ff,isRadianAroundZero:Hr,linearMap:Di,nice:yo,numericToNumber:Wr,parseDate:We,quantile:sd,quantity:uf,quantityExponent:aa,reformIntervals:ld,remRadian:mo,round:ae},Symbol.toStringTag,{value:"Module"})),M_=Object.freeze(Object.defineProperty({__proto__:null,format:an,parse:We},Symbol.toStringTag,{value:"Module"})),A_=Object.freeze(Object.defineProperty({__proto__:null,Arc:ra,BezierCurve:po,BoundingRect:Ee,Circle:ea,CompoundPath:qu,Ellipse:co,Group:me,Image:sr,IncrementalDisplayable:ju,Line:wt,LinearGradient:go,Polygon:ho,Polyline:ta,RadialGradient:Qu,Rect:fe,Ring:vo,Sector:lr,Text:we,clipPointsByRect:Bf,clipRectByRect:Ff,createIcon:fa,extendPath:Pf,extendShape:Lf,getShapeClass:kf,getTransform:Of,initProps:Oe,makeImage:Mo,makePath:ua,mergePath:Rf,registerShape:Xe,resizePath:Ao,updateProps:he},Symbol.toStringTag,{value:"Module"})),I_=Object.freeze(Object.defineProperty({__proto__:null,addCommas:No,capitalFirst:Np,encodeHTML:xe,formatTime:Op,formatTpl:Fo,getTextRect:Rp,getTooltipMarker:Jf,normalizeCssArray:on,toCamelCase:Bo,truncateText:Nh},Symbol.toStringTag,{value:"Module"})),L_=Object.freeze(Object.defineProperty({__proto__:null,bind:Q,clone:$,curry:le,defaults:ee,each:b,extend:N,filter:se,indexOf:ie,inherits:$u,isArray:R,isFunction:H,isObject:V,isString:B,map:F,merge:j,reduce:Vr},Symbol.toStringTag,{value:"Module"}));var Jr=re();function Av(r,e){var t=F(e,function(n){return r.scale.parse(n)});return r.type==="time"&&t.length>0&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function P_(r){var e=r.getLabelModel().get("customValues");if(e){var t=vr(r),n=r.scale.getExtent(),a=Av(r,e),i=se(a,function(o){return o>=n[0]&&o<=n[1]});return{labels:F(i,function(o){var s={value:o};return{formattedLabel:t(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?E_(r):O_(r)}function k_(r,e){var t=r.getTickModel().get("customValues");if(t){var n=r.scale.getExtent(),a=Av(r,t);return{ticks:se(a,function(i){return i>=n[0]&&i<=n[1]})}}return r.type==="category"?R_(r,e):{ticks:F(r.scale.getTicks(),function(i){return i.value})}}function E_(r){var e=r.getLabelModel(),t=Iv(r,e);return!e.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:t.labelCategoryInterval}:t}function Iv(r,e){var t=Lv(r,"labels"),n=is(e),a=Pv(t,n);if(a)return a;var i,o;return H(n)?i=Rv(r,n):(o=n==="auto"?N_(r):n,i=Ev(r,o)),kv(t,n,{labels:i,labelCategoryInterval:o})}function R_(r,e){var t=Lv(r,"ticks"),n=is(e),a=Pv(t,n);if(a)return a;var i,o;if((!e.get("show")||r.scale.isBlank())&&(i=[]),H(n))i=Rv(r,n,!0);else if(n==="auto"){var s=Iv(r,r.getLabelModel());o=s.labelCategoryInterval,i=F(s.labels,function(l){return l.tickValue})}else o=n,i=Ev(r,o,!0);return kv(t,n,{ticks:i,tickCategoryInterval:o})}function O_(r){var e=r.scale.getTicks(),t=vr(r);return{labels:F(e,function(n,a){return{level:n.level,formattedLabel:t(n,a),rawLabel:r.scale.getLabel(n),tickValue:n.value}})}}function Lv(r,e){return Jr(r)[e]||(Jr(r)[e]=[])}function Pv(r,e){for(var t=0;t<r.length;t++)if(r[t].key===e)return r[t].value}function kv(r,e,t){return r.push({key:e,value:t}),t}function N_(r){var e=Jr(r).autoInterval;return e??(Jr(r).autoInterval=r.calculateCategoryInterval())}function B_(r){var e=F_(r),t=vr(r),n=(e.axisRotate-e.labelRotate)/180*Math.PI,a=r.scale,i=a.getExtent(),o=a.count();if(i[1]-i[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=i[0],u=r.dataToCoord(l+1)-r.dataToCoord(l),f=Math.abs(u*Math.cos(n)),c=Math.abs(u*Math.sin(n)),h=0,v=0;l<=i[1];l+=s){var d=0,p=0,g=rf(t({value:l}),e.font,"center","top");d=g.width*1.3,p=g.height*1.3,h=Math.max(h,d,7),v=Math.max(v,p,7)}var m=h/f,y=v/c;isNaN(m)&&(m=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(m,y))),S=Jr(r.model),w=r.getExtent(),x=S.lastAutoInterval,T=S.lastTickCount;return x!=null&&T!=null&&Math.abs(x-_)<=1&&Math.abs(T-o)<=1&&x>_&&S.axisExtent0===w[0]&&S.axisExtent1===w[1]?_=x:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=w[0],S.axisExtent1=w[1]),_}function F_(r){var e=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function Ev(r,e,t){var n=vr(r),a=r.scale,i=a.getExtent(),o=r.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=i[0],f=a.count();u!==0&&l>1&&f/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=Tv(r),h=o.get("showMinLabel")||c,v=o.get("showMaxLabel")||c;h&&u!==i[0]&&p(i[0]);for(var d=u;d<=i[1];d+=l)p(d);v&&d-l!==i[1]&&p(i[1]);function p(g){var m={value:g};s.push(t?g:{formattedLabel:n(m),rawLabel:a.getLabel(m),tickValue:g})}return s}function Rv(r,e,t){var n=r.scale,a=vr(r),i=[];return b(n.getTicks(),function(o){var s=n.getLabel(o),l=o.value;e(o.value,s)&&i.push(t?l:{formattedLabel:a(o),rawLabel:s,tickValue:l})}),i}var $l=[0,1],Ov=function(){function r(e,t,n){this.onBand=!1,this.inverse=!1,this.dim=e,this.scale=t,this._extent=n||[0,0]}return r.prototype.contain=function(e){var t=this._extent,n=Math.min(t[0],t[1]),a=Math.max(t[0],t[1]);return e>=n&&e<=a},r.prototype.containData=function(e){return this.scale.contain(e)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(e){return lf(e||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(e,t){var n=this._extent;n[0]=e,n[1]=t},r.prototype.dataToCoord=function(e,t){var n=this._extent,a=this.scale;return e=a.normalize(e),this.onBand&&a.type==="ordinal"&&(n=n.slice(),Kl(n,a.count())),Di(e,$l,n,t)},r.prototype.coordToData=function(e,t){var n=this._extent,a=this.scale;this.onBand&&a.type==="ordinal"&&(n=n.slice(),Kl(n,a.count()));var i=Di(e,n,$l,t);return this.scale.scale(i)},r.prototype.pointToData=function(e,t){},r.prototype.getTicksCoords=function(e){e=e||{};var t=e.tickModel||this.getTickModel(),n=k_(this,t),a=n.ticks,i=F(a,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=t.get("alignWithLabel");return G_(this,i,o,e.clamp),i},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var e=this.model.getModel("minorTick"),t=e.get("splitNumber");t>0&&t<100||(t=5);var n=this.scale.getMinorTicks(t),a=F(n,function(i){return F(i,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return a},r.prototype.getViewLabels=function(){return P_(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var e=this._extent,t=this.scale.getExtent(),n=t[1]-t[0]+(this.onBand?1:0);n===0&&(n=1);var a=Math.abs(e[1]-e[0]);return Math.abs(a)/n},r.prototype.calculateCategoryInterval=function(){return B_(this)},r}();function Kl(r,e){var t=r[1]-r[0],n=e,a=t/n/2;r[0]+=a,r[1]-=a}function G_(r,e,t,n){var a=e.length;if(!r.onBand||t||!a)return;var i=r.getExtent(),o,s;if(a===1)e[0].coord=i[0],o=e[1]={coord:i[1],tickValue:e[0].tickValue};else{var l=e[a-1].tickValue-e[0].tickValue,u=(e[a-1].coord-e[0].coord)/l;b(e,function(v){v.coord-=u/2});var f=r.scale.getExtent();s=1+f[1]-e[a-1].tickValue,o={coord:e[a-1].coord+u*s,tickValue:f[1]+1},e.push(o)}var c=i[0]>i[1];h(e[0].coord,i[0])&&(n?e[0].coord=i[0]:e.shift()),n&&h(i[0],e[0].coord)&&e.unshift({coord:i[0]}),h(i[1],o.coord)&&(n?o.coord=i[1]:e.pop()),n&&h(o.coord,i[1])&&e.push({coord:i[1]});function h(v,d){return v=ae(v),d=ae(d),c?v>d:v<d}}function V_(r){var e=X.extend(r);return X.registerClass(e),e}function z_(r){var e=Be.extend(r);return Be.registerClass(e),e}function H_(r){var e=Ne.extend(r);return Ne.registerClass(e),e}function W_(r){var e=Me.extend(r);return Me.registerClass(e),e}var Tr=Math.PI*2,kt=nf.CMD,U_=["top","right","bottom","left"];function Y_(r,e,t,n,a){var i=t.width,o=t.height;switch(r){case"top":n.set(t.x+i/2,t.y-e),a.set(0,-1);break;case"bottom":n.set(t.x+i/2,t.y+o+e),a.set(0,1);break;case"left":n.set(t.x-e,t.y+o/2),a.set(-1,0);break;case"right":n.set(t.x+i+e,t.y+o/2),a.set(1,0);break}}function X_(r,e,t,n,a,i,o,s,l){o-=r,s-=e;var u=Math.sqrt(o*o+s*s);o/=u,s/=u;var f=o*t+r,c=s*t+e;if(Math.abs(n-a)%Tr<1e-4)return l[0]=f,l[1]=c,u-t;if(i){var h=n;n=Ar(a),a=Ar(h)}else n=Ar(n),a=Ar(a);n>a&&(a+=Tr);var v=Math.atan2(s,o);if(v<0&&(v+=Tr),v>=n&&v<=a||v+Tr>=n&&v+Tr<=a)return l[0]=f,l[1]=c,u-t;var d=t*Math.cos(n)+r,p=t*Math.sin(n)+e,g=t*Math.cos(a)+r,m=t*Math.sin(a)+e,y=(d-o)*(d-o)+(p-s)*(p-s),_=(g-o)*(g-o)+(m-s)*(m-s);return y<_?(l[0]=d,l[1]=p,Math.sqrt(y)):(l[0]=g,l[1]=m,Math.sqrt(_))}function ji(r,e,t,n,a,i,o,s){var l=a-r,u=i-e,f=t-r,c=n-e,h=Math.sqrt(f*f+c*c);f/=h,c/=h;var v=l*f+u*c,d=v/h;s&&(d=Math.min(Math.max(d,0),1)),d*=h;var p=o[0]=r+d*f,g=o[1]=e+d*c;return Math.sqrt((p-a)*(p-a)+(g-i)*(g-i))}function Nv(r,e,t,n,a,i,o){t<0&&(r=r+t,t=-t),n<0&&(e=e+n,n=-n);var s=r+t,l=e+n,u=o[0]=Math.min(Math.max(a,r),s),f=o[1]=Math.min(Math.max(i,e),l);return Math.sqrt((u-a)*(u-a)+(f-i)*(f-i))}var je=[];function Z_(r,e,t){var n=Nv(e.x,e.y,e.width,e.height,r.x,r.y,je);return t.set(je[0],je[1]),n}function $_(r,e,t){for(var n=0,a=0,i=0,o=0,s,l,u=1/0,f=e.data,c=r.x,h=r.y,v=0;v<f.length;){var d=f[v++];v===1&&(n=f[v],a=f[v+1],i=n,o=a);var p=u;switch(d){case kt.M:i=f[v++],o=f[v++],n=i,a=o;break;case kt.L:p=ji(n,a,f[v],f[v+1],c,h,je,!0),n=f[v++],a=f[v++];break;case kt.C:p=Fh(n,a,f[v++],f[v++],f[v++],f[v++],f[v],f[v+1],c,h,je),n=f[v++],a=f[v++];break;case kt.Q:p=Bh(n,a,f[v++],f[v++],f[v],f[v+1],c,h,je),n=f[v++],a=f[v++];break;case kt.A:var g=f[v++],m=f[v++],y=f[v++],_=f[v++],S=f[v++],w=f[v++];v+=1;var x=!!(1-f[v++]);s=Math.cos(S)*y+g,l=Math.sin(S)*_+m,v<=1&&(i=s,o=l);var T=(c-g)*_/y+g;p=X_(g,m,_,S,S+w,x,T,h,je),n=Math.cos(S+w)*y+g,a=Math.sin(S+w)*_+m;break;case kt.R:i=n=f[v++],o=a=f[v++];var A=f[v++],M=f[v++];p=Nv(i,o,A,M,c,h,je);break;case kt.Z:p=ji(n,a,i,o,c,h,je,!0),n=i,a=o;break}p<u&&(u=p,t.set(je[0],je[1]))}return u}var Ot=new He,ue=new He,ke=new He,er=new He,Cr=new He;function ql(r,e){if(r){var t=r.getTextGuideLine(),n=r.getTextContent();if(n&&t){var a=r.textGuideLineConfig||{},i=[[0,0],[0,0],[0,0]],o=a.candidates||U_,s=n.getBoundingRect().clone();s.applyTransform(n.getComputedTransform());var l=1/0,u=a.anchor,f=r.getComputedTransform(),c=f&&Jn([],f),h=e.get("length2")||0;u&&ke.copy(u);for(var v=0;v<o.length;v++){var d=o[v];Y_(d,0,s,Ot,er),He.scaleAndAdd(ue,Ot,er,h),ue.transform(c);var p=r.getBoundingRect(),g=u?u.distance(ue):r instanceof Ie?$_(ue,r.path,ke):Z_(ue,p,ke);g<l&&(l=g,ue.transform(f),ke.transform(f),ke.toArray(i[0]),ue.toArray(i[1]),Ot.toArray(i[2]))}K_(i,e.get("minTurnAngle")),t.setShape({points:i})}}}var jl=[],Et=new He;function K_(r,e){if(e<=180&&e>0){e=e/180*Math.PI,Ot.fromArray(r[0]),ue.fromArray(r[1]),ke.fromArray(r[2]),He.sub(er,Ot,ue),He.sub(Cr,ke,ue);var t=er.len(),n=Cr.len();if(!(t<.001||n<.001)){er.scale(1/t),Cr.scale(1/n);var a=er.dot(Cr),i=Math.cos(e);if(i<a){var o=ji(ue.x,ue.y,ke.x,ke.y,Ot.x,Ot.y,jl,!1);Et.fromArray(jl),Et.scaleAndAdd(Cr,o/Math.tan(Math.PI-e));var s=ke.x!==ue.x?(Et.x-ue.x)/(ke.x-ue.x):(Et.y-ue.y)/(ke.y-ue.y);if(isNaN(s))return;s<0?He.copy(Et,ue):s>1&&He.copy(Et,ke),Et.toArray(r[1])}}}}function si(r,e,t,n){var a=t==="normal",i=a?r:r.ensureState(t);i.ignore=e;var o=n.get("smooth");o&&o===!0&&(o=.3),i.shape=i.shape||{},o>0&&(i.shape.smooth=o);var s=n.getModel("lineStyle").getLineStyle();a?r.useStyle(s):i.style=s}function q_(r,e){var t=e.smooth,n=e.points;if(n)if(r.moveTo(n[0][0],n[0][1]),t>0&&n.length>=3){var a=ms(n[0],n[1]),i=ms(n[1],n[2]);if(!a||!i){r.lineTo(n[1][0],n[1][1]),r.lineTo(n[2][0],n[2][1]);return}var o=Math.min(a,i)*t,s=Ea([],n[1],n[0],o/a),l=Ea([],n[1],n[2],o/i),u=Ea([],s,l,.5);r.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),r.bezierCurveTo(l[0],l[1],l[0],l[1],n[2][0],n[2][1])}else for(var f=1;f<n.length;f++)r.lineTo(n[f][0],n[f][1])}function j_(r,e,t){var n=r.getTextGuideLine(),a=r.getTextContent();if(!a){n&&r.removeTextGuideLine();return}for(var i=e.normal,o=i.get("show"),s=a.ignore,l=0;l<Bn.length;l++){var u=Bn[l],f=e[u],c=u==="normal";if(f){var h=f.get("show"),v=c?s:K(a.states[u]&&a.states[u].ignore,s);if(v||!K(h,o)){var d=c?n:n&&n.states[u];d&&(d.ignore=!0),n&&si(n,!0,u,f);continue}n||(n=new ta,r.setTextGuideLine(n),!c&&(s||!o)&&si(n,!0,"normal",e.normal),r.stateProxy&&(n.stateProxy=r.stateProxy)),si(n,!1,u,f)}}if(n){ee(n.style,t),n.style.fill=null;var p=i.get("showAbove"),g=r.textGuideLineConfig=r.textGuideLineConfig||{};g.showAbove=p||!1,n.buildPath=q_}}function Q_(r,e){e=e||"labelLine";for(var t={normal:r.getModel(e)},n=0;n<Ue.length;n++){var a=Ue[n];t[a]=r.getModel([a,e])}return t}function Bv(r){for(var e=[],t=0;t<r.length;t++){var n=r[t];if(!n.defaultAttr.ignore){var a=n.label,i=a.getComputedTransform(),o=a.getBoundingRect(),s=!i||i[1]<1e-5&&i[2]<1e-5,l=a.style.margin||0,u=o.clone();u.applyTransform(i),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var f=s?new Nn(o,i):null;e.push({label:a,labelLine:n.labelLine,rect:u,localRect:o,obb:f,priority:n.priority,defaultAttr:n.defaultAttr,layoutOption:n.computedLayoutOption,axisAligned:s,transform:i})}}return e}function Fv(r,e,t,n,a,i){var o=r.length;if(o<2)return;r.sort(function(x,T){return x.rect[e]-T.rect[e]});for(var s=0,l,u=!1,f=0;f<o;f++){var c=r[f],h=c.rect;l=h[e]-s,l<0&&(h[e]-=l,c.label[e]-=l,u=!0),s=h[e]+h[t]}var v=r[0],d=r[o-1],p,g;m(),p<0&&S(-p,.8),g<0&&S(g,.8),m(),y(p,g,1),y(g,p,-1),m(),p<0&&w(-p),g<0&&w(g);function m(){p=v.rect[e]-n,g=a-d.rect[e]-d.rect[t]}function y(x,T,A){if(x<0){var M=Math.min(T,-x);if(M>0){_(M*A,0,o);var D=M+x;D<0&&S(-D*A,1)}else S(-x*A,1)}}function _(x,T,A){x!==0&&(u=!0);for(var M=T;M<A;M++){var D=r[M],C=D.rect;C[e]+=x,D.label[e]+=x}}function S(x,T){for(var A=[],M=0,D=1;D<o;D++){var C=r[D-1].rect,L=Math.max(r[D].rect[e]-C[e]-C[t],0);A.push(L),M+=L}if(M){var I=Math.min(Math.abs(x)/M,T);if(x>0)for(var D=0;D<o-1;D++){var P=A[D]*I;_(P,0,D+1)}else for(var D=o-1;D>0;D--){var P=A[D-1]*I;_(-P,D,o)}}}function w(x){var T=x<0?-1:1;x=Math.abs(x);for(var A=Math.ceil(x/(o-1)),M=0;M<o-1;M++)if(T>0?_(A,0,M+1):_(-A,o-M-1,o),x-=A,x<=0)return}return u}function J_(r,e,t,n){return Fv(r,"x","width",e,t)}function e0(r,e,t,n){return Fv(r,"y","height",e,t)}function Gv(r){var e=[];r.sort(function(p,g){return g.priority-p.priority});var t=new Ee(0,0,0,0);function n(p){if(!p.ignore){var g=p.ensureState("emphasis");g.ignore==null&&(g.ignore=!1)}p.ignore=!0}for(var a=0;a<r.length;a++){var i=r[a],o=i.axisAligned,s=i.localRect,l=i.transform,u=i.label,f=i.labelLine;t.copy(i.rect),t.width-=.1,t.height-=.1,t.x+=.05,t.y+=.05;for(var c=i.obb,h=!1,v=0;v<e.length;v++){var d=e[v];if(t.intersect(d.rect)){if(o&&d.axisAligned){h=!0;break}if(d.obb||(d.obb=new Nn(d.localRect,d.transform)),c||(c=new Nn(s,l)),c.intersect(d.obb)){h=!0;break}}}h?(n(u),f&&n(f)):(u.attr("ignore",i.defaultAttr.ignore),f&&f.attr("ignore",i.defaultAttr.labelGuideIgnore),e.push(i))}}function t0(r){if(r){for(var e=[],t=0;t<r.length;t++)e.push(r[t].slice());return e}}function r0(r,e){var t=r.label,n=e&&e.getTextGuideLine();return{dataIndex:r.dataIndex,dataType:r.dataType,seriesIndex:r.seriesModel.seriesIndex,text:r.label.style.text,rect:r.hostRect,labelRect:r.rect,align:t.style.align,verticalAlign:t.style.verticalAlign,labelLinePoints:t0(n&&n.shape.points)}}var Ql=["align","verticalAlign","width","height","fontSize"],_e=new Ku,li=re(),n0=re();function bn(r,e,t){for(var n=0;n<t.length;n++){var a=t[n];e[a]!=null&&(r[a]=e[a])}}var wn=["x","y","rotation"],a0=function(){function r(){this._labelList=[],this._chartViewList=[]}return r.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},r.prototype._addLabel=function(e,t,n,a,i){var o=a.style,s=a.__hostTarget,l=s.textConfig||{},u=a.getComputedTransform(),f=a.getBoundingRect().plain();Ee.applyTransform(f,f,u),u?_e.setLocalTransform(u):(_e.x=_e.y=_e.rotation=_e.originX=_e.originY=0,_e.scaleX=_e.scaleY=1),_e.rotation=Ar(_e.rotation);var c=a.__hostTarget,h;if(c){h=c.getBoundingRect().plain();var v=c.getComputedTransform();Ee.applyTransform(h,h,v)}var d=h&&c.getTextGuideLine();this._labelList.push({label:a,labelLine:d,seriesModel:n,dataIndex:e,dataType:t,layoutOption:i,computedLayoutOption:null,rect:f,hostRect:h,priority:h?h.width*h.height:0,defaultAttr:{ignore:a.ignore,labelGuideIgnore:d&&d.ignore,x:_e.x,y:_e.y,scaleX:_e.scaleX,scaleY:_e.scaleY,rotation:_e.rotation,style:{x:o.x,y:o.y,align:o.align,verticalAlign:o.verticalAlign,width:o.width,height:o.height,fontSize:o.fontSize},cursor:a.cursor,attachedPos:l.position,attachedRot:l.rotation}})},r.prototype.addLabelsOfSeries=function(e){var t=this;this._chartViewList.push(e);var n=e.__model,a=n.get("labelLayout");(H(a)||Ye(a).length)&&e.group.traverse(function(i){if(i.ignore)return!0;var o=i.getTextContent(),s=Y(i);o&&!o.disableLabelLayout&&t._addLabel(s.dataIndex,s.dataType,n,o,a)})},r.prototype.updateLayoutConfig=function(e){var t=e.getWidth(),n=e.getHeight();function a(_,S){return function(){ql(_,S)}}for(var i=0;i<this._labelList.length;i++){var o=this._labelList[i],s=o.label,l=s.__hostTarget,u=o.defaultAttr,f=void 0;H(o.layoutOption)?f=o.layoutOption(r0(o,l)):f=o.layoutOption,f=f||{},o.computedLayoutOption=f;var c=Math.PI/180;l&&l.setTextConfig({local:!1,position:f.x!=null||f.y!=null?null:u.attachedPos,rotation:f.rotate!=null?f.rotate*c:u.attachedRot,offset:[f.dx||0,f.dy||0]});var h=!1;if(f.x!=null?(s.x=ve(f.x,t),s.setStyle("x",0),h=!0):(s.x=u.x,s.setStyle("x",u.style.x)),f.y!=null?(s.y=ve(f.y,n),s.setStyle("y",0),h=!0):(s.y=u.y,s.setStyle("y",u.style.y)),f.labelLinePoints){var v=l.getTextGuideLine();v&&(v.setShape({points:f.labelLinePoints}),h=!1)}var d=li(s);d.needsUpdateLabelLine=h,s.rotation=f.rotate!=null?f.rotate*c:u.rotation,s.scaleX=u.scaleX,s.scaleY=u.scaleY;for(var p=0;p<Ql.length;p++){var g=Ql[p];s.setStyle(g,f[g]!=null?f[g]:u.style[g])}if(f.draggable){if(s.draggable=!0,s.cursor="move",l){var m=o.seriesModel;if(o.dataIndex!=null){var y=o.seriesModel.getData(o.dataType);m=y.getItemModel(o.dataIndex)}s.on("drag",a(l,m.getModel("labelLine")))}}else s.off("drag"),s.cursor=u.cursor}},r.prototype.layout=function(e){var t=e.getWidth(),n=e.getHeight(),a=Bv(this._labelList),i=se(a,function(l){return l.layoutOption.moveOverlap==="shiftX"}),o=se(a,function(l){return l.layoutOption.moveOverlap==="shiftY"});J_(i,0,t),e0(o,0,n);var s=se(a,function(l){return l.layoutOption.hideOverlap});Gv(s)},r.prototype.processLabelsOverall=function(){var e=this;b(this._chartViewList,function(t){var n=t.__model,a=t.ignoreLabelLineUpdate,i=n.isAnimationEnabled();t.group.traverse(function(o){if(o.ignore&&!o.forceLabelAnimation)return!0;var s=!a,l=o.getTextContent();!s&&l&&(s=li(l).needsUpdateLabelLine),s&&e._updateLabelLine(o,n),i&&e._animateLabels(o,n)})})},r.prototype._updateLabelLine=function(e,t){var n=e.getTextContent(),a=Y(e),i=a.dataIndex;if(n&&i!=null){var o=t.getData(a.dataType),s=o.getItemModel(i),l={},u=o.getItemVisual(i,"style");if(u){var f=o.getVisual("drawType");l.stroke=u[f]}var c=s.getModel("labelLine");j_(e,Q_(s),l),ql(e,c)}},r.prototype._animateLabels=function(e,t){var n=e.getTextContent(),a=e.getTextGuideLine();if(n&&(e.forceLabelAnimation||!n.ignore&&!n.invisible&&!e.disableLabelAnimation&&!tr(e))){var i=li(n),o=i.oldLayout,s=Y(e),l=s.dataIndex,u={x:n.x,y:n.y,rotation:n.rotation},f=t.getData(s.dataType);if(o){n.attr(o);var h=e.prevStates;h&&(ie(h,"select")>=0&&n.attr(i.oldLayoutSelect),ie(h,"emphasis")>=0&&n.attr(i.oldLayoutEmphasis)),he(n,u,t,l)}else if(n.attr(u),!ur(n).valueAnimation){var c=K(n.style.opacity,1);n.style.opacity=0,Oe(n,{style:{opacity:c}},t,l)}if(i.oldLayout=u,n.states.select){var v=i.oldLayoutSelect={};bn(v,u,wn),bn(v,n.states.select,wn)}if(n.states.emphasis){var d=i.oldLayoutEmphasis={};bn(d,u,wn),bn(d,n.states.emphasis,wn)}pp(n,l,f,t,t)}if(a&&!a.ignore&&!a.invisible){var i=n0(a),o=i.oldLayout,p={points:a.shape.points};o?(a.attr({shape:o}),he(a,{shape:p},t)):(a.setShape(p),a.style.strokePercent=0,Oe(a,{style:{strokePercent:1}},t)),i.oldLayout=p}},r}(),ui=re();function xx(r){r.registerUpdateLifecycle("series:beforeupdate",function(e,t,n){var a=ui(t).labelManager;a||(a=ui(t).labelManager=new a0),a.clearLabels()}),r.registerUpdateLifecycle("series:layoutlabels",function(e,t,n){var a=ui(t).labelManager;n.updatedSeries.forEach(function(i){a.addLabelsOfSeries(t.getViewOfSeriesModel(i))}),a.updateLayoutConfig(t),a.layout(t),a.processLabelsOverall()})}const bx=Object.freeze(Object.defineProperty({__proto__:null,Axis:Ov,ChartView:Me,ComponentModel:X,ComponentView:Be,List:uv,Model:J,PRIORITY:Wc,SeriesModel:Ne,color:Gh,connect:oy,dataTool:hy,dependencies:Hm,disConnect:sy,disconnect:jc,dispose:ly,env:te,extendChartView:W_,extendComponentModel:V_,extendComponentView:z_,extendSeriesModel:H_,format:I_,getCoordinateSystemDimensions:fy,getInstanceByDom:Ko,getInstanceById:uy,getMap:vy,graphic:A_,helper:S_,init:iy,innerDrawElementOnCanvas:ef,matrix:Vh,number:D_,parseGeoJSON:Zl,parseGeoJson:Zl,registerAction:Yt,registerCoordinateSystem:ev,registerLayout:tv,registerLoading:es,registerLocale:ko,registerMap:rv,registerPostInit:Qc,registerPostUpdate:Jc,registerPreprocessor:jo,registerProcessor:Qo,registerTheme:qo,registerTransform:nv,registerUpdateLifecycle:Ma,registerVisual:Dt,setCanvasCreator:cy,setPlatformAPI:tf,throttle:Ca,time:M_,use:Ct,util:L_,vector:zh,version:zm,zrUtil:Hh,zrender:Wh},Symbol.toStringTag,{value:"Module"}));var i0=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t){return Aa(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var n=new me,a=Wt("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);n.add(a),a.setStyle(t.lineStyle);var i=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=i==="none"?"circle":i,l=t.itemHeight*.8,u=Wt(s,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);n.add(u),u.setStyle(t.itemStyle);var f=t.iconRotate==="inherit"?o:t.iconRotate||0;return u.rotation=f*Math.PI/180,u.setOrigin([t.itemWidth/2,t.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),n},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(Ne);function os(r,e){var t=r.mapDimensionsAll("defaultedLabel"),n=t.length;if(n===1){var a=or(r,e,t[0]);return a!=null?a+"":null}else if(n){for(var i=[],o=0;o<t.length;o++)i.push(or(r,e,t[o]));return i.join(" ")}}function Vv(r,e){var t=r.mapDimensionsAll("defaultedLabel");if(!R(e))return e+"";for(var n=[],a=0;a<t.length;a++){var i=r.getDimensionIndex(t[a]);i>=0&&n.push(e[i])}return n.join(" ")}var ss=function(r){z(e,r);function e(t,n,a,i){var o=r.call(this)||this;return o.updateData(t,n,a,i),o}return e.prototype._createSymbol=function(t,n,a,i,o){this.removeAll();var s=Wt(t,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),s.drift=o0,this._symbolType=t,this.add(s)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){Fn(this.childAt(0))},e.prototype.downplay=function(){Gn(this.childAt(0))},e.prototype.setZ=function(t,n){var a=this.childAt(0);a.zlevel=t,a.z=n},e.prototype.setDraggable=function(t,n){var a=this.childAt(0);a.draggable=t,a.cursor=!n&&t?"move":a.cursor},e.prototype.updateData=function(t,n,a,i){this.silent=!1;var o=t.getItemVisual(n,"symbol")||"circle",s=t.hostModel,l=e.getSymbolSize(t,n),u=o!==this._symbolType,f=i&&i.disableAnimation;if(u){var c=t.getItemVisual(n,"symbolKeepAspect");this._createSymbol(o,t,n,l,c)}else{var h=this.childAt(0);h.silent=!1;var v={scaleX:l[0]/2,scaleY:l[1]/2};f?h.attr(v):he(h,v,s,n),If(h)}if(this._updateCommon(t,n,l,a,i),u){var h=this.childAt(0);if(!f){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:h.style.opacity}};h.scaleX=h.scaleY=0,h.style.opacity=0,Oe(h,v,s,n)}}f&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,n,a,i,o){var s=this.childAt(0),l=t.hostModel,u,f,c,h,v,d,p,g,m;if(i&&(u=i.emphasisItemStyle,f=i.blurItemStyle,c=i.selectItemStyle,h=i.focus,v=i.blurScope,p=i.labelStatesModels,g=i.hoverScale,m=i.cursorStyle,d=i.emphasisDisabled),!i||t.hasItemOption){var y=i&&i.itemModel?i.itemModel:t.getItemModel(n),_=y.getModel("emphasis");u=_.getModel("itemStyle").getItemStyle(),c=y.getModel(["select","itemStyle"]).getItemStyle(),f=y.getModel(["blur","itemStyle"]).getItemStyle(),h=_.get("focus"),v=_.get("blurScope"),d=_.get("disabled"),p=pa(y),g=_.getShallow("scale"),m=y.getShallow("cursor")}var S=t.getItemVisual(n,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var w=Rc(t.getItemVisual(n,"symbolOffset"),a);w&&(s.x=w[0],s.y=w[1]),m&&s.attr("cursor",m);var x=t.getItemVisual(n,"style"),T=x.fill;if(s instanceof sr){var A=s.style;s.useStyle(N({image:A.image,x:A.x,y:A.y,width:A.width,height:A.height},x))}else s.__isEmptyBrush?s.useStyle(N({},x)):s.useStyle(x),s.style.decal=null,s.setColor(T,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var M=t.getItemVisual(n,"liftZ"),D=this._z2;M!=null?D==null&&(this._z2=s.z2,s.z2+=M):D!=null&&(s.z2=D,this._z2=null);var C=o&&o.useNameLabel;da(s,p,{labelFetcher:l,labelDataIndex:n,defaultText:L,inheritColor:T,defaultOpacity:x.opacity});function L(k){return C?t.getName(k):os(t,k)}this._sizeX=a[0]/2,this._sizeY=a[1]/2;var I=s.ensureState("emphasis");I.style=u,s.ensureState("select").style=c,s.ensureState("blur").style=f;var P=g==null||g===!0?Math.max(1.1,3/this._sizeY):isFinite(g)&&g>0?+g:1;I.scaleX=this._sizeX*P,I.scaleY=this._sizeY*P,this.setSymbolScale(1),zn(this,h,v,d)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,n,a){var i=this.childAt(0),o=Y(this).dataIndex,s=a&&a.animation;if(this.silent=i.silent=!0,a&&a.fadeLabel){var l=i.getTextContent();l&&Hn(l,{style:{opacity:0}},n,{dataIndex:o,removeOpt:s,cb:function(){i.removeTextContent()}})}else i.removeTextContent();Hn(i,{style:{opacity:0},scaleX:0,scaleY:0},n,{dataIndex:o,cb:t,removeOpt:s})},e.getSymbolSize=function(t,n){return Nm(t.getItemVisual(n,"symbolSize"))},e}(me);function o0(r,e){this.parent.drift(r,e)}function fi(r,e,t,n){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(n.isIgnore&&n.isIgnore(t))&&!(n.clipShape&&!n.clipShape.contain(e[0],e[1]))&&r.getItemVisual(t,"symbol")!=="none"}function Jl(r){return r!=null&&!V(r)&&(r={isIgnore:r}),r||{}}function eu(r){var e=r.hostModel,t=e.getModel("emphasis");return{emphasisItemStyle:t.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:t.get("focus"),blurScope:t.get("blurScope"),emphasisDisabled:t.get("disabled"),hoverScale:t.get("scale"),labelStatesModels:pa(e),cursorStyle:e.get("cursor")}}var s0=function(){function r(e){this.group=new me,this._SymbolCtor=e||ss}return r.prototype.updateData=function(e,t){this._progressiveEls=null,t=Jl(t);var n=this.group,a=e.hostModel,i=this._data,o=this._SymbolCtor,s=t.disableAnimation,l=eu(e),u={disableAnimation:s},f=t.getSymbolPoint||function(c){return e.getItemLayout(c)};i||n.removeAll(),e.diff(i).add(function(c){var h=f(c);if(fi(e,h,c,t)){var v=new o(e,c,l,u);v.setPosition(h),e.setItemGraphicEl(c,v),n.add(v)}}).update(function(c,h){var v=i.getItemGraphicEl(h),d=f(c);if(!fi(e,d,c,t)){n.remove(v);return}var p=e.getItemVisual(c,"symbol")||"circle",g=v&&v.getSymbolType&&v.getSymbolType();if(!v||g&&g!==p)n.remove(v),v=new o(e,c,l,u),v.setPosition(d);else{v.updateData(e,c,l,u);var m={x:d[0],y:d[1]};s?v.attr(m):he(v,m,a)}n.add(v),e.setItemGraphicEl(c,v)}).remove(function(c){var h=i.getItemGraphicEl(c);h&&h.fadeOut(function(){n.remove(h)},a)}).execute(),this._getSymbolPoint=f,this._data=e},r.prototype.updateLayout=function(){var e=this,t=this._data;t&&t.eachItemGraphicEl(function(n,a){var i=e._getSymbolPoint(a);n.setPosition(i),n.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=eu(e),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(e,t,n){this._progressiveEls=[],n=Jl(n);function a(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var i=e.start;i<e.end;i++){var o=t.getItemLayout(i);if(fi(t,o,i,n)){var s=new this._SymbolCtor(t,i,this._seriesScope);s.traverse(a),s.setPosition(o),this.group.add(s),t.setItemGraphicEl(i,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(e){va(this._progressiveEls||this.group,e)},r.prototype.remove=function(e){var t=this.group,n=this._data;n&&e?n.eachItemGraphicEl(function(a){a.fadeOut(function(){t.remove(a)},n.hostModel)}):t.removeAll()},r}();function zv(r,e,t){var n=r.getBaseAxis(),a=r.getOtherAxis(n),i=l0(a,t),o=n.dim,s=a.dim,l=e.mapDimension(s),u=e.mapDimension(o),f=s==="x"||s==="radius"?1:0,c=F(r.dimensions,function(d){return e.mapDimension(d)}),h=!1,v=e.getCalculationInfo("stackResultDimension");return Ut(e,c[0])&&(h=!0,c[0]=v),Ut(e,c[1])&&(h=!0,c[1]=v),{dataDimsForPoint:c,valueStart:i,valueAxisDim:s,baseAxisDim:o,stacked:!!h,valueDim:l,baseDim:u,baseDataOffset:f,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function l0(r,e){var t=0,n=r.scale.getExtent();return e==="start"?t=n[0]:e==="end"?t=n[1]:oe(e)&&!isNaN(e)?t=e:n[0]>0?t=n[0]:n[1]<0&&(t=n[1]),t}function Hv(r,e,t,n){var a=NaN;r.stacked&&(a=t.get(t.getCalculationInfo("stackedOverDimension"),n)),isNaN(a)&&(a=r.valueStart);var i=r.baseDataOffset,o=[];return o[i]=t.get(r.baseDim,n),o[1-i]=a,e.dataToPoint(o)}function u0(r,e){var t=[];return e.diff(r).add(function(n){t.push({cmd:"+",idx:n})}).update(function(n,a){t.push({cmd:"=",idx:a,idx1:n})}).remove(function(n){t.push({cmd:"-",idx:n})}).execute(),t}function f0(r,e,t,n,a,i,o,s){for(var l=u0(r,e),u=[],f=[],c=[],h=[],v=[],d=[],p=[],g=zv(a,e,o),m=r.getLayout("points")||[],y=e.getLayout("points")||[],_=0;_<l.length;_++){var S=l[_],w=!0,x=void 0,T=void 0;switch(S.cmd){case"=":x=S.idx*2,T=S.idx1*2;var A=m[x],M=m[x+1],D=y[T],C=y[T+1];(isNaN(A)||isNaN(M))&&(A=D,M=C),u.push(A,M),f.push(D,C),c.push(t[x],t[x+1]),h.push(n[T],n[T+1]),p.push(e.getRawIndex(S.idx1));break;case"+":var L=S.idx,I=g.dataDimsForPoint,P=a.dataToPoint([e.get(I[0],L),e.get(I[1],L)]);T=L*2,u.push(P[0],P[1]),f.push(y[T],y[T+1]);var k=Hv(g,a,e,L);c.push(k[0],k[1]),h.push(n[T],n[T+1]),p.push(e.getRawIndex(L));break;case"-":w=!1}w&&(v.push(S),d.push(d.length))}d.sort(function(ye,hr){return p[ye]-p[hr]});for(var G=u.length,Z=it(G),E=it(G),O=it(G),U=it(G),q=[],_=0;_<d.length;_++){var Te=d[_],ce=_*2,Le=Te*2;Z[ce]=u[Le],Z[ce+1]=u[Le+1],E[ce]=f[Le],E[ce+1]=f[Le+1],O[ce]=c[Le],O[ce+1]=c[Le+1],U[ce]=h[Le],U[ce+1]=h[Le+1],q[_]=v[Te]}return{current:Z,next:E,stackedOnCurrent:O,stackedOnNext:U,status:q}}var gt=Math.min,mt=Math.max;function Ft(r,e){return isNaN(r)||isNaN(e)}function Qi(r,e,t,n,a,i,o,s,l){for(var u,f,c,h,v,d,p=t,g=0;g<n;g++){var m=e[p*2],y=e[p*2+1];if(p>=a||p<0)break;if(Ft(m,y)){if(l){p+=i;continue}break}if(p===t)r[i>0?"moveTo":"lineTo"](m,y),c=m,h=y;else{var _=m-u,S=y-f;if(_*_+S*S<.5){p+=i;continue}if(o>0){for(var w=p+i,x=e[w*2],T=e[w*2+1];x===m&&T===y&&g<n;)g++,w+=i,p+=i,x=e[w*2],T=e[w*2+1],m=e[p*2],y=e[p*2+1],_=m-u,S=y-f;var A=g+1;if(l)for(;Ft(x,T)&&A<n;)A++,w+=i,x=e[w*2],T=e[w*2+1];var M=.5,D=0,C=0,L=void 0,I=void 0;if(A>=n||Ft(x,T))v=m,d=y;else{D=x-u,C=T-f;var P=m-u,k=x-m,G=y-f,Z=T-y,E=void 0,O=void 0;if(s==="x"){E=Math.abs(P),O=Math.abs(k);var U=D>0?1:-1;v=m-U*E*o,d=y,L=m+U*O*o,I=y}else if(s==="y"){E=Math.abs(G),O=Math.abs(Z);var q=C>0?1:-1;v=m,d=y-q*E*o,L=m,I=y+q*O*o}else E=Math.sqrt(P*P+G*G),O=Math.sqrt(k*k+Z*Z),M=O/(O+E),v=m-D*o*(1-M),d=y-C*o*(1-M),L=m+D*o*M,I=y+C*o*M,L=gt(L,mt(x,m)),I=gt(I,mt(T,y)),L=mt(L,gt(x,m)),I=mt(I,gt(T,y)),D=L-m,C=I-y,v=m-D*E/O,d=y-C*E/O,v=gt(v,mt(u,m)),d=gt(d,mt(f,y)),v=mt(v,gt(u,m)),d=mt(d,gt(f,y)),D=m-v,C=y-d,L=m+D*O/E,I=y+C*O/E}r.bezierCurveTo(c,h,v,d,m,y),c=L,h=I}else r.lineTo(m,y)}u=m,f=y,p+=i}return g}var Wv=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),c0=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;return n.type="ec-polyline",n}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Wv},e.prototype.buildPath=function(t,n){var a=n.points,i=0,o=a.length/2;if(n.connectNulls){for(;o>0&&Ft(a[o*2-2],a[o*2-1]);o--);for(;i<o&&Ft(a[i*2],a[i*2+1]);i++);}for(;i<o;)i+=Qi(t,a,i,o,o,1,n.smooth,n.smoothMonotone,n.connectNulls)+1},e.prototype.getPointOn=function(t,n){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var a=this.path,i=a.data,o=nf.CMD,s,l,u=n==="x",f=[],c=0;c<i.length;){var h=i[c++],v=void 0,d=void 0,p=void 0,g=void 0,m=void 0,y=void 0,_=void 0;switch(h){case o.M:s=i[c++],l=i[c++];break;case o.L:if(v=i[c++],d=i[c++],_=u?(t-s)/(v-s):(t-l)/(d-l),_<=1&&_>=0){var S=u?(d-l)*_+l:(v-s)*_+s;return u?[t,S]:[S,t]}s=v,l=d;break;case o.C:v=i[c++],d=i[c++],p=i[c++],g=i[c++],m=i[c++],y=i[c++];var w=u?ys(s,v,p,m,t,f):ys(l,d,g,y,t,f);if(w>0)for(var x=0;x<w;x++){var T=f[x];if(T<=1&&T>=0){var S=u?_s(l,d,g,y,T):_s(s,v,p,m,T);return u?[t,S]:[S,t]}}s=m,l=y;break}}},e}(Ie),v0=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e}(Wv),h0=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;return n.type="ec-polygon",n}return e.prototype.getDefaultShape=function(){return new v0},e.prototype.buildPath=function(t,n){var a=n.points,i=n.stackedOnPoints,o=0,s=a.length/2,l=n.smoothMonotone;if(n.connectNulls){for(;s>0&&Ft(a[s*2-2],a[s*2-1]);s--);for(;o<s&&Ft(a[o*2],a[o*2+1]);o++);}for(;o<s;){var u=Qi(t,a,o,s,s,1,n.smooth,l,n.connectNulls);Qi(t,i,o+u-1,u,s,-1,n.stackedOnSmooth,l,n.connectNulls),o+=u+1,t.closePath()}},e}(Ie);function Uv(r,e,t,n,a){var i=r.getArea(),o=i.x,s=i.y,l=i.width,u=i.height,f=t.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,l+=f,u+=f,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var c=new fe({shape:{x:o,y:s,width:l,height:u}});if(e){var h=r.getBaseAxis(),v=h.isHorizontal(),d=h.inverse;v?(d&&(c.shape.x+=l),c.shape.width=0):(d||(c.shape.y+=u),c.shape.height=0);var p=H(a)?function(g){a(g,c)}:null;Oe(c,{shape:{width:l,height:u,x:o,y:s}},t,null,n,p)}return c}function Yv(r,e,t){var n=r.getArea(),a=ae(n.r0,1),i=ae(n.r,1),o=new lr({shape:{cx:ae(r.cx,1),cy:ae(r.cy,1),r0:a,r:i,startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}});if(e){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=n.startAngle:o.shape.r=a,Oe(o,{shape:{endAngle:n.endAngle,r:i}},t)}return o}function d0(r,e,t,n,a){if(r){if(r.type==="polar")return Yv(r,e,t);if(r.type==="cartesian2d")return Uv(r,e,t,n,a)}else return null;return null}function ls(r,e){return r.type===e}function tu(r,e){if(r.length===e.length){for(var t=0;t<r.length;t++)if(r[t]!==e[t])return;return!0}}function ru(r){for(var e=1/0,t=1/0,n=-1/0,a=-1/0,i=0;i<r.length;){var o=r[i++],s=r[i++];isNaN(o)||(e=Math.min(o,e),n=Math.max(o,n)),isNaN(s)||(t=Math.min(s,t),a=Math.max(s,a))}return[[e,t],[n,a]]}function nu(r,e){var t=ru(r),n=t[0],a=t[1],i=ru(e),o=i[0],s=i[1];return Math.max(Math.abs(n[0]-o[0]),Math.abs(n[1]-o[1]),Math.abs(a[0]-s[0]),Math.abs(a[1]-s[1]))}function au(r){return oe(r)?r:r?.5:0}function p0(r,e,t){if(!t.valueDim)return[];for(var n=e.count(),a=it(n*2),i=0;i<n;i++){var o=Hv(t,r,e,i);a[i*2]=o[0],a[i*2+1]=o[1]}return a}function yt(r,e,t,n,a){var i=t.getBaseAxis(),o=i.dim==="x"||i.dim==="radius"?0:1,s=[],l=0,u=[],f=[],c=[],h=[];if(a){for(l=0;l<r.length;l+=2){var v=e||r;!isNaN(v[l])&&!isNaN(v[l+1])&&h.push(r[l],r[l+1])}r=h}for(l=0;l<r.length-2;l+=2)switch(c[0]=r[l+2],c[1]=r[l+3],f[0]=r[l],f[1]=r[l+1],s.push(f[0],f[1]),n){case"end":u[o]=c[o],u[1-o]=f[1-o],s.push(u[0],u[1]);break;case"middle":var d=(f[o]+c[o])/2,p=[];u[o]=p[o]=d,u[1-o]=f[1-o],p[1-o]=c[1-o],s.push(u[0],u[1]),s.push(p[0],p[1]);break;default:u[o]=f[o],u[1-o]=c[1-o],s.push(u[0],u[1])}return s.push(r[l++],r[l++]),s}function g0(r,e){var t=[],n=r.length,a,i;function o(f,c,h){var v=f.coord,d=(h-v)/(c.coord-v),p=Uh(d,[f.color,c.color]);return{coord:h,color:p}}for(var s=0;s<n;s++){var l=r[s],u=l.coord;if(u<0)a=l;else if(u>e){i?t.push(o(i,l,e)):a&&t.push(o(a,l,0),o(a,l,e));break}else a&&(t.push(o(a,l,0)),a=null),t.push(l),i=l}return t}function m0(r,e,t){var n=r.getVisual("visualMeta");if(!(!n||!n.length||!r.count())&&e.type==="cartesian2d"){for(var a,i,o=n.length-1;o>=0;o--){var s=r.getDimensionInfo(n[o].dimension);if(a=s&&s.coordDim,a==="x"||a==="y"){i=n[o];break}}if(i){var l=e.getAxis(a),u=F(i.stops,function(_){return{coord:l.toGlobalCoord(l.dataToCoord(_.value)),color:_.color}}),f=u.length,c=i.outerColors.slice();f&&u[0].coord>u[f-1].coord&&(u.reverse(),c.reverse());var h=g0(u,a==="x"?t.getWidth():t.getHeight()),v=h.length;if(!v&&f)return u[0].coord<0?c[1]?c[1]:u[f-1].color:c[0]?c[0]:u[0].color;var d=10,p=h[0].coord-d,g=h[v-1].coord+d,m=g-p;if(m<.001)return"transparent";b(h,function(_){_.offset=(_.coord-p)/m}),h.push({offset:v?h[v-1].offset:.5,color:c[1]||"transparent"}),h.unshift({offset:v?h[0].offset:.5,color:c[0]||"transparent"});var y=new go(0,0,0,0,h,!0);return y[a]=p,y[a+"2"]=g,y}}}function y0(r,e,t){var n=r.get("showAllSymbol"),a=n==="auto";if(!(n&&!a)){var i=t.getAxesByScale("ordinal")[0];if(i&&!(a&&_0(i,e))){var o=e.mapDimension(i.dim),s={};return b(i.getViewLabels(),function(l){var u=i.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(e.get(o,l))}}}}function _0(r,e){var t=r.getExtent(),n=Math.abs(t[1]-t[0])/r.scale.count();isNaN(n)&&(n=0);for(var a=e.count(),i=Math.max(1,Math.round(a/5)),o=0;o<a;o+=i)if(ss.getSymbolSize(e,o)[r.isHorizontal()?1:0]*1.5>n)return!1;return!0}function S0(r,e){return isNaN(r)||isNaN(e)}function x0(r){for(var e=r.length/2;e>0&&S0(r[e*2-2],r[e*2-1]);e--);return e-1}function iu(r,e){return[r[e*2],r[e*2+1]]}function b0(r,e,t){for(var n=r.length/2,a=t==="x"?0:1,i,o,s=0,l=-1,u=0;u<n;u++)if(o=r[u*2+a],!(isNaN(o)||isNaN(r[u*2+1-a]))){if(u===0){i=o;continue}if(i<=e&&o>=e||i>=e&&o<=e){l=u;break}s=u,i=o}return{range:[s,l],t:(e-i)/(o-i)}}function Xv(r){if(r.get(["endLabel","show"]))return!0;for(var e=0;e<Ue.length;e++)if(r.get([Ue[e],"endLabel","show"]))return!0;return!1}function ci(r,e,t,n){if(ls(e,"cartesian2d")){var a=n.getModel("endLabel"),i=a.get("valueAnimation"),o=n.getData(),s={lastFrameIndex:0},l=Xv(n)?function(v,d){r._endLabelOnDuring(v,d,o,s,i,a,e)}:null,u=e.getBaseAxis().isHorizontal(),f=Uv(e,t,n,function(){var v=r._endLabel;v&&t&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},l);if(!n.get("clip",!0)){var c=f.shape,h=Math.max(c.width,c.height);u?(c.y-=h,c.height+=h*2):(c.x-=h,c.width+=h*2)}return l&&l(1,f),f}else return Yv(e,t,n)}function w0(r,e){var t=e.getBaseAxis(),n=t.isHorizontal(),a=t.inverse,i=n?a?"right":"left":"center",o=n?"middle":a?"top":"bottom";return{normal:{align:r.get("align")||i,verticalAlign:r.get("verticalAlign")||o}}}var T0=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.init=function(){var t=new me,n=new s0;this.group.add(n.group),this._symbolDraw=n,this._lineGroup=t,this._changePolyState=Q(this._changePolyState,this)},e.prototype.render=function(t,n,a){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=t.getModel("lineStyle"),u=t.getModel("areaStyle"),f=s.getLayout("points")||[],c=i.type==="polar",h=this._coordSys,v=this._symbolDraw,d=this._polyline,p=this._polygon,g=this._lineGroup,m=!n.ssr&&t.get("animation"),y=!u.isEmpty(),_=u.get("origin"),S=zv(i,s,_),w=y&&p0(i,s,S),x=t.get("showSymbol"),T=t.get("connectNulls"),A=x&&!c&&y0(t,s,i),M=this._data;M&&M.eachItemGraphicEl(function(ye,hr){ye.__temp&&(o.remove(ye),M.setItemGraphicEl(hr,null))}),x||v.remove(),o.add(g);var D=c?!1:t.get("step"),C;i&&i.getArea&&t.get("clip",!0)&&(C=i.getArea(),C.width!=null?(C.x-=.1,C.y-=.1,C.width+=.2,C.height+=.2):C.r0&&(C.r0-=.5,C.r+=.5)),this._clipShapeForSymbol=C;var L=m0(s,i,a)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&h.type===i.type&&D===this._step))x&&v.updateData(s,{isIgnore:A,clipShape:C,disableAnimation:!0,getSymbolPoint:function(ye){return[f[ye*2],f[ye*2+1]]}}),m&&this._initSymbolLabelAnimation(s,i,C),D&&(w&&(w=yt(w,f,i,D,T)),f=yt(f,null,i,D,T)),d=this._newPolyline(f),y?p=this._newPolygon(f,w):p&&(g.remove(p),p=this._polygon=null),c||this._initOrUpdateEndLabel(t,i,Ht(L)),g.setClipPath(ci(this,i,!0,t));else{y&&!p?p=this._newPolygon(f,w):p&&!y&&(g.remove(p),p=this._polygon=null),c||this._initOrUpdateEndLabel(t,i,Ht(L));var I=g.getClipPath();if(I){var P=ci(this,i,!1,t);Oe(I,{shape:P.shape},t)}else g.setClipPath(ci(this,i,!0,t));x&&v.updateData(s,{isIgnore:A,clipShape:C,disableAnimation:!0,getSymbolPoint:function(ye){return[f[ye*2],f[ye*2+1]]}}),(!tu(this._stackedOnPoints,w)||!tu(this._points,f))&&(m?this._doUpdateAnimation(s,w,i,a,D,_,T):(D&&(w&&(w=yt(w,f,i,D,T)),f=yt(f,null,i,D,T)),d.setShape({points:f}),p&&p.setShape({points:f,stackedOnPoints:w})))}var k=t.getModel("emphasis"),G=k.get("focus"),Z=k.get("blurScope"),E=k.get("disabled");if(d.useStyle(ee(l.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),Ii(d,t,"lineStyle"),d.style.lineWidth>0&&t.get(["emphasis","lineStyle","width"])==="bolder"){var O=d.getState("emphasis").style;O.lineWidth=+d.style.lineWidth+1}Y(d).seriesIndex=t.seriesIndex,zn(d,G,Z,E);var U=au(t.get("smooth")),q=t.get("smoothMonotone");if(d.setShape({smooth:U,smoothMonotone:q,connectNulls:T}),p){var Te=s.getCalculationInfo("stackedOnSeries"),ce=0;p.useStyle(ee(u.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),Te&&(ce=au(Te.get("smooth"))),p.setShape({smooth:U,stackedOnSmooth:ce,smoothMonotone:q,connectNulls:T}),Ii(p,t,"areaStyle"),Y(p).seriesIndex=t.seriesIndex,zn(p,G,Z,E)}var Le=this._changePolyState;s.eachItemGraphicEl(function(ye){ye&&(ye.onHoverStateChange=Le)}),this._polyline.onHoverStateChange=Le,this._data=s,this._coordSys=i,this._stackedOnPoints=w,this._points=f,this._step=D,this._valueOrigin=_,t.get("triggerLineEvent")&&(this.packEventData(t,d),p&&this.packEventData(t,p))},e.prototype.packEventData=function(t,n){Y(n).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,n,a,i){var o=t.getData(),s=Vt(o,i);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var f=l[s*2],c=l[s*2+1];if(isNaN(f)||isNaN(c)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,c))return;var h=t.get("zlevel")||0,v=t.get("z")||0;u=new ss(o,s),u.x=f,u.y=c,u.setZ(h,v);var d=u.getSymbolPath().getTextContent();d&&(d.zlevel=h,d.z=v,d.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else Me.prototype.highlight.call(this,t,n,a,i)},e.prototype.downplay=function(t,n,a,i){var o=t.getData(),s=Vt(o,i);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else Me.prototype.downplay.call(this,t,n,a,i)},e.prototype._changePolyState=function(t){var n=this._polygon;As(this._polyline,t),n&&As(n,t)},e.prototype._newPolyline=function(t){var n=this._polyline;return n&&this._lineGroup.remove(n),n=new c0({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(n),this._polyline=n,n},e.prototype._newPolygon=function(t,n){var a=this._polygon;return a&&this._lineGroup.remove(a),a=new h0({shape:{points:t,stackedOnPoints:n},segmentIgnoreThreshold:2}),this._lineGroup.add(a),this._polygon=a,a},e.prototype._initSymbolLabelAnimation=function(t,n,a){var i,o,s=n.getBaseAxis(),l=s.inverse;n.type==="cartesian2d"?(i=s.isHorizontal(),o=!1):n.type==="polar"&&(i=s.dim==="angle",o=!0);var u=t.hostModel,f=u.get("animationDuration");H(f)&&(f=f(null));var c=u.get("animationDelay")||0,h=H(c)?c(null):c;t.eachItemGraphicEl(function(v,d){var p=v;if(p){var g=[v.x,v.y],m=void 0,y=void 0,_=void 0;if(a)if(o){var S=a,w=n.pointToCoord(g);i?(m=S.startAngle,y=S.endAngle,_=-w[1]/180*Math.PI):(m=S.r0,y=S.r,_=w[0])}else{var x=a;i?(m=x.x,y=x.x+x.width,_=v.x):(m=x.y+x.height,y=x.y,_=v.y)}var T=y===m?0:(_-m)/(y-m);l&&(T=1-T);var A=H(c)?c(d):f*T+h,M=p.getSymbolPath(),D=M.getTextContent();p.attr({scaleX:0,scaleY:0}),p.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:A}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:A}),M.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,n,a){var i=t.getModel("endLabel");if(Xv(t)){var o=t.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new we({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=x0(l);f>=0&&(da(s,pa(t,"endLabel"),{inheritColor:a,labelFetcher:t,labelDataIndex:f,defaultText:function(c,h,v){return v!=null?Vv(o,v):os(o,c)},enableTextSetter:!0},w0(i,n)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,n,a,i,o,s,l){var u=this._endLabel,f=this._polyline;if(u){t<1&&i.originalX==null&&(i.originalX=u.x,i.originalY=u.y);var c=a.getLayout("points"),h=a.hostModel,v=h.get("connectNulls"),d=s.get("precision"),p=s.get("distance")||0,g=l.getBaseAxis(),m=g.isHorizontal(),y=g.inverse,_=n.shape,S=y?m?_.x:_.y+_.height:m?_.x+_.width:_.y,w=(m?p:0)*(y?-1:1),x=(m?0:-p)*(y?-1:1),T=m?"x":"y",A=b0(c,S,T),M=A.range,D=M[1]-M[0],C=void 0;if(D>=1){if(D>1&&!v){var L=iu(c,M[0]);u.attr({x:L[0]+w,y:L[1]+x}),o&&(C=h.getRawValue(M[0]))}else{var L=f.getPointOn(S,T);L&&u.attr({x:L[0]+w,y:L[1]+x});var I=h.getRawValue(M[0]),P=h.getRawValue(M[1]);o&&(C=gf(a,d,I,P,A.t))}i.lastFrameIndex=M[0]}else{var k=t===1||i.lastFrameIndex>0?M[0]:0,L=iu(c,k);o&&(C=h.getRawValue(k)),u.attr({x:L[0]+w,y:L[1]+x})}if(o){var G=ur(u);typeof G.setLabelText=="function"&&G.setLabelText(C)}}},e.prototype._doUpdateAnimation=function(t,n,a,i,o,s,l){var u=this._polyline,f=this._polygon,c=t.hostModel,h=f0(this._data,t,this._stackedOnPoints,n,this._coordSys,a,this._valueOrigin),v=h.current,d=h.stackedOnCurrent,p=h.next,g=h.stackedOnNext;if(o&&(d=yt(h.stackedOnCurrent,h.current,a,o,l),v=yt(h.current,null,a,o,l),g=yt(h.stackedOnNext,h.next,a,o,l),p=yt(h.next,null,a,o,l)),nu(v,p)>3e3||f&&nu(d,g)>3e3){u.stopAnimation(),u.setShape({points:p}),f&&(f.stopAnimation(),f.setShape({points:p,stackedOnPoints:g}));return}u.shape.__points=h.current,u.shape.points=v;var m={shape:{points:p}};h.current!==v&&(m.shape.__points=h.next),u.stopAnimation(),he(u,m,c),f&&(f.setShape({points:v,stackedOnPoints:d}),f.stopAnimation(),he(f,{shape:{stackedOnPoints:g}},c),u.shape.points!==f.shape.points&&(f.shape.points=u.shape.points));for(var y=[],_=h.status,S=0;S<_.length;S++){var w=_[S].cmd;if(w==="="){var x=t.getItemGraphicEl(_[S].idx1);x&&y.push({el:x,ptIdx:S})}}u.animators&&u.animators.length&&u.animators[0].during(function(){f&&f.dirtyShape();for(var T=u.shape.__points,A=0;A<y.length;A++){var M=y[A].el,D=y[A].ptIdx*2;M.x=T[D],M.y=T[D+1],M.markRedraw()}})},e.prototype.remove=function(t){var n=this.group,a=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),a&&a.eachItemGraphicEl(function(i,o){i.__temp&&(n.remove(i),a.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Me);function C0(r,e){return{seriesType:r,plan:Yo(),reset:function(t){var n=t.getData(),a=t.coordinateSystem;if(t.pipelineContext,!!a){var i=F(a.dimensions,function(c){return n.mapDimension(c)}).slice(0,2),o=i.length,s=n.getCalculationInfo("stackResultDimension");Ut(n,i[0])&&(i[0]=s),Ut(n,i[1])&&(i[1]=s);var l=n.getStore(),u=n.getDimensionIndex(i[0]),f=n.getDimensionIndex(i[1]);return o&&{progress:function(c,h){for(var v=c.end-c.start,d=it(v*o),p=[],g=[],m=c.start,y=0;m<c.end;m++){var _=void 0;if(o===1){var S=l.get(u,m);_=a.dataToPoint(S,null,g)}else p[0]=l.get(u,m),p[1]=l.get(f,m),_=a.dataToPoint(p,null,g);d[y++]=_[0],d[y++]=_[1]}h.setLayout("points",d)}}}}}}var D0={average:function(r){for(var e=0,t=0,n=0;n<r.length;n++)isNaN(r[n])||(e+=r[n],t++);return t===0?NaN:e/t},sum:function(r){for(var e=0,t=0;t<r.length;t++)e+=r[t]||0;return e},max:function(r){for(var e=-1/0,t=0;t<r.length;t++)r[t]>e&&(e=r[t]);return isFinite(e)?e:NaN},min:function(r){for(var e=1/0,t=0;t<r.length;t++)r[t]<e&&(e=r[t]);return isFinite(e)?e:NaN},nearest:function(r){return r[0]}},M0=function(r){return Math.round(r.length/2)};function Zv(r){return{seriesType:r,reset:function(e,t,n){var a=e.getData(),i=e.get("sampling"),o=e.coordinateSystem,s=a.count();if(s>10&&o.type==="cartesian2d"&&i){var l=o.getBaseAxis(),u=o.getOtherAxis(l),f=l.getExtent(),c=n.getDevicePixelRatio(),h=Math.abs(f[1]-f[0])*(c||1),v=Math.round(s/h);if(isFinite(v)&&v>1){i==="lttb"?e.setData(a.lttbDownSample(a.mapDimension(u.dim),1/v)):i==="minmax"&&e.setData(a.minmaxDownSample(a.mapDimension(u.dim),1/v));var d=void 0;B(i)?d=D0[i]:H(i)&&(d=i),d&&e.setData(a.downSample(a.mapDimension(u.dim),1/v,d,M0))}}}}}function wx(r){r.registerChartView(T0),r.registerSeriesModel(i0),r.registerLayout(C0("line")),r.registerVisual({seriesType:"line",reset:function(e){var t=e.getData(),n=e.getModel("lineStyle").getLineStyle();n&&!n.stroke&&(n.stroke=t.getVisual("style").fill),t.setVisual("legendLineStyle",n)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,Zv("line"))}var Ji=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,n){return Aa(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,n,a){var i=this.coordinateSystem;if(i&&i.clampData){var o=i.clampData(t),s=i.dataToPoint(o);if(a)b(i.getAxes(),function(h,v){if(h.type==="category"&&n!=null){var d=h.getTicksCoords(),p=h.getTickModel().get("alignWithLabel"),g=o[v],m=n[v]==="x1"||n[v]==="y1";if(m&&!p&&(g+=1),d.length<2)return;if(d.length===2){s[v]=h.toGlobalCoord(h.getExtent()[m?1:0]);return}for(var y=void 0,_=void 0,S=1,w=0;w<d.length;w++){var x=d[w].coord,T=w===d.length-1?d[w-1].tickValue+S:d[w].tickValue;if(T===g){_=x;break}else if(T<g)y=x;else if(y!=null&&T>g){_=(x+y)/2;break}w===1&&(S=T-d[0].tickValue)}_==null&&(y?y&&(_=d[d.length-1].coord):_=d[0].coord),s[v]=h.toGlobalCoord(_)}});else{var l=this.getData(),u=l.getLayout("offset"),f=l.getLayout("size"),c=i.getBaseAxis().isHorizontal()?0:1;s[c]+=u+f/2}return s}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(Ne);Ne.registerClass(Ji);var A0=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(){return Aa(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),n=this.get("largeThreshold");return n>t&&(t=n),t},e.prototype.brushSelector=function(t,n,a){return a.rect(n.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=Wf(Ji.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(Ji),I0=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),ou=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;return n.type="sausage",n}return e.prototype.getDefaultShape=function(){return new I0},e.prototype.buildPath=function(t,n){var a=n.cx,i=n.cy,o=Math.max(n.r0||0,0),s=Math.max(n.r,0),l=(s-o)*.5,u=o+l,f=n.startAngle,c=n.endAngle,h=n.clockwise,v=Math.PI*2,d=h?c-f<v:f-c<v;d||(f=c-(h?v:-v));var p=Math.cos(f),g=Math.sin(f),m=Math.cos(c),y=Math.sin(c);d?(t.moveTo(p*o+a,g*o+i),t.arc(p*u+a,g*u+i,l,-Math.PI+f,f,!h)):t.moveTo(p*s+a,g*s+i),t.arc(a,i,s,f,c,!h),t.arc(m*u+a,y*u+i,l,c-Math.PI*2,c-Math.PI,!h),o!==0&&t.arc(a,i,o,c,f,h)},e}(Ie);function L0(r,e){e=e||{};var t=e.isRoundCap;return function(n,a,i){var o=a.position;if(!o||o instanceof Array)return wi(n,a,i);var s=r(o),l=a.distance!=null?a.distance:5,u=this.shape,f=u.cx,c=u.cy,h=u.r,v=u.r0,d=(h+v)/2,p=u.startAngle,g=u.endAngle,m=(p+g)/2,y=t?Math.abs(h-v)/2:0,_=Math.cos,S=Math.sin,w=f+h*_(p),x=c+h*S(p),T="left",A="top";switch(s){case"startArc":w=f+(v-l)*_(m),x=c+(v-l)*S(m),T="center",A="top";break;case"insideStartArc":w=f+(v+l)*_(m),x=c+(v+l)*S(m),T="center",A="bottom";break;case"startAngle":w=f+d*_(p)+Tn(p,l+y,!1),x=c+d*S(p)+Cn(p,l+y,!1),T="right",A="middle";break;case"insideStartAngle":w=f+d*_(p)+Tn(p,-l+y,!1),x=c+d*S(p)+Cn(p,-l+y,!1),T="left",A="middle";break;case"middle":w=f+d*_(m),x=c+d*S(m),T="center",A="middle";break;case"endArc":w=f+(h+l)*_(m),x=c+(h+l)*S(m),T="center",A="bottom";break;case"insideEndArc":w=f+(h-l)*_(m),x=c+(h-l)*S(m),T="center",A="top";break;case"endAngle":w=f+d*_(g)+Tn(g,l+y,!0),x=c+d*S(g)+Cn(g,l+y,!0),T="left",A="middle";break;case"insideEndAngle":w=f+d*_(g)+Tn(g,-l+y,!0),x=c+d*S(g)+Cn(g,-l+y,!0),T="right",A="middle";break;default:return wi(n,a,i)}return n=n||{},n.x=w,n.y=x,n.align=T,n.verticalAlign=A,n}}function P0(r,e,t,n){if(oe(n)){r.setTextConfig({rotation:n});return}else if(R(e)){r.setTextConfig({rotation:0});return}var a=r.shape,i=a.clockwise?a.startAngle:a.endAngle,o=a.clockwise?a.endAngle:a.startAngle,s=(i+o)/2,l,u=t(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":l=s;break;case"startAngle":case"insideStartAngle":l=i;break;case"endAngle":case"insideEndAngle":l=o;break;default:r.setTextConfig({rotation:0});return}var f=Math.PI*1.5-l;u==="middle"&&f>Math.PI/2&&f<Math.PI*1.5&&(f-=Math.PI),r.setTextConfig({rotation:f})}function Tn(r,e,t){return e*Math.sin(r)*(t?-1:1)}function Cn(r,e,t){return e*Math.cos(r)*(t?1:-1)}function k0(r,e,t){var n=r.get("borderRadius");if(n==null)return{cornerRadius:0};R(n)||(n=[n,n,n,n]);var a=Math.abs(e.r||0-e.r0||0);return{cornerRadius:F(n,function(i){return Ti(i,a)})}}var vi=Math.max,hi=Math.min;function E0(r,e){var t=r.getArea&&r.getArea();if(ls(r,"cartesian2d")){var n=r.getBaseAxis();if(n.type!=="category"||!n.onBand){var a=e.getLayout("bandWidth");n.isHorizontal()?(t.x-=a,t.width+=a*2):(t.y-=a,t.height+=a*2)}}return t}var R0=function(r){z(e,r);function e(){var t=r.call(this)||this;return t.type=e.type,t._isFirstFrame=!0,t}return e.prototype.render=function(t,n,a,i){this._model=t,this._removeOnRenderedListener(a),this._updateDrawMode(t);var o=t.get("coordinateSystem");(o==="cartesian2d"||o==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,n,a):this._renderNormal(t,n,a,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,n){this._progressiveEls=[],this._incrementalRenderLarge(t,n)},e.prototype.eachRendered=function(t){va(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var n=t.pipelineContext.large;(this._isLargeDraw==null||n!==this._isLargeDraw)&&(this._isLargeDraw=n,this._clear())},e.prototype._renderNormal=function(t,n,a,i){var o=this.group,s=t.getData(),l=this._data,u=t.coordinateSystem,f=u.getBaseAxis(),c;u.type==="cartesian2d"?c=f.isHorizontal():u.type==="polar"&&(c=f.dim==="angle");var h=t.isAnimationEnabled()?t:null,v=O0(t,u);v&&this._enableRealtimeSort(v,s,a);var d=t.get("clip",!0)||v,p=E0(u,s);o.removeClipPath();var g=t.get("roundCap",!0),m=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),_=y.get("borderRadius")||0,S=[],w=this._backgroundEls,x=i&&i.isInitSort,T=i&&i.type==="changeAxisOrder";function A(C){var L=Dn[u.type](s,C),I=H0(u,c,L);return I.useStyle(y.getItemStyle()),u.type==="cartesian2d"?I.setShape("r",_):I.setShape("cornerRadius",_),S[C]=I,I}s.diff(l).add(function(C){var L=s.getItemModel(C),I=Dn[u.type](s,C,L);if(m&&A(C),!(!s.hasValue(C)||!cu[u.type](I))){var P=!1;d&&(P=su[u.type](p,I));var k=lu[u.type](t,s,C,I,c,h,f.model,!1,g);v&&(k.forceLabelAnimation=!0),vu(k,s,C,L,I,t,c,u.type==="polar"),x?k.attr({shape:I}):v?uu(v,h,k,I,C,c,!1,!1):Oe(k,{shape:I},t,C),s.setItemGraphicEl(C,k),o.add(k),k.ignore=P}}).update(function(C,L){var I=s.getItemModel(C),P=Dn[u.type](s,C,I);if(m){var k=void 0;w.length===0?k=A(L):(k=w[L],k.useStyle(y.getItemStyle()),u.type==="cartesian2d"?k.setShape("r",_):k.setShape("cornerRadius",_),S[C]=k);var G=Dn[u.type](s,C),Z=Kv(c,G,u);he(k,{shape:Z},h,C)}var E=l.getItemGraphicEl(L);if(!s.hasValue(C)||!cu[u.type](P)){o.remove(E);return}var O=!1;if(d&&(O=su[u.type](p,P),O&&o.remove(E)),E?If(E):E=lu[u.type](t,s,C,P,c,h,f.model,!!E,g),v&&(E.forceLabelAnimation=!0),T){var U=E.getTextContent();if(U){var q=ur(U);q.prevValue!=null&&(q.prevValue=q.value)}}else vu(E,s,C,I,P,t,c,u.type==="polar");x?E.attr({shape:P}):v?uu(v,h,E,P,C,c,!0,T):he(E,{shape:P},t,C,null),s.setItemGraphicEl(C,E),E.ignore=O,o.add(E)}).remove(function(C){var L=l.getItemGraphicEl(C);L&&ki(L,t,C)}).execute();var M=this._backgroundGroup||(this._backgroundGroup=new me);M.removeAll();for(var D=0;D<S.length;++D)M.add(S[D]);o.add(M),this._backgroundEls=S,this._data=s},e.prototype._renderLarge=function(t,n,a){this._clear(),du(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,n){this._removeBackground(),du(n,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var n=t.get("clip",!0)&&d0(t.coordinateSystem,!1,t),a=this.group;n?a.setClipPath(n):a.removeClipPath()},e.prototype._enableRealtimeSort=function(t,n,a){var i=this;if(n.count()){var o=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(n,t,a),this._isFirstFrame=!1;else{var s=function(l){var u=n.getItemGraphicEl(l),f=u&&u.shape;return f&&Math.abs(o.isHorizontal()?f.height:f.width)||0};this._onRendered=function(){i._updateSortWithinSameData(n,s,o,a)},a.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,n,a){var i=[];return t.each(t.mapDimension(n.dim),function(o,s){var l=a(s);l=l??NaN,i.push({dataIndex:s,mappedValue:l,ordinalNumber:o})}),i.sort(function(o,s){return s.mappedValue-o.mappedValue}),{ordinalNumbers:F(i,function(o){return o.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(t,n,a){for(var i=a.scale,o=t.mapDimension(a.dim),s=Number.MAX_VALUE,l=0,u=i.getOrdinalMeta().categories.length;l<u;++l){var f=t.rawIndexOf(o,i.getRawOrdinalNumber(l)),c=f<0?Number.MIN_VALUE:n(t.indexOfRawIndex(f));if(c>s)return!0;s=c}return!1},e.prototype._isOrderDifferentInView=function(t,n){for(var a=n.scale,i=a.getExtent(),o=Math.max(0,i[0]),s=Math.min(i[1],a.getOrdinalMeta().categories.length-1);o<=s;++o)if(t.ordinalNumbers[o]!==a.getRawOrdinalNumber(o))return!0},e.prototype._updateSortWithinSameData=function(t,n,a,i){if(this._isOrderChangedWithinSameData(t,n,a)){var o=this._dataSort(t,a,n);this._isOrderDifferentInView(o,a)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:a.dim+"Axis",axisId:a.index,sortInfo:o}))}},e.prototype._dispatchInitSort=function(t,n,a){var i=n.baseAxis,o=this._dataSort(t,i,function(s){return t.get(t.mapDimension(n.otherAxis.dim),s)});a.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:o})},e.prototype.remove=function(t,n){this._clear(this._model),this._removeOnRenderedListener(n)},e.prototype.dispose=function(t,n){this._removeOnRenderedListener(n)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var n=this.group,a=this._data;t&&t.isAnimationEnabled()&&a&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],a.eachItemGraphicEl(function(i){ki(i,t,Y(i).dataIndex)})):n.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Me),su={cartesian2d:function(r,e){var t=e.width<0?-1:1,n=e.height<0?-1:1;t<0&&(e.x+=e.width,e.width=-e.width),n<0&&(e.y+=e.height,e.height=-e.height);var a=r.x+r.width,i=r.y+r.height,o=vi(e.x,r.x),s=hi(e.x+e.width,a),l=vi(e.y,r.y),u=hi(e.y+e.height,i),f=s<o,c=u<l;return e.x=f&&o>a?s:o,e.y=c&&l>i?u:l,e.width=f?0:s-o,e.height=c?0:u-l,t<0&&(e.x+=e.width,e.width=-e.width),n<0&&(e.y+=e.height,e.height=-e.height),f||c},polar:function(r,e){var t=e.r0<=e.r?1:-1;if(t<0){var n=e.r;e.r=e.r0,e.r0=n}var a=hi(e.r,r.r),i=vi(e.r0,r.r0);e.r=a,e.r0=i;var o=a-i<0;if(t<0){var n=e.r;e.r=e.r0,e.r0=n}return o}},lu={cartesian2d:function(r,e,t,n,a,i,o,s,l){var u=new fe({shape:N({},n),z2:1});if(u.__dataIndex=t,u.name="item",i){var f=u.shape,c=a?"height":"width";f[c]=0}return u},polar:function(r,e,t,n,a,i,o,s,l){var u=!a&&l?ou:lr,f=new u({shape:n,z2:1});f.name="item";var c=$v(a);if(f.calculateTextPosition=L0(c,{isRoundCap:u===ou}),i){var h=f.shape,v=a?"r":"endAngle",d={};h[v]=a?n.r0:n.startAngle,d[v]=n[v],(s?he:Oe)(f,{shape:d},i)}return f}};function O0(r,e){var t=r.get("realtimeSort",!0),n=e.getBaseAxis();if(t&&n.type==="category"&&e.type==="cartesian2d")return{baseAxis:n,otherAxis:e.getOtherAxis(n)}}function uu(r,e,t,n,a,i,o,s){var l,u;i?(u={x:n.x,width:n.width},l={y:n.y,height:n.height}):(u={y:n.y,height:n.height},l={x:n.x,width:n.width}),s||(o?he:Oe)(t,{shape:l},e,a,null);var f=e?r.baseAxis.model:null;(o?he:Oe)(t,{shape:u},f,a)}function fu(r,e){for(var t=0;t<e.length;t++)if(!isFinite(r[e[t]]))return!0;return!1}var N0=["x","y","width","height"],B0=["cx","cy","r","startAngle","endAngle"],cu={cartesian2d:function(r){return!fu(r,N0)},polar:function(r){return!fu(r,B0)}},Dn={cartesian2d:function(r,e,t){var n=r.getItemLayout(e),a=t?G0(t,n):0,i=n.width>0?1:-1,o=n.height>0?1:-1;return{x:n.x+i*a/2,y:n.y+o*a/2,width:n.width-i*a,height:n.height-o*a}},polar:function(r,e,t){var n=r.getItemLayout(e);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle,clockwise:n.clockwise}}};function F0(r){return r.startAngle!=null&&r.endAngle!=null&&r.startAngle===r.endAngle}function $v(r){return function(e){var t=e?"Arc":"Angle";return function(n){switch(n){case"start":case"insideStart":case"end":case"insideEnd":return n+t;default:return n}}}(r)}function vu(r,e,t,n,a,i,o,s){var l=e.getItemVisual(t,"style");if(s){if(!i.get("roundCap")){var f=r.shape,c=k0(n.getModel("itemStyle"),f);N(f,c),r.setShape(f)}}else{var u=n.get(["itemStyle","borderRadius"])||0;r.setShape("r",u)}r.useStyle(l);var h=n.getShallow("cursor");h&&r.attr("cursor",h);var v=s?o?a.r>=a.r0?"endArc":"startArc":a.endAngle>=a.startAngle?"endAngle":"startAngle":o?a.height>=0?"bottom":"top":a.width>=0?"right":"left",d=pa(n);da(r,d,{labelFetcher:i,labelDataIndex:t,defaultText:os(i.getData(),t),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:v});var p=r.getTextContent();if(s&&p){var g=n.get(["label","position"]);r.textConfig.inside=g==="middle"?!0:null,P0(r,g==="outside"?v:g,$v(o),n.get(["label","rotate"]))}dp(p,d,i.getRawValue(t),function(y){return Vv(e,y)});var m=n.getModel(["emphasis"]);zn(r,m.get("focus"),m.get("blurScope"),m.get("disabled")),Ii(r,n),F0(a)&&(r.style.fill="none",r.style.stroke="none",b(r.states,function(y){y.style&&(y.style.fill=y.style.stroke="none")}))}function G0(r,e){var t=r.get(["itemStyle","borderColor"]);if(!t||t==="none")return 0;var n=r.get(["itemStyle","borderWidth"])||0,a=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),i=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(n,a,i)}var V0=function(){function r(){}return r}(),hu=function(r){z(e,r);function e(t){var n=r.call(this,t)||this;return n.type="largeBar",n}return e.prototype.getDefaultShape=function(){return new V0},e.prototype.buildPath=function(t,n){for(var a=n.points,i=this.baseDimIdx,o=1-this.baseDimIdx,s=[],l=[],u=this.barWidth,f=0;f<a.length;f+=3)l[i]=u,l[o]=a[f+2],s[i]=a[f+i],s[o]=a[f+o],t.rect(s[0],s[1],l[0],l[1])},e}(Ie);function du(r,e,t,n){var a=r.getData(),i=a.getLayout("valueAxisHorizontal")?1:0,o=a.getLayout("largeDataIndices"),s=a.getLayout("size"),l=r.getModel("backgroundStyle"),u=a.getLayout("largeBackgroundPoints");if(u){var f=new hu({shape:{points:u},incremental:!!n,silent:!0,z2:0});f.baseDimIdx=i,f.largeDataIndices=o,f.barWidth=s,f.useStyle(l.getItemStyle()),e.add(f),t&&t.push(f)}var c=new hu({shape:{points:a.getLayout("largePoints")},incremental:!!n,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=i,c.largeDataIndices=o,c.barWidth=s,e.add(c),c.useStyle(a.getVisual("style")),c.style.stroke=null,Y(c).seriesIndex=r.seriesIndex,r.get("silent")||(c.on("mousedown",pu),c.on("mousemove",pu)),t&&t.push(c)}var pu=Ca(function(r){var e=this,t=z0(e,r.offsetX,r.offsetY);Y(e).dataIndex=t>=0?t:null},30,!1);function z0(r,e,t){for(var n=r.baseDimIdx,a=1-n,i=r.shape.points,o=r.largeDataIndices,s=[],l=[],u=r.barWidth,f=0,c=i.length/3;f<c;f++){var h=f*3;if(l[n]=u,l[a]=i[h+2],s[n]=i[h+n],s[a]=i[h+a],l[a]<0&&(s[a]+=l[a],l[a]=-l[a]),e>=s[0]&&e<=s[0]+l[0]&&t>=s[1]&&t<=s[1]+l[1])return o[f]}return-1}function Kv(r,e,t){if(ls(t,"cartesian2d")){var n=e,a=t.getArea();return{x:r?n.x:a.x,y:r?a.y:n.y,width:r?n.width:a.width,height:r?a.height:n.height}}else{var a=t.getArea(),i=e;return{cx:a.cx,cy:a.cy,r0:r?a.r0:i.r0,r:r?a.r:i.r,startAngle:r?i.startAngle:0,endAngle:r?i.endAngle:Math.PI*2}}}function H0(r,e,t){var n=r.type==="polar"?lr:fe;return new n({shape:Kv(e,t,r),silent:!0,z2:0})}function Tx(r){r.registerChartView(R0),r.registerSeriesModel(A0),r.registerLayout(r.PRIORITY.VISUAL.LAYOUT,le(Xy,"bar")),r.registerLayout(r.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Zy("bar")),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,Zv("bar")),r.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var n=e.componentType||"series";t.eachComponent({mainType:n,query:e},function(a){e.sortInfo&&a.axis.setCategorySortInfo(e.sortInfo)})})}var W0=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(X),eo=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Qe).models[0]},e.type="cartesian2dAxis",e}(X);ft(eo,Cv);var qv={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},U0=j({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},qv),us=j({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},qv),Y0=j({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},us),X0=ee({logBase:10},us);const Z0={category:U0,value:us,time:Y0,log:X0};var $0={value:1,category:1,time:1,log:1};function gu(r,e,t,n){b($0,function(a,i){var o=j(j({},Z0[i],!0),n,!0),s=function(l){z(u,l);function u(){var f=l!==null&&l.apply(this,arguments)||this;return f.type=e+"Axis."+i,f}return u.prototype.mergeDefaultAndTheme=function(f,c){var h=Kr(this),v=h?xa(f):{},d=c.getTheme();j(f,d.get(i+"Axis")),j(f,this.getDefaultOption()),f.type=mu(f),h&&ir(f,v,h)},u.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=$i.createByAxisModel(this))},u.prototype.getCategories=function(f){var c=this.option;if(c.type==="category")return f?c.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=e+"Axis."+i,u.defaultOption=o,u}(t);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(e+"Axis",mu)}function mu(r){return r.type||(r.data?"category":"value")}var K0=function(){function r(e){this.type="cartesian",this._dimList=[],this._axes={},this.name=e||""}return r.prototype.getAxis=function(e){return this._axes[e]},r.prototype.getAxes=function(){return F(this._dimList,function(e){return this._axes[e]},this)},r.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),se(this.getAxes(),function(t){return t.scale.type===e})},r.prototype.addAxis=function(e){var t=e.dim;this._axes[t]=e,this._dimList.push(t)},r}(),to=["x","y"];function yu(r){return r.type==="interval"||r.type==="time"}var q0=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=to,t}return e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,n=this.getAxis("y").scale;if(!(!yu(t)||!yu(n))){var a=t.getExtent(),i=n.getExtent(),o=this.dataToPoint([a[0],i[0]]),s=this.dataToPoint([a[1],i[1]]),l=a[1]-a[0],u=i[1]-i[0];if(!(!l||!u)){var f=(s[0]-o[0])/l,c=(s[1]-o[1])/u,h=o[0]-a[0]*f,v=o[1]-i[0]*c,d=this._transform=[f,0,0,c,h,v];this._invTransform=Jn([],d)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var n=this.getAxis("x"),a=this.getAxis("y");return n.contain(n.toLocalCoord(t[0]))&&a.contain(a.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,n){var a=this.dataToPoint(t),i=this.dataToPoint(n),o=this.getArea(),s=new Ee(a[0],a[1],i[0]-a[0],i[1]-a[1]);return o.intersect(s)},e.prototype.dataToPoint=function(t,n,a){a=a||[];var i=t[0],o=t[1];if(this._transform&&i!=null&&isFinite(i)&&o!=null&&isFinite(o))return ut(a,t,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return a[0]=s.toGlobalCoord(s.dataToCoord(i,n)),a[1]=l.toGlobalCoord(l.dataToCoord(o,n)),a},e.prototype.clampData=function(t,n){var a=this.getAxis("x").scale,i=this.getAxis("y").scale,o=a.getExtent(),s=i.getExtent(),l=a.parse(t[0]),u=i.parse(t[1]);return n=n||[],n[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),n[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),n},e.prototype.pointToData=function(t,n){var a=[];if(this._invTransform)return ut(a,t,this._invTransform);var i=this.getAxis("x"),o=this.getAxis("y");return a[0]=i.coordToData(i.toLocalCoord(t[0]),n),a[1]=o.coordToData(o.toLocalCoord(t[1]),n),a},e.prototype.getOtherAxis=function(t){return this.getAxis(t.dim==="x"?"y":"x")},e.prototype.getArea=function(t){t=t||0;var n=this.getAxis("x").getGlobalExtent(),a=this.getAxis("y").getGlobalExtent(),i=Math.min(n[0],n[1])-t,o=Math.min(a[0],a[1])-t,s=Math.max(n[0],n[1])-i+t,l=Math.max(a[0],a[1])-o+t;return new Ee(i,o,s,l)},e}(K0),j0=function(r){z(e,r);function e(t,n,a,i,o){var s=r.call(this,t,n,a)||this;return s.index=0,s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.getGlobalExtent=function(t){var n=this.getExtent();return n[0]=this.toGlobalCoord(n[0]),n[1]=this.toGlobalCoord(n[1]),t&&n[0]>n[1]&&n.reverse(),n},e.prototype.pointToData=function(t,n){return this.coordToData(this.toLocalCoord(t[this.dim==="x"?0:1]),n)},e.prototype.setCategorySortInfo=function(t){if(this.type!=="category")return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(Ov);function ro(r,e,t){t=t||{};var n=r.coordinateSystem,a=e.axis,i={},o=a.getAxesOnZeroOf()[0],s=a.position,l=o?"onZero":s,u=a.dim,f=n.getRect(),c=[f.x,f.x+f.width,f.y,f.y+f.height],h={left:0,right:1,top:0,bottom:1,onZero:2},v=e.get("offset")||0,d=u==="x"?[c[2]-v,c[3]+v]:[c[0]-v,c[1]+v];if(o){var p=o.toGlobalCoord(o.dataToCoord(0));d[h.onZero]=Math.max(Math.min(p,d[1]),d[0])}i.position=[u==="y"?d[h[l]]:c[0],u==="x"?d[h[l]]:c[3]],i.rotation=Math.PI/2*(u==="x"?0:1);var g={top:-1,bottom:1,left:-1,right:1};i.labelDirection=i.tickDirection=i.nameDirection=g[s],i.labelOffset=o?d[h[s]]-d[h.onZero]:0,e.get(["axisTick","inside"])&&(i.tickDirection=-i.tickDirection),zr(t.labelInside,e.get(["axisLabel","inside"]))&&(i.labelDirection=-i.labelDirection);var m=e.get(["axisLabel","rotate"]);return i.labelRotate=l==="top"?-m:m,i.z2=1,i}function _u(r){return r.get("coordinateSystem")==="cartesian2d"}function Su(r){var e={xAxisModel:null,yAxisModel:null};return b(e,function(t,n){var a=n.replace(/Model$/,""),i=r.getReferringComponents(a,Qe).models[0];e[n]=i}),e}var di=Math.log;function Q0(r,e,t){var n=cr.prototype,a=n.getTicks.call(t),i=n.getTicks.call(t,!0),o=a.length-1,s=n.getInterval.call(t),l=bv(r,e),u=l.extent,f=l.fixMin,c=l.fixMax;if(r.type==="log"){var h=di(r.base);u=[di(u[0])/h,di(u[1])/h]}r.setExtent(u[0],u[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:c});var v=n.getExtent.call(r);f&&(u[0]=v[0]),c&&(u[1]=v[1]);var d=n.getInterval.call(r),p=u[0],g=u[1];if(f&&c)d=(g-p)/o;else if(f)for(g=u[0]+d*o;g<u[1]&&isFinite(g)&&isFinite(u[1]);)d=ai(d),g=u[0]+d*o;else if(c)for(p=u[1]-d*o;p>u[0]&&isFinite(p)&&isFinite(u[0]);)d=ai(d),p=u[1]-d*o;else{var m=r.getTicks().length-1;m>o&&(d=ai(d));var y=d*o;g=Math.ceil(u[1]/d)*d,p=ae(g-y),p<0&&u[0]>=0?(p=0,g=ae(y)):g>0&&u[1]<=0&&(g=0,p=-ae(y))}var _=(a[0].value-i[0].value)/s,S=(a[o].value-i[o].value)/s;n.setExtent.call(r,p+d*_,g+d*S),n.setInterval.call(r,d),(_||S)&&n.setNiceExtent.call(r,p+d,g-d)}var J0=function(){function r(e,t,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=to,this._initCartesian(e,t,n),this.model=e}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(e,t){var n=this._axesMap;this._updateScale(e,this.model);function a(o){var s,l=Ye(o),u=l.length;if(u){for(var f=[],c=u-1;c>=0;c--){var h=+l[c],v=o[h],d=v.model,p=v.scale;Ki(p)&&d.get("alignTicks")&&d.get("interval")==null?f.push(v):(qi(p,d),Ki(p)&&(s=v))}f.length&&(s||(s=f.pop(),qi(s.scale,s.model)),b(f,function(g){Q0(g.scale,g.model,s.scale)}))}}a(n.x),a(n.y);var i={};b(n.x,function(o){xu(n,"y",o,i)}),b(n.y,function(o){xu(n,"x",o,i)}),this.resize(this.model,t)},r.prototype.resize=function(e,t,n){var a=e.getBoxLayoutParams(),i=!n&&e.get("containLabel"),o=$r(a,{width:t.getWidth(),height:t.getHeight()});this._rect=o;var s=this._axesList;l(),i&&(b(s,function(u){if(!u.model.get(["axisLabel","inside"])){var f=v_(u);if(f){var c=u.isHorizontal()?"height":"width",h=u.model.get(["axisLabel","margin"]);o[c]-=f[c]+h,u.position==="top"?o.y+=f.height+h:u.position==="left"&&(o.x+=f.width+h)}}}),l()),b(this._coordsList,function(u){u.calcAffineTransform()});function l(){b(s,function(u){var f=u.isHorizontal(),c=f?[0,o.width]:[0,o.height],h=u.inverse?1:0;u.setExtent(c[h],c[1-h]),eS(u,f?o.x:o.y)})}},r.prototype.getAxis=function(e,t){var n=this._axesMap[e];if(n!=null)return n[t||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(e,t){if(e!=null&&t!=null){var n="x"+e+"y"+t;return this._coordsMap[n]}V(e)&&(t=e.yAxisIndex,e=e.xAxisIndex);for(var a=0,i=this._coordsList;a<i.length;a++)if(i[a].getAxis("x").index===e||i[a].getAxis("y").index===t)return i[a]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(e,t,n){var a=this._findConvertTarget(t);return a.cartesian?a.cartesian.dataToPoint(n):a.axis?a.axis.toGlobalCoord(a.axis.dataToCoord(n)):null},r.prototype.convertFromPixel=function(e,t,n){var a=this._findConvertTarget(t);return a.cartesian?a.cartesian.pointToData(n):a.axis?a.axis.coordToData(a.axis.toLocalCoord(n)):null},r.prototype._findConvertTarget=function(e){var t=e.seriesModel,n=e.xAxisModel||t&&t.getReferringComponents("xAxis",Qe).models[0],a=e.yAxisModel||t&&t.getReferringComponents("yAxis",Qe).models[0],i=e.gridModel,o=this._coordsList,s,l;if(t)s=t.coordinateSystem,ie(o,s)<0&&(s=null);else if(n&&a)s=this.getCartesian(n.componentIndex,a.componentIndex);else if(n)l=this.getAxis("x",n.componentIndex);else if(a)l=this.getAxis("y",a.componentIndex);else if(i){var u=i.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},r.prototype.containPoint=function(e){var t=this._coordsList[0];if(t)return t.containPoint(e)},r.prototype._initCartesian=function(e,t,n){var a=this,i=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(t.eachComponent("xAxis",u("x"),this),t.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,b(s.x,function(f,c){b(s.y,function(h,v){var d="x"+c+"y"+v,p=new q0(d);p.master=a,p.model=e,a._coordsMap[d]=p,a._coordsList.push(p),p.addAxis(f),p.addAxis(h)})});function u(f){return function(c,h){if(pi(c,e)){var v=c.get("position");f==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var d=new j0(f,wv(c),[0,0],c.get("type"),v),p=d.type==="category";d.onBand=p&&c.get("boundaryGap"),d.inverse=c.get("inverse"),c.axis=d,d.model=c,d.grid=i,d.index=h,i._axesList.push(d),s[f][h]=d,l[f]++}}}},r.prototype._updateScale=function(e,t){b(this._axesList,function(a){if(a.scale.setExtent(1/0,-1/0),a.type==="category"){var i=a.model.get("categorySortInfo");a.scale.setSortInfo(i)}}),e.eachSeries(function(a){if(_u(a)){var i=Su(a),o=i.xAxisModel,s=i.yAxisModel;if(!pi(o,t)||!pi(s,t))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=a.getData(),f=l.getAxis("x"),c=l.getAxis("y");n(u,f),n(u,c)}},this);function n(a,i){b(d_(a,i.dim),function(o){i.scale.unionExtentFromData(a,o)})}},r.prototype.getTooltipAxes=function(e){var t=[],n=[];return b(this.getCartesians(),function(a){var i=e!=null&&e!=="auto"?a.getAxis(e):a.getBaseAxis(),o=a.getOtherAxis(i);ie(t,i)<0&&t.push(i),ie(n,o)<0&&n.push(o)}),{baseAxes:t,otherAxes:n}},r.create=function(e,t){var n=[];return e.eachComponent("grid",function(a,i){var o=new r(a,e,t);o.name="grid_"+i,o.resize(a,t,!0),a.coordinateSystem=o,n.push(o)}),e.eachSeries(function(a){if(_u(a)){var i=Su(a),o=i.xAxisModel,s=i.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;a.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),n},r.dimensions=to,r}();function pi(r,e){return r.getCoordSysModel()===e}function xu(r,e,t,n){t.getAxesOnZeroOf=function(){return i?[i]:[]};var a=r[e],i,o=t.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)bu(a[l])&&(i=a[l]);else for(var u in a)if(a.hasOwnProperty(u)&&bu(a[u])&&!n[f(a[u])]){i=a[u];break}i&&(n[f(i)]=!0);function f(c){return c.dim+"_"+c.index}}function bu(r){return r&&r.type!=="category"&&r.type!=="time"&&c_(r)}function eS(r,e){var t=r.getExtent(),n=t[0]+t[1];r.toGlobalCoord=r.dim==="x"?function(a){return a+e}:function(a){return n-a+e},r.toLocalCoord=r.dim==="x"?function(a){return a-e}:function(a){return n-a+e}}var St=Math.PI,bt=function(){function r(e,t){this.group=new me,this.opt=t,this.axisModel=e,ee(t,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new me({x:t.position[0],y:t.position[1],rotation:t.rotation});n.updateTransform(),this._transformGroup=n}return r.prototype.hasBuilder=function(e){return!!wu[e]},r.prototype.add=function(e){wu[e](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(e,t,n){var a=mo(t-e),i,o;return Hr(a)?(o=n>0?"top":"bottom",i="center"):Hr(a-St)?(o=n>0?"bottom":"top",i="center"):(o="middle",a>0&&a<St?i=n>0?"right":"left":i=n>0?"left":"right"),{rotation:a,textAlign:i,textVerticalAlign:o}},r.makeAxisEventDataBase=function(e){var t={componentType:e.mainType,componentIndex:e.componentIndex};return t[e.mainType+"Index"]=e.componentIndex,t},r.isLabelSilent=function(e){var t=e.get("tooltip");return e.get("silent")||!(e.get("triggerEvent")||t&&t.show)},r}(),wu={axisLine:function(r,e,t,n){var a=e.get(["axisLine","show"]);if(a==="auto"&&r.handleAutoShown&&(a=r.handleAutoShown("axisLine")),!!a){var i=e.axis.getExtent(),o=n.transform,s=[i[0],0],l=[i[1],0],u=s[0]>l[0];o&&(ut(s,s,o),ut(l,l,o));var f=N({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),c=new wt({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});Xr(c.shape,c.style.lineWidth),c.anid="line",t.add(c);var h=e.get(["axisLine","symbol"]);if(h!=null){var v=e.get(["axisLine","symbolSize"]);B(h)&&(h=[h,h]),(B(v)||oe(v))&&(v=[v,v]);var d=Rc(e.get(["axisLine","symbolOffset"])||0,v),p=v[0],g=v[1];b([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],function(m,y){if(h[y]!=="none"&&h[y]!=null){var _=Wt(h[y],-p/2,-g/2,p,g,f.stroke,!0),S=m.r+m.offset,w=u?l:s;_.attr({rotation:m.rotate,x:w[0]+S*Math.cos(r.rotation),y:w[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),t.add(_)}})}}},axisTickLabel:function(r,e,t,n){var a=nS(t,n,e,r),i=iS(t,n,e,r);if(rS(e,i,a),aS(t,n,e,r.tickDirection),e.get(["axisLabel","hideOverlap"])){var o=Bv(F(i,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));Gv(o)}},axisName:function(r,e,t,n){var a=zr(r.axisName,e.get("name"));if(a){var i=e.get("nameLocation"),o=r.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=e.axis.getExtent(),f=u[0]>u[1]?-1:1,c=[i==="start"?u[0]-f*l:i==="end"?u[1]+f*l:(u[0]+u[1])/2,Cu(i)?r.labelOffset+o*l:0],h,v=e.get("nameRotate");v!=null&&(v=v*St/180);var d;Cu(i)?h=bt.innerTextLayout(r.rotation,v??r.rotation,o):(h=tS(r.rotation,i,v||0,u),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(h.rotation)),!isFinite(d)&&(d=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},m=g.ellipsis,y=zr(r.nameTruncateMaxWidth,g.maxWidth,d),_=new we({x:c[0],y:c[1],rotation:h.rotation,silent:bt.isLabelSilent(e),style:zt(s,{text:a,font:p,overflow:"truncate",width:y,ellipsis:m,fill:s.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:s.get("align")||h.textAlign,verticalAlign:s.get("verticalAlign")||h.textVerticalAlign}),z2:1});if(ca({el:_,componentModel:e,itemName:a}),_.__fullText=a,_.anid="name",e.get("triggerEvent")){var S=bt.makeAxisEventDataBase(e);S.targetType="axisName",S.name=a,Y(_).eventData=S}n.add(_),_.updateTransform(),t.add(_),_.decomposeTransform()}}};function tS(r,e,t,n){var a=mo(t-r),i,o,s=n[0]>n[1],l=e==="start"&&!s||e!=="start"&&s;return Hr(a-St/2)?(o=l?"bottom":"top",i="center"):Hr(a-St*1.5)?(o=l?"top":"bottom",i="center"):(o="middle",a<St*1.5&&a>St/2?i=l?"left":"right":i=l?"right":"left"),{rotation:a,textAlign:i,textVerticalAlign:o}}function rS(r,e,t){if(!Tv(r.axis)){var n=r.get(["axisLabel","showMinLabel"]),a=r.get(["axisLabel","showMaxLabel"]);e=e||[],t=t||[];var i=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],u=t[0],f=t[1],c=t[t.length-1],h=t[t.length-2];n===!1?(Ge(i),Ge(u)):Tu(i,o)&&(n?(Ge(o),Ge(f)):(Ge(i),Ge(u))),a===!1?(Ge(s),Ge(c)):Tu(l,s)&&(a?(Ge(l),Ge(h)):(Ge(s),Ge(c)))}}function Ge(r){r&&(r.ignore=!0)}function Tu(r,e){var t=r&&r.getBoundingRect().clone(),n=e&&e.getBoundingRect().clone();if(!(!t||!n)){var a=fo([]);return af(a,a,-r.rotation),t.applyTransform(On([],a,r.getLocalTransform())),n.applyTransform(On([],a,e.getLocalTransform())),t.intersect(n)}}function Cu(r){return r==="middle"||r==="center"}function jv(r,e,t,n,a){for(var i=[],o=[],s=[],l=0;l<r.length;l++){var u=r[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=t,e&&(ut(o,o,e),ut(s,s,e));var f=new wt({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:n,z2:2,autoBatch:!0,silent:!0});Xr(f.shape,f.style.lineWidth),f.anid=a+"_"+r[l].tickValue,i.push(f)}return i}function nS(r,e,t,n){var a=t.axis,i=t.getModel("axisTick"),o=i.get("show");if(o==="auto"&&n.handleAutoShown&&(o=n.handleAutoShown("axisTick")),!(!o||a.scale.isBlank())){for(var s=i.getModel("lineStyle"),l=n.tickDirection*i.get("length"),u=a.getTicksCoords(),f=jv(u,e.transform,l,ee(s.getLineStyle(),{stroke:t.get(["axisLine","lineStyle","color"])}),"ticks"),c=0;c<f.length;c++)r.add(f[c]);return f}}function aS(r,e,t,n){var a=t.axis,i=t.getModel("minorTick");if(!(!i.get("show")||a.scale.isBlank())){var o=a.getMinorTicksCoords();if(o.length)for(var s=i.getModel("lineStyle"),l=n*i.get("length"),u=ee(s.getLineStyle(),ee(t.getModel("axisTick").getLineStyle(),{stroke:t.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var c=jv(o[f],e.transform,l,u,"minorticks_"+f),h=0;h<c.length;h++)r.add(c[h])}}function iS(r,e,t,n){var a=t.axis,i=zr(n.axisLabelShow,t.get(["axisLabel","show"]));if(!(!i||a.scale.isBlank())){var o=t.getModel("axisLabel"),s=o.get("margin"),l=a.getViewLabels(),u=(zr(n.labelRotate,o.get("rotate"))||0)*St/180,f=bt.innerTextLayout(n.rotation,u,n.labelDirection),c=t.getCategories&&t.getCategories(!0),h=[],v=bt.isLabelSilent(t),d=t.get("triggerEvent");return b(l,function(p,g){var m=a.scale.type==="ordinal"?a.scale.getRawOrdinalNumber(p.tickValue):p.tickValue,y=p.formattedLabel,_=p.rawLabel,S=o;if(c&&c[m]){var w=c[m];V(w)&&w.textStyle&&(S=new J(w.textStyle,o,t.ecModel))}var x=S.getTextColor()||t.get(["axisLine","lineStyle","color"]),T=a.dataToCoord(m),A=S.getShallow("align",!0)||f.textAlign,M=K(S.getShallow("alignMinLabel",!0),A),D=K(S.getShallow("alignMaxLabel",!0),A),C=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=K(S.getShallow("verticalAlignMinLabel",!0),C),I=K(S.getShallow("verticalAlignMaxLabel",!0),C),P=new we({x:T,y:n.labelOffset+n.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(p.level||0),style:zt(S,{text:y,align:g===0?M:g===l.length-1?D:A,verticalAlign:g===0?L:g===l.length-1?I:C,fill:H(x)?x(a.type==="category"?_:a.type==="value"?m+"":m,g):x})});if(P.anid="label_"+m,ca({el:P,componentModel:t,itemName:y,formatterParamsExtra:{isTruncated:function(){return P.isTruncated},value:_,tickIndex:g}}),d){var k=bt.makeAxisEventDataBase(t);k.targetType="axisLabel",k.value=_,k.tickIndex=g,a.type==="category"&&(k.dataIndex=m),Y(P).eventData=k}e.add(P),P.updateTransform(),h.push(P),r.add(P),P.decomposeTransform()}),h}}function oS(r,e){var t={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return sS(t,r,e),t.seriesInvolved&&uS(t,r),t}function sS(r,e,t){var n=e.getComponent("tooltip"),a=e.getComponent("axisPointer"),i=a.get("link",!0)||[],o=[];b(t.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var l=en(s.model),u=r.coordSysAxesInfo[l]={};r.coordSysMap[l]=s;var f=s.model,c=f.getModel("tooltip",n);if(b(s.getAxes(),le(p,!1,null)),s.getTooltipAxes&&n&&c.get("show")){var h=c.get("trigger")==="axis",v=c.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(c.get(["axisPointer","axis"]));(h||v)&&b(d.baseAxes,le(p,v?"cross":!0,h)),v&&b(d.otherAxes,le(p,"cross",!1))}function p(g,m,y){var _=y.model.getModel("axisPointer",a),S=_.get("show");if(!(!S||S==="auto"&&!g&&!no(_))){m==null&&(m=_.get("triggerTooltip")),_=g?lS(y,c,a,e,g,m):_;var w=_.get("snap"),x=_.get("triggerEmphasis"),T=en(y.model),A=m||w||y.type==="category",M=r.axesInfo[T]={key:T,axis:y,coordSys:s,axisPointerModel:_,triggerTooltip:m,triggerEmphasis:x,involveSeries:A,snap:w,useHandle:no(_),seriesModels:[],linkGroup:null};u[T]=M,r.seriesInvolved=r.seriesInvolved||A;var D=fS(i,y);if(D!=null){var C=o[D]||(o[D]={axesInfo:{}});C.axesInfo[T]=M,C.mapper=i[D].mapper,M.linkGroup=C}}}})}function lS(r,e,t,n,a,i){var o=e.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],l={};b(s,function(h){l[h]=$(o.get(h))}),l.snap=r.type!=="category"&&!!i,o.get("type")==="cross"&&(l.type="line");var u=l.label||(l.label={});if(u.show==null&&(u.show=!1),a==="cross"){var f=o.get(["label","show"]);if(u.show=f??!0,!i){var c=l.lineStyle=o.get("crossStyle");c&&ee(u,c.textStyle)}}return r.model.getModel("axisPointer",new J(l,t,n))}function uS(r,e){e.eachSeries(function(t){var n=t.coordinateSystem,a=t.get(["tooltip","trigger"],!0),i=t.get(["tooltip","show"],!0);!n||a==="none"||a===!1||a==="item"||i===!1||t.get(["axisPointer","show"],!0)===!1||b(r.coordSysAxesInfo[en(n.model)],function(o){var s=o.axis;n.getAxis(s.dim)===s&&(o.seriesModels.push(t),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=t.getData().count())})})}function fS(r,e){for(var t=e.model,n=e.dim,a=0;a<r.length;a++){var i=r[a]||{};if(gi(i[n+"AxisId"],t.id)||gi(i[n+"AxisIndex"],t.componentIndex)||gi(i[n+"AxisName"],t.name))return a}}function gi(r,e){return r==="all"||R(r)&&ie(r,e)>=0||r===e}function cS(r){var e=fs(r);if(e){var t=e.axisPointerModel,n=e.axis.scale,a=t.option,i=t.get("status"),o=t.get("value");o!=null&&(o=n.parse(o));var s=no(t);i==null&&(a.status=s?"show":"hide");var l=n.getExtent().slice();l[0]>l[1]&&l.reverse(),(o==null||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),a.value=o,s&&(a.status=e.axis.scale.isBlank()?"hide":"show")}}function fs(r){var e=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[en(r)]}function vS(r){var e=fs(r);return e&&e.axisPointerModel}function no(r){return!!r.get(["handle","show"])}function en(r){return r.type+"||"+r.id}var Du={},Qv=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,n,a,i){this.axisPointerClass&&cS(t),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,a,!0)},e.prototype.updateAxisPointer=function(t,n,a,i){this._doUpdateAxisPointerClass(t,a,!1)},e.prototype.remove=function(t,n){var a=this._axisPointer;a&&a.remove(n)},e.prototype.dispose=function(t,n){this._disposeAxisPointer(n),r.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,a){var i=e.getAxisPointerClass(this.axisPointerClass);if(i){var o=vS(t);o?(this._axisPointer||(this._axisPointer=new i)).render(t,o,n,a):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,n){Du[t]=n},e.getAxisPointerClass=function(t){return t&&Du[t]},e.type="axis",e}(Be),ao=re();function hS(r,e,t,n){var a=t.axis;if(!a.scale.isBlank()){var i=t.getModel("splitArea"),o=i.getModel("areaStyle"),s=o.get("color"),l=n.coordinateSystem.getRect(),u=a.getTicksCoords({tickModel:i,clamp:!0});if(u.length){var f=s.length,c=ao(r).splitAreaColors,h=W(),v=0;if(c)for(var d=0;d<u.length;d++){var p=c.get(u[d].tickValue);if(p!=null){v=(p+(f-1)*d)%f;break}}var g=a.toGlobalCoord(u[0].coord),m=o.getAreaStyle();s=R(s)?s:[s];for(var d=1;d<u.length;d++){var y=a.toGlobalCoord(u[d].coord),_=void 0,S=void 0,w=void 0,x=void 0;a.isHorizontal()?(_=g,S=l.y,w=y-_,x=l.height,g=_+w):(_=l.x,S=g,w=l.width,x=y-S,g=S+x);var T=u[d-1].tickValue;T!=null&&h.set(T,v),e.add(new fe({anid:T!=null?"area_"+T:null,shape:{x:_,y:S,width:w,height:x},style:ee({fill:s[v]},m),autoBatch:!0,silent:!0})),v=(v+1)%f}ao(r).splitAreaColors=h}}}function dS(r){ao(r).splitAreaColors=null}var pS=["axisLine","axisTickLabel","axisName"],gS=["splitArea","splitLine","minorSplitLine"],Jv=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="CartesianAxisPointer",t}return e.prototype.render=function(t,n,a,i){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new me,this.group.add(this._axisGroup),!!t.get("show")){var s=t.getCoordSysModel(),l=ro(s,t),u=new bt(t,N({handleAutoShown:function(c){for(var h=s.coordinateSystem.getCartesians(),v=0;v<h.length;v++)if(Ki(h[v].getOtherAxis(t.axis).scale))return!0;return!1}},l));b(pS,u.add,u),this._axisGroup.add(u.getGroup()),b(gS,function(c){t.get([c,"show"])&&mS[c](this,this._axisGroup,t,s)},this);var f=i&&i.type==="changeAxisOrder"&&i.isInitSort;f||Nf(o,this._axisGroup,t),r.prototype.render.call(this,t,n,a,i)}},e.prototype.remove=function(){dS(this)},e.type="cartesianAxis",e}(Qv),mS={splitLine:function(r,e,t,n){var a=t.axis;if(!a.scale.isBlank()){var i=t.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color"),l=i.get("showMinLine")!==!1,u=i.get("showMaxLine")!==!1;s=R(s)?s:[s];for(var f=n.coordinateSystem.getRect(),c=a.isHorizontal(),h=0,v=a.getTicksCoords({tickModel:i}),d=[],p=[],g=o.getLineStyle(),m=0;m<v.length;m++){var y=a.toGlobalCoord(v[m].coord);if(!(m===0&&!l||m===v.length-1&&!u)){var _=v[m].tickValue;c?(d[0]=y,d[1]=f.y,p[0]=y,p[1]=f.y+f.height):(d[0]=f.x,d[1]=y,p[0]=f.x+f.width,p[1]=y);var S=h++%s.length,w=new wt({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:p[0],y2:p[1]},style:ee({stroke:s[S]},g),silent:!0});Xr(w.shape,g.lineWidth),e.add(w)}}}},minorSplitLine:function(r,e,t,n){var a=t.axis,i=t.getModel("minorSplitLine"),o=i.getModel("lineStyle"),s=n.coordinateSystem.getRect(),l=a.isHorizontal(),u=a.getMinorTicksCoords();if(u.length)for(var f=[],c=[],h=o.getLineStyle(),v=0;v<u.length;v++)for(var d=0;d<u[v].length;d++){var p=a.toGlobalCoord(u[v][d].coord);l?(f[0]=p,f[1]=s.y,c[0]=p,c[1]=s.y+s.height):(f[0]=s.x,f[1]=p,c[0]=s.x+s.width,c[1]=p);var g=new wt({anid:"minor_line_"+u[v][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:c[0],y2:c[1]},style:h,silent:!0});Xr(g.shape,h.lineWidth),e.add(g)}},splitArea:function(r,e,t,n){hS(r,e,t,n)}},eh=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="xAxis",e}(Jv),yS=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=eh.type,t}return e.type="yAxis",e}(Jv),_S=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type="grid",t}return e.prototype.render=function(t,n){this.group.removeAll(),t.get("show")&&this.group.add(new fe({shape:t.coordinateSystem.getRect(),style:ee({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(Be),Mu={offset:0};function SS(r){r.registerComponentView(_S),r.registerComponentModel(W0),r.registerCoordinateSystem("cartesian2d",J0),gu(r,"x",eo,Mu),gu(r,"y",eo,Mu),r.registerComponentView(eh),r.registerComponentView(yS),r.registerPreprocessor(function(e){e.xAxis&&e.yAxis&&!e.grid&&(e.grid={})})}var Rt=re(),Au=$,mi=Q,xS=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(e,t,n,a){var i=t.get("value"),o=t.get("status");if(this._axisModel=e,this._axisPointerModel=t,this._api=n,!(!a&&this._lastValue===i&&this._lastStatus===o)){this._lastValue=i,this._lastStatus=o;var s=this._group,l=this._handle;if(!o||o==="hide"){s&&s.hide(),l&&l.hide();return}s&&s.show(),l&&l.show();var u={};this.makeElOption(u,i,e,t,n);var f=u.graphicKey;f!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=f;var c=this._moveAnimation=this.determineAnimation(e,t);if(!s)s=this._group=new me,this.createPointerEl(s,u,e,t),this.createLabelEl(s,u,e,t),n.getZr().add(s);else{var h=le(Iu,t,c);this.updatePointerEl(s,u,h),this.updateLabelEl(s,u,h,t)}Pu(s,t,!0),this._renderHandle(i)}},r.prototype.remove=function(e){this.clear(e)},r.prototype.dispose=function(e){this.clear(e)},r.prototype.determineAnimation=function(e,t){var n=t.get("animation"),a=e.axis,i=a.type==="category",o=t.get("snap");if(!o&&!i)return!1;if(n==="auto"||n==null){var s=this.animationThreshold;if(i&&a.getBandWidth()>s)return!0;if(o){var l=fs(e).seriesDataCount,u=a.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return n===!0},r.prototype.makeElOption=function(e,t,n,a,i){},r.prototype.createPointerEl=function(e,t,n,a){var i=t.pointer;if(i){var o=Rt(e).pointerEl=new fp[i.type](Au(t.pointer));e.add(o)}},r.prototype.createLabelEl=function(e,t,n,a){if(t.label){var i=Rt(e).labelEl=new we(Au(t.label));e.add(i),Lu(i,a)}},r.prototype.updatePointerEl=function(e,t,n){var a=Rt(e).pointerEl;a&&t.pointer&&(a.setStyle(t.pointer.style),n(a,{shape:t.pointer.shape}))},r.prototype.updateLabelEl=function(e,t,n,a){var i=Rt(e).labelEl;i&&(i.setStyle(t.label.style),n(i,{x:t.label.x,y:t.label.y}),Lu(i,a))},r.prototype._renderHandle=function(e){if(!(this._dragging||!this.updateHandleTransform)){var t=this._axisPointerModel,n=this._api.getZr(),a=this._handle,i=t.getModel("handle"),o=t.get("status");if(!i.get("show")||!o||o==="hide"){a&&n.remove(a),this._handle=null;return}var s;this._handle||(s=!0,a=this._handle=fa(i.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(u){Yh(u.event)},onmousedown:mi(this._onHandleDragMove,this,0,0),drift:mi(this._onHandleDragMove,this),ondragend:mi(this._onHandleDragEnd,this)}),n.add(a)),Pu(a,t,!1),a.setStyle(i.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var l=i.get("size");R(l)||(l=[l,l]),a.scaleX=l[0]/2,a.scaleY=l[1]/2,Dc(this,"_doDispatchAxisPointer",i.get("throttle")||0,"fixRate"),this._moveHandleToValue(e,s)}},r.prototype._moveHandleToValue=function(e,t){Iu(this._axisPointerModel,!t&&this._moveAnimation,this._handle,yi(this.getHandleTransform(e,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(e,t){var n=this._handle;if(n){this._dragging=!0;var a=this.updateHandleTransform(yi(n),[e,t],this._axisModel,this._axisPointerModel);this._payloadInfo=a,n.stopAnimation(),n.attr(yi(a)),Rt(n).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var e=this._handle;if(e){var t=this._payloadInfo,n=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:n.axis.dim,axisIndex:n.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var e=this._handle;if(e){var t=this._axisPointerModel.get("value");this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(e){this._lastValue=null,this._lastStatus=null;var t=e.getZr(),n=this._group,a=this._handle;t&&n&&(this._lastGraphicKey=null,n&&t.remove(n),a&&t.remove(a),this._group=null,this._handle=null,this._payloadInfo=null),zi(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(e,t,n){return n=n||0,{x:e[n],y:e[1-n],width:t[n],height:t[1-n]}},r}();function Iu(r,e,t,n){th(Rt(t).lastProp,n)||(Rt(t).lastProp=n,e?he(t,n,r):(t.stopAnimation(),t.attr(n)))}function th(r,e){if(V(r)&&V(e)){var t=!0;return b(e,function(n,a){t=t&&th(r[a],n)}),!!t}else return r===e}function Lu(r,e){r[e.get(["label","show"])?"show":"hide"]()}function yi(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Pu(r,e,t){var n=e.get("z"),a=e.get("zlevel");r&&r.traverse(function(i){i.type!=="group"&&(n!=null&&(i.z=n),a!=null&&(i.zlevel=a),i.silent=t)})}function bS(r){var e=r.get("type"),t=r.getModel(e+"Style"),n;return e==="line"?(n=t.getLineStyle(),n.fill=null):e==="shadow"&&(n=t.getAreaStyle(),n.stroke=null),n}function wS(r,e,t,n,a){var i=t.get("value"),o=rh(i,e.axis,e.ecModel,t.get("seriesDataIndices"),{precision:t.get(["label","precision"]),formatter:t.get(["label","formatter"])}),s=t.getModel("label"),l=on(s.get("padding")||0),u=s.getFont(),f=rf(o,u),c=a.position,h=f.width+l[1]+l[3],v=f.height+l[0]+l[2],d=a.align;d==="right"&&(c[0]-=h),d==="center"&&(c[0]-=h/2);var p=a.verticalAlign;p==="bottom"&&(c[1]-=v),p==="middle"&&(c[1]-=v/2),TS(c,h,v,n);var g=s.get("backgroundColor");(!g||g==="auto")&&(g=e.get(["axisLine","lineStyle","color"])),r.label={x:c[0],y:c[1],style:zt(s,{text:o,font:u,fill:s.getTextColor(),padding:l,backgroundColor:g}),z2:10}}function TS(r,e,t,n){var a=n.getWidth(),i=n.getHeight();r[0]=Math.min(r[0]+e,a)-e,r[1]=Math.min(r[1]+t,i)-t,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function rh(r,e,t,n,a){r=e.scale.parse(r);var i=e.scale.getLabel({value:r},{precision:a.precision}),o=a.formatter;if(o){var s={value:as(e,{value:r}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};b(n,function(l){var u=t.getSeriesByIndex(l.seriesIndex),f=l.dataIndexInside,c=u&&u.getDataParams(f);c&&s.seriesData.push(c)}),B(o)?i=o.replace("{value}",i):H(o)&&(i=o(s))}return i}function nh(r,e,t){var n=Xh();return af(n,n,t.rotation),Zh(n,n,t.position),Io([r.dataToCoord(e),(t.labelOffset||0)+(t.labelDirection||1)*(t.labelMargin||0)],n)}function CS(r,e,t,n,a,i){var o=bt.innerTextLayout(t.rotation,0,t.labelDirection);t.labelMargin=a.get(["label","margin"]),wS(e,n,a,i,{position:nh(n.axis,r,t),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function DS(r,e,t){return t=t||0,{x1:r[t],y1:r[1-t],x2:e[t],y2:e[1-t]}}function MS(r,e,t){return t=t||0,{x:r[t],y:r[1-t],width:e[t],height:e[1-t]}}var AS=function(r){z(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,n,a,i,o){var s=a.axis,l=s.grid,u=i.get("type"),f=ku(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(n,!0));if(u&&u!=="none"){var h=bS(i),v=IS[u](s,c,f);v.style=h,t.graphicKey=v.type,t.pointer=v}var d=ro(l.model,a);CS(n,t,d,a,i,o)},e.prototype.getHandleTransform=function(t,n,a){var i=ro(n.axis.grid.model,n,{labelInside:!1});i.labelMargin=a.get(["handle","margin"]);var o=nh(n.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,n,a,i){var o=a.axis,s=o.grid,l=o.getGlobalExtent(!0),u=ku(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,c=[t.x,t.y];c[f]+=n[f],c[f]=Math.min(l[1],c[f]),c[f]=Math.max(l[0],c[f]);var h=(u[1]+u[0])/2,v=[h,h];v[f]=c[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:c[0],y:c[1],rotation:t.rotation,cursorPoint:v,tooltipOption:d[f]}},e}(xS);function ku(r,e){var t={};return t[e.dim+"AxisIndex"]=e.index,r.getCartesian(t)}var IS={line:function(r,e,t){var n=DS([e,t[0]],[e,t[1]],Eu(r));return{type:"Line",subPixelOptimize:!0,shape:n}},shadow:function(r,e,t){var n=Math.max(1,r.getBandWidth()),a=t[1]-t[0];return{type:"Rect",shape:MS([e-n/2,t[0]],[n,a],Eu(r))}}};function Eu(r){return r.dim==="x"?0:1}var LS=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="axisPointer",e.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},e}(X),ot=re(),PS=b;function ah(r,e,t){if(!te.node){var n=e.getZr();ot(n).records||(ot(n).records={}),kS(n,e);var a=ot(n).records[r]||(ot(n).records[r]={});a.handler=t}}function kS(r,e){if(ot(r).initialized)return;ot(r).initialized=!0,t("click",le(Ru,"click")),t("mousemove",le(Ru,"mousemove")),t("globalout",RS);function t(n,a){r.on(n,function(i){var o=OS(e);PS(ot(r).records,function(s){s&&a(s,i,o.dispatchAction)}),ES(o.pendings,e)})}}function ES(r,e){var t=r.showTip.length,n=r.hideTip.length,a;t?a=r.showTip[t-1]:n&&(a=r.hideTip[n-1]),a&&(a.dispatchAction=null,e.dispatchAction(a))}function RS(r,e,t){r.handler("leave",null,t)}function Ru(r,e,t,n){e.handler(r,t,n)}function OS(r){var e={showTip:[],hideTip:[]},t=function(n){var a=e[n.type];a?a.push(n):(n.dispatchAction=t,r.dispatchAction(n))};return{dispatchAction:t,pendings:e}}function io(r,e){if(!te.node){var t=e.getZr(),n=(ot(t).records||{})[r];n&&(ot(t).records[r]=null)}}var NS=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,n,a){var i=n.getComponent("tooltip"),o=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";ah("axisPointer",a,function(s,l,u){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&u({type:"updateAxisPointer",currTrigger:s,x:l&&l.offsetX,y:l&&l.offsetY})})},e.prototype.remove=function(t,n){io("axisPointer",n)},e.prototype.dispose=function(t,n){io("axisPointer",n)},e.type="axisPointer",e}(Be);function ih(r,e){var t=[],n=r.seriesIndex,a;if(n==null||!(a=e.getSeriesByIndex(n)))return{point:[]};var i=a.getData(),o=Vt(i,r);if(o==null||o<0||R(o))return{point:[]};var s=i.getItemGraphicEl(o),l=a.coordinateSystem;if(a.getTooltipPosition)t=a.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)if(r.isStacked){var u=l.getBaseAxis(),f=l.getOtherAxis(u),c=f.dim,h=u.dim,v=c==="x"||c==="radius"?1:0,d=i.mapDimension(h),p=[];p[v]=i.get(d,o),p[1-v]=i.get(i.getCalculationInfo("stackResultDimension"),o),t=l.dataToPoint(p)||[]}else t=l.dataToPoint(i.getValues(F(l.dimensions,function(m){return i.mapDimension(m)}),o))||[];else if(s){var g=s.getBoundingRect().clone();g.applyTransform(s.transform),t=[g.x+g.width/2,g.y+g.height/2]}return{point:t,el:s}}var Ou=re();function BS(r,e,t){var n=r.currTrigger,a=[r.x,r.y],i=r,o=r.dispatchAction||Q(t.dispatchAction,t),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Rn(a)&&(a=ih({seriesIndex:i.seriesIndex,dataIndex:i.dataIndex},e).point);var l=Rn(a),u=i.axesInfo,f=s.axesInfo,c=n==="leave"||Rn(a),h={},v={},d={list:[],map:{}},p={showPointer:le(GS,v),showTooltip:le(VS,d)};b(s.coordSysMap,function(m,y){var _=l||m.containPoint(a);b(s.coordSysAxesInfo[y],function(S,w){var x=S.axis,T=US(u,S);if(!c&&_&&(!u||T)){var A=T&&T.value;A==null&&!l&&(A=x.pointToData(a)),A!=null&&Nu(S,A,p,!1,h)}})});var g={};return b(f,function(m,y){var _=m.linkGroup;_&&!v[y]&&b(_.axesInfo,function(S,w){var x=v[w];if(S!==m&&x){var T=x.value;_.mapper&&(T=m.axis.scale.parse(_.mapper(T,Bu(S),Bu(m)))),g[m.key]=T}})}),b(g,function(m,y){Nu(f[y],m,p,!0,h)}),zS(v,f,h),HS(d,a,r,o),WS(f,o,t),h}}function Nu(r,e,t,n,a){var i=r.axis;if(!(i.scale.isBlank()||!i.containData(e))){if(!r.involveSeries){t.showPointer(r,e);return}var o=FS(e,r),s=o.payloadBatch,l=o.snapToValue;s[0]&&a.seriesIndex==null&&N(a,s[0]),!n&&r.snap&&i.containData(l)&&l!=null&&(e=l),t.showPointer(r,e,s),t.showTooltip(r,o,l)}}function FS(r,e){var t=e.axis,n=t.dim,a=r,i=[],o=Number.MAX_VALUE,s=-1;return b(e.seriesModels,function(l,u){var f=l.getData().mapDimensionsAll(n),c,h;if(l.getAxisTooltipData){var v=l.getAxisTooltipData(f,r,t);h=v.dataIndices,c=v.nestestValue}else{if(h=l.getData().indicesOfNearest(f[0],r,t.type==="category"?.5:null),!h.length)return;c=l.getData().get(f[0],h[0])}if(!(c==null||!isFinite(c))){var d=r-c,p=Math.abs(d);p<=o&&((p<o||d>=0&&s<0)&&(o=p,s=d,a=c,i.length=0),b(h,function(g){i.push({seriesIndex:l.seriesIndex,dataIndexInside:g,dataIndex:l.getData().getRawIndex(g)})}))}}),{payloadBatch:i,snapToValue:a}}function GS(r,e,t,n){r[e.key]={value:t,payloadBatch:n}}function VS(r,e,t,n){var a=t.payloadBatch,i=e.axis,o=i.model,s=e.axisPointerModel;if(!(!e.triggerTooltip||!a.length)){var l=e.coordSys.model,u=en(l),f=r.map[u];f||(f=r.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:i.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:n,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:a.slice()})}}function zS(r,e,t){var n=t.axesInfo=[];b(e,function(a,i){var o=a.axisPointerModel.option,s=r[i];s?(!a.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!a.useHandle&&(o.status="hide"),o.status==="show"&&n.push({axisDim:a.axis.dim,axisIndex:a.axis.model.componentIndex,value:o.value})})}function HS(r,e,t,n){if(Rn(e)||!r.list.length){n({type:"hideTip"});return}var a=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};n({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:t.tooltipOption,position:t.position,dataIndexInside:a.dataIndexInside,dataIndex:a.dataIndex,seriesIndex:a.seriesIndex,dataByCoordSys:r.list})}function WS(r,e,t){var n=t.getZr(),a="axisPointerLastHighlights",i=Ou(n)[a]||{},o=Ou(n)[a]={};b(r,function(u,f){var c=u.axisPointerModel.option;c.status==="show"&&u.triggerEmphasis&&b(c.seriesDataIndices,function(h){var v=h.seriesIndex+" | "+h.dataIndex;o[v]=h})});var s=[],l=[];b(i,function(u,f){!o[f]&&l.push(u)}),b(o,function(u,f){!i[f]&&s.push(u)}),l.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function US(r,e){for(var t=0;t<(r||[]).length;t++){var n=r[t];if(e.axis.dim===n.axisDim&&e.axis.model.componentIndex===n.axisIndex)return n}}function Bu(r){var e=r.axis.model,t={},n=t.axisDim=r.axis.dim;return t.axisIndex=t[n+"AxisIndex"]=e.componentIndex,t.axisName=t[n+"AxisName"]=e.name,t.axisId=t[n+"AxisId"]=e.id,t}function Rn(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function oh(r){Qv.registerAxisPointerClass("CartesianAxisPointer",AS),r.registerComponentModel(LS),r.registerComponentView(NS),r.registerPreprocessor(function(e){if(e){(!e.axisPointer||e.axisPointer.length===0)&&(e.axisPointer={});var t=e.axisPointer.link;t&&!R(t)&&(e.axisPointer.link=[t])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(e,t){e.getComponent("axisPointer").coordSysAxesInfo=oS(e,t)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},BS)}function Cx(r){Ct(SS),Ct(oh)}function YS(r,e){var t=on(e.get("padding")),n=e.getItemStyle(["color","opacity"]);return n.fill=e.get("backgroundColor"),r=new fe({shape:{x:r.x-t[3],y:r.y-t[0],width:r.width+t[1]+t[3],height:r.height+t[0]+t[2],r:e.get("borderRadius")},style:n,silent:!0,z2:-1}),r}var XS=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="tooltip",e.dependencies=["axisPointer"],e.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},e}(X);function sh(r){var e=r.get("confine");return e!=null?!!e:r.get("renderMode")==="richText"}function lh(r){if(te.domSupported){for(var e=document.documentElement.style,t=0,n=r.length;t<n;t++)if(r[t]in e)return r[t]}}var uh=lh(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),ZS=lh(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function fh(r,e){if(!r)return e;e=Bo(e,!0);var t=r.indexOf(e);return r=t===-1?e:"-"+r.slice(0,t)+"-"+e,r.toLowerCase()}function $S(r,e){var t=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return t?t[e]:null}var KS=fh(ZS,"transition"),cs=fh(uh,"transform"),qS="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(te.transform3dSupported?"will-change:transform;":"");function jS(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function QS(r,e,t){if(!B(t)||t==="inside")return"";var n=r.get("backgroundColor"),a=r.get("borderWidth");e=Ht(e);var i=jS(t),o=Math.max(Math.round(a)*1.5,6),s="",l=cs+":",u;ie(["left","right"],i)>-1?(s+="top:50%",l+="translateY(-50%) rotate("+(u=i==="left"?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(u=i==="top"?225:45)+"deg)");var f=u*Math.PI/180,c=o+a,h=c*Math.abs(Math.cos(f))+c*Math.abs(Math.sin(f)),v=Math.round(((h-Math.SQRT2*a)/2+Math.SQRT2*a-(h-c)/2)*100)/100;s+=";"+i+":-"+v+"px";var d=e+" solid "+a+"px;",p=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+l+";","border-bottom:"+d,"border-right:"+d,"background-color:"+n+";"];return'<div style="'+p.join("")+'"></div>'}function JS(r,e){var t="cubic-bezier(0.23,1,0.32,1)",n=" "+r/2+"s "+t,a="opacity"+n+",visibility"+n;return e||(n=" "+r+"s "+t,a+=te.transformSupported?","+cs+n:",left"+n+",top"+n),KS+":"+a}function Fu(r,e,t){var n=r.toFixed(0)+"px",a=e.toFixed(0)+"px";if(!te.transformSupported)return t?"top:"+a+";left:"+n+";":[["top",a],["left",n]];var i=te.transform3dSupported,o="translate"+(i?"3d":"")+"("+n+","+a+(i?",0":"")+")";return t?"top:0;left:0;"+cs+":"+o+";":[["top",0],["left",0],[uh,o]]}function ex(r){var e=[],t=r.get("fontSize"),n=r.getTextColor();n&&e.push("color:"+n),e.push("font:"+r.getFont());var a=K(r.get("lineHeight"),Math.round(t*3/2));t&&e.push("line-height:"+a+"px");var i=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,l=r.get("textShadowOffsetY")||0;return i&&o&&e.push("text-shadow:"+s+"px "+l+"px "+o+"px "+i),b(["decoration","align"],function(u){var f=r.get(u);f&&e.push("text-"+u+":"+f)}),e.join(";")}function tx(r,e,t){var n=[],a=r.get("transitionDuration"),i=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),l=r.get("shadowOffsetX"),u=r.get("shadowOffsetY"),f=r.getModel("textStyle"),c=Tc(r,"html"),h=l+"px "+u+"px "+o+"px "+s;return n.push("box-shadow:"+h),e&&a&&n.push(JS(a,t)),i&&n.push("background-color:"+i),b(["width","color","radius"],function(v){var d="border-"+v,p=Bo(d),g=r.get(p);g!=null&&n.push(d+":"+g+(v==="color"?"":"px"))}),n.push(ex(f)),c!=null&&n.push("padding:"+on(c).join("px ")+"px"),n.join(";")+";"}function Gu(r,e,t,n,a){var i=e&&e.painter;if(t){var o=i&&i.getViewportRoot();o&&Kh(r,o,t,n,a)}else{r[0]=n,r[1]=a;var s=i&&i.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/e.getWidth(),r[3]=r[1]/e.getHeight()}var rx=function(){function r(e,t){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,te.wxa)return null;var n=document.createElement("div");n.domBelongToZr=!0,this.el=n;var a=this._zr=e.getZr(),i=t.appendTo,o=i&&(B(i)?document.querySelector(i):Ss(i)?i:H(i)&&i(e.getDom()));Gu(this._styleCoord,a,o,e.getWidth()/2,e.getHeight()/2),(o||e.getDom()).appendChild(n),this._api=e,this._container=o;var s=this;n.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},n.onmousemove=function(l){if(l=l||window.event,!s._enterable){var u=a.handler,f=a.painter.getViewportRoot();$h(f,l,!0),u.dispatch("mousemove",l)}},n.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(e){if(!this._container){var t=this._api.getDom(),n=$S(t,"position"),a=t.style;a.position!=="absolute"&&n!=="absolute"&&(a.position="relative")}var i=e.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i,this.el.className=e.get("className")||""},r.prototype.show=function(e,t){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,a=n.style,i=this._styleCoord;n.innerHTML?a.cssText=qS+tx(e,!this._firstShow,this._longHide)+Fu(i[0],i[1],!0)+("border-color:"+Ht(t)+";")+(e.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):a.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(e,t,n,a,i){var o=this.el;if(e==null){o.innerHTML="";return}var s="";if(B(i)&&n.get("trigger")==="item"&&!sh(n)&&(s=QS(n,a,i)),B(e))o.innerHTML=e+s;else if(e){o.innerHTML="",R(e)||(e=[e]);for(var l=0;l<e.length;l++)Ss(e[l])&&e[l].parentNode!==o&&o.appendChild(e[l]);if(s&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=s,o.appendChild(u)}}},r.prototype.setEnterable=function(e){this._enterable=e},r.prototype.getSize=function(){var e=this.el;return e?[e.offsetWidth,e.offsetHeight]:[0,0]},r.prototype.moveTo=function(e,t){if(this.el){var n=this._styleCoord;if(Gu(n,this._zr,this._container,e,t),n[0]!=null&&n[1]!=null){var a=this.el.style,i=Fu(n[0],n[1]);b(i,function(o){a[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var e=this._styleCoord[2],t=this._styleCoord[3];this.moveTo(e*this._zr.getWidth(),t*this._zr.getHeight())},r.prototype.hide=function(){var e=this,t=this.el.style;t.visibility="hidden",t.opacity="0",te.transform3dSupported&&(t.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return e._longHide=!0},500)},r.prototype.hideLater=function(e){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(Q(this.hide,this),e)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var e=this.el.parentNode;e&&e.removeChild(this.el),this.el=this._container=null},r}(),nx=function(){function r(e){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=e.getZr(),zu(this._styleCoord,this._zr,e.getWidth()/2,e.getHeight()/2)}return r.prototype.update=function(e){var t=e.get("alwaysShowContent");t&&this._moveIfResized(),this._alwaysShowContent=t},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(e,t,n,a,i){var o=this;V(e)&&De(""),this.el&&this._zr.remove(this.el);var s=n.getModel("textStyle");this.el=new we({style:{rich:t.richTextStyles,text:e,lineHeight:22,borderWidth:1,borderColor:a,textShadowColor:s.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:Tc(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),b(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(u){o.el.style[u]=n.get(u)}),b(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(u){o.el.style[u]=s.get(u)||0}),this._zr.add(this.el);var l=this;this.el.on("mouseover",function(){l._enterable&&(clearTimeout(l._hideTimeout),l._show=!0),l._inContent=!0}),this.el.on("mouseout",function(){l._enterable&&l._show&&l.hideLater(l._hideDelay),l._inContent=!1})},r.prototype.setEnterable=function(e){this._enterable=e},r.prototype.getSize=function(){var e=this.el,t=this.el.getBoundingRect(),n=Vu(e.style);return[t.width+n.left+n.right,t.height+n.top+n.bottom]},r.prototype.moveTo=function(e,t){var n=this.el;if(n){var a=this._styleCoord;zu(a,this._zr,e,t),e=a[0],t=a[1];var i=n.style,o=_t(i.borderWidth||0),s=Vu(i);n.x=e+o+s.left,n.y=t+o+s.top,n.markRedraw()}},r.prototype._moveIfResized=function(){var e=this._styleCoord[2],t=this._styleCoord[3];this.moveTo(e*this._zr.getWidth(),t*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(e){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(Q(this.hide,this),e)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function _t(r){return Math.max(0,r)}function Vu(r){var e=_t(r.shadowBlur||0),t=_t(r.shadowOffsetX||0),n=_t(r.shadowOffsetY||0);return{left:_t(e-t),right:_t(e+t),top:_t(e-n),bottom:_t(e+n)}}function zu(r,e,t,n){r[0]=t,r[1]=n,r[2]=r[0]/e.getWidth(),r[3]=r[1]/e.getHeight()}var ax=new fe({shape:{x:-1,y:-1,width:2,height:2}}),ix=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,n){if(!(te.node||!n.getDom())){var a=t.getComponent("tooltip"),i=this._renderMode=bd(a.get("renderMode"));this._tooltipContent=i==="richText"?new nx(n):new rx(n,{appendTo:a.get("appendToBody",!0)?"body":a.get("appendTo",!0)})}},e.prototype.render=function(t,n,a){if(!(te.node||!a.getDom())){this.group.removeAll(),this._tooltipModel=t,this._ecModel=n,this._api=a;var i=this._tooltipContent;i.update(t),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&t.get("transitionDuration")?Dc(this,"_updatePosition",50,"fixRate"):zi(this,"_updatePosition")}},e.prototype._initGlobalListener=function(){var t=this._tooltipModel,n=t.get("triggerOn");ah("itemTooltip",this._api,Q(function(a,i,o){n!=="none"&&(n.indexOf(a)>=0?this._tryShow(i,o):a==="leave"&&this._hide(o))},this))},e.prototype._keepShow=function(){var t=this._tooltipModel,n=this._ecModel,a=this._api,i=t.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&i!=="none"&&i!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!a.isDisposed()&&o.manuallyShowTip(t,n,a,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},e.prototype.manuallyShowTip=function(t,n,a,i){if(!(i.from===this.uid||te.node||!a.getDom())){var o=Hu(i,a);this._ticket="";var s=i.dataByCoordSys,l=ux(i,n,a);if(l){var u=l.el.getBoundingRect().clone();u.applyTransform(l.el.transform),this._tryShow({offsetX:u.x+u.width/2,offsetY:u.y+u.height/2,target:l.el,position:i.position,positionDefault:"bottom"},o)}else if(i.tooltip&&i.x!=null&&i.y!=null){var f=ax;f.x=i.x,f.y=i.y,f.update(),Y(f).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:f},o)}else if(s)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:s,tooltipOption:i.tooltipOption},o);else if(i.seriesIndex!=null){if(this._manuallyAxisShowTip(t,n,a,i))return;var c=ih(i,n),h=c.point[0],v=c.point[1];h!=null&&v!=null&&this._tryShow({offsetX:h,offsetY:v,target:c.el,position:i.position,positionDefault:"bottom"},o)}else i.x!=null&&i.y!=null&&(a.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:a.getZr().findHover(i.x,i.y).target},o))}},e.prototype.manuallyHideTip=function(t,n,a,i){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(Hu(i,a))},e.prototype._manuallyAxisShowTip=function(t,n,a,i){var o=i.seriesIndex,s=i.dataIndex,l=n.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||l==null)){var u=n.getSeriesByIndex(o);if(u){var f=u.getData(),c=Dr([f.getItemModel(s),u,(u.coordinateSystem||{}).model],this._tooltipModel);if(c.get("trigger")==="axis")return a.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:i.position}),!0}}},e.prototype._tryShow=function(t,n){var a=t.target,i=this._tooltipModel;if(i){this._lastX=t.offsetX,this._lastY=t.offsetY;var o=t.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,t);else if(a){var s=Y(a);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var l,u;Pr(a,function(f){if(Y(f).dataIndex!=null)return l=f,!0;if(Y(f).tooltipConfig!=null)return u=f,!0},!0),l?this._showSeriesItemTooltip(t,l,n):u?this._showComponentItemTooltip(t,u,n):this._hide(n)}else this._lastDataByCoordSys=null,this._hide(n)}},e.prototype._showOrMove=function(t,n){var a=t.get("showDelay");n=Q(n,this),clearTimeout(this._showTimout),a>0?this._showTimout=setTimeout(n,a):n()},e.prototype._showAxisTooltip=function(t,n){var a=this._ecModel,i=this._tooltipModel,o=[n.offsetX,n.offsetY],s=Dr([n.tooltipOption],i),l=this._renderMode,u=[],f=jr("section",{blocks:[],noHeader:!0}),c=[],h=new Xa;b(t,function(y){b(y.dataByAxis,function(_){var S=a.getComponent(_.axisDim+"Axis",_.axisIndex),w=_.value;if(!(!S||w==null)){var x=rh(w,S.axis,a,_.seriesDataIndices,_.valueLabelOpt),T=jr("section",{header:x,noHeader:!tn(x),sortBlocks:!0,blocks:[]});f.blocks.push(T),b(_.seriesDataIndices,function(A){var M=a.getSeriesByIndex(A.seriesIndex),D=A.dataIndexInside,C=M.getDataParams(D);if(!(C.dataIndex<0)){C.axisDim=_.axisDim,C.axisIndex=_.axisIndex,C.axisType=_.axisType,C.axisId=_.axisId,C.axisValue=as(S.axis,{value:w}),C.axisValueLabel=x,C.marker=h.makeTooltipMarker("item",Ht(C.color),l);var L=ul(M.formatTooltip(D,!0,null)),I=L.frag;if(I){var P=Dr([M],i).get("valueFormatter");T.blocks.push(P?N({valueFormatter:P},I):I)}L.text&&c.push(L.text),u.push(C)}})}})}),f.blocks.reverse(),c.reverse();var v=n.position,d=s.get("order"),p=dl(f,h,l,d,a.get("useUTC"),s.get("textStyle"));p&&c.unshift(p);var g=l==="richText"?`

`:"<br/>",m=c.join(g);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(t,u)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,u):this._showTooltipContent(s,m,u,Math.random()+"",o[0],o[1],v,null,h)})},e.prototype._showSeriesItemTooltip=function(t,n,a){var i=this._ecModel,o=Y(n),s=o.seriesIndex,l=i.getSeriesByIndex(s),u=o.dataModel||l,f=o.dataIndex,c=o.dataType,h=u.getData(c),v=this._renderMode,d=t.positionDefault,p=Dr([h.getItemModel(f),u,l&&(l.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),g=p.get("trigger");if(!(g!=null&&g!=="item")){var m=u.getDataParams(f,c),y=new Xa;m.marker=y.makeTooltipMarker("item",Ht(m.color),v);var _=ul(u.formatTooltip(f,!1,c)),S=p.get("order"),w=p.get("valueFormatter"),x=_.frag,T=x?dl(w?N({valueFormatter:w},x):x,y,v,S,i.get("useUTC"),p.get("textStyle")):_.text,A="item_"+u.name+"_"+f;this._showOrMove(p,function(){this._showTooltipContent(p,T,m,A,t.offsetX,t.offsetY,t.position,t.target,y)}),a({type:"showTip",dataIndexInside:f,dataIndex:h.getRawIndex(f),seriesIndex:s,from:this.uid})}},e.prototype._showComponentItemTooltip=function(t,n,a){var i=this._renderMode==="html",o=Y(n),s=o.tooltipConfig,l=s.option||{},u=l.encodeHTMLContent;if(B(l)){var f=l;l={content:f,formatter:f},u=!0}u&&i&&l.content&&(l=$(l),l.content=xe(l.content));var c=[l],h=this._ecModel.getComponent(o.componentMainType,o.componentIndex);h&&c.push(h),c.push({formatter:l.content});var v=t.positionDefault,d=Dr(c,this._tooltipModel,v?{position:v}:null),p=d.get("content"),g=Math.random()+"",m=new Xa;this._showOrMove(d,function(){var y=$(d.get("formatterParams")||{});this._showTooltipContent(d,p,y,g,t.offsetX,t.offsetY,t.position,n,m)}),a({type:"showTip",from:this.uid})},e.prototype._showTooltipContent=function(t,n,a,i,o,s,l,u,f){if(this._ticket="",!(!t.get("showContent")||!t.get("show"))){var c=this._tooltipContent;c.setEnterable(t.get("enterable"));var h=t.get("formatter");l=l||t.get("position");var v=n,d=this._getNearestPoint([o,s],a,t.get("trigger"),t.get("borderColor")),p=d.color;if(h)if(B(h)){var g=t.ecModel.get("useUTC"),m=R(a)?a[0]:a,y=m&&m.axisType&&m.axisType.indexOf("time")>=0;v=h,y&&(v=an(m.axisValue,v,g)),v=Fo(v,a,!0)}else if(H(h)){var _=Q(function(S,w){S===this._ticket&&(c.setContent(w,f,t,p,l),this._updatePosition(t,l,o,s,c,a,u))},this);this._ticket=i,v=h(a,i,_)}else v=h;c.setContent(v,f,t,p,l),c.show(t,p),this._updatePosition(t,l,o,s,c,a,u)}},e.prototype._getNearestPoint=function(t,n,a,i){if(a==="axis"||R(n))return{color:i||(this._renderMode==="html"?"#fff":"none")};if(!R(n))return{color:i||n.color||n.borderColor}},e.prototype._updatePosition=function(t,n,a,i,o,s,l){var u=this._api.getWidth(),f=this._api.getHeight();n=n||t.get("position");var c=o.getSize(),h=t.get("align"),v=t.get("verticalAlign"),d=l&&l.getBoundingRect().clone();if(l&&d.applyTransform(l.transform),H(n)&&(n=n([a,i],s,o.el,d,{viewSize:[u,f],contentSize:c.slice()})),R(n))a=ve(n[0],u),i=ve(n[1],f);else if(V(n)){var p=n;p.width=c[0],p.height=c[1];var g=$r(p,{width:u,height:f});a=g.x,i=g.y,h=null,v=null}else if(B(n)&&l){var m=lx(n,d,c,t.get("borderWidth"));a=m[0],i=m[1]}else{var m=ox(a,i,o,u,f,h?null:20,v?null:20);a=m[0],i=m[1]}if(h&&(a-=Wu(h)?c[0]/2:h==="right"?c[0]:0),v&&(i-=Wu(v)?c[1]/2:v==="bottom"?c[1]:0),sh(t)){var m=sx(a,i,o,u,f);a=m[0],i=m[1]}o.moveTo(a,i)},e.prototype._updateContentNotChangedOnAxis=function(t,n){var a=this._lastDataByCoordSys,i=this._cbParamsList,o=!!a&&a.length===t.length;return o&&b(a,function(s,l){var u=s.dataByAxis||[],f=t[l]||{},c=f.dataByAxis||[];o=o&&u.length===c.length,o&&b(u,function(h,v){var d=c[v]||{},p=h.seriesDataIndices||[],g=d.seriesDataIndices||[];o=o&&h.value===d.value&&h.axisType===d.axisType&&h.axisId===d.axisId&&p.length===g.length,o&&b(p,function(m,y){var _=g[y];o=o&&m.seriesIndex===_.seriesIndex&&m.dataIndex===_.dataIndex}),i&&b(h.seriesDataIndices,function(m){var y=m.seriesIndex,_=n[y],S=i[y];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=t,this._cbParamsList=n,!!o},e.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},e.prototype.dispose=function(t,n){te.node||!n.getDom()||(zi(this,"_updatePosition"),this._tooltipContent.dispose(),io("itemTooltip",n))},e.type="tooltip",e}(Be);function Dr(r,e,t){var n=e.ecModel,a;t?(a=new J(t,n,n),a=new J(e.option,a,n)):a=e;for(var i=r.length-1;i>=0;i--){var o=r[i];o&&(o instanceof J&&(o=o.get("tooltip",!0)),B(o)&&(o={formatter:o}),o&&(a=new J(o,a,n)))}return a}function Hu(r,e){return r.dispatchAction||Q(e.dispatchAction,e)}function ox(r,e,t,n,a,i,o){var s=t.getSize(),l=s[0],u=s[1];return i!=null&&(r+l+i+2>n?r-=l+i:r+=i),o!=null&&(e+u+o>a?e-=u+o:e+=o),[r,e]}function sx(r,e,t,n,a){var i=t.getSize(),o=i[0],s=i[1];return r=Math.min(r+o,n)-o,e=Math.min(e+s,a)-s,r=Math.max(r,0),e=Math.max(e,0),[r,e]}function lx(r,e,t,n){var a=t[0],i=t[1],o=Math.ceil(Math.SQRT2*n)+8,s=0,l=0,u=e.width,f=e.height;switch(r){case"inside":s=e.x+u/2-a/2,l=e.y+f/2-i/2;break;case"top":s=e.x+u/2-a/2,l=e.y-i-o;break;case"bottom":s=e.x+u/2-a/2,l=e.y+f+o;break;case"left":s=e.x-a-o,l=e.y+f/2-i/2;break;case"right":s=e.x+u+o,l=e.y+f/2-i/2}return[s,l]}function Wu(r){return r==="center"||r==="middle"}function ux(r,e,t){var n=So(r).queryOptionMap,a=n.keys()[0];if(!(!a||a==="series")){var i=nn(e,a,n.get(a),{useDefault:!1,enableAll:!1,enableNone:!1}),o=i.models[0];if(o){var s=t.getViewOfComponentModel(o),l;if(s.group.traverse(function(u){var f=Y(u).tooltipConfig;if(f&&f.name===r.name)return l=u,!0}),l)return{componentMainType:a,componentIndex:o.componentIndex,el:l}}}}function Dx(r){Ct(oh),r.registerComponentModel(XS),r.registerComponentView(ix),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Tt),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Tt)}var fx=function(r,e){if(e==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(e==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},oo=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.layoutMode={type:"box",ignoreSize:!0},t}return e.prototype.init=function(t,n,a){this.mergeDefaultAndTheme(t,a),t.selected=t.selected||{},this._updateSelector(t)},e.prototype.mergeOption=function(t,n){r.prototype.mergeOption.call(this,t,n),this._updateSelector(t)},e.prototype._updateSelector=function(t){var n=t.selector,a=this.ecModel;n===!0&&(n=t.selector=["all","inverse"]),R(n)&&b(n,function(i,o){B(i)&&(i={type:i}),n[o]=j(i,fx(a,i.type))})},e.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&this.get("selectedMode")==="single"){for(var n=!1,a=0;a<t.length;a++){var i=t[a].get("name");if(this.isSelected(i)){this.select(i),n=!0;break}}!n&&this.select(t[0].get("name"))}},e.prototype._updateData=function(t){var n=[],a=[];t.eachRawSeries(function(l){var u=l.name;a.push(u);var f;if(l.legendVisualProvider){var c=l.legendVisualProvider,h=c.getAllNames();t.isSeriesFiltered(l)||(a=a.concat(h)),h.length?n=n.concat(h):f=!0}else f=!0;f&&_o(l)&&n.push(l.name)}),this._availableNames=a;var i=this.get("data")||n,o=W(),s=F(i,function(l){return(B(l)||oe(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new J(l,this,this.ecModel))},this);this._data=se(s,function(l){return!!l})},e.prototype.getData=function(){return this._data},e.prototype.select=function(t){var n=this.option.selected,a=this.get("selectedMode");if(a==="single"){var i=this._data;b(i,function(o){n[o.get("name")]=!1})}n[t]=!0},e.prototype.unSelect=function(t){this.get("selectedMode")!=="single"&&(this.option.selected[t]=!1)},e.prototype.toggleSelected=function(t){var n=this.option.selected;n.hasOwnProperty(t)||(n[t]=!0),this[n[t]?"unSelect":"select"](t)},e.prototype.allSelect=function(){var t=this._data,n=this.option.selected;b(t,function(a){n[a.get("name",!0)]=!0})},e.prototype.inverseSelect=function(){var t=this._data,n=this.option.selected;b(t,function(a){var i=a.get("name",!0);n.hasOwnProperty(i)||(n[i]=!0),n[i]=!n[i]})},e.prototype.isSelected=function(t){var n=this.option.selected;return!(n.hasOwnProperty(t)&&!n[t])&&ie(this._availableNames,t)>=0},e.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},e.type="legend.plain",e.dependencies=["series"],e.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},e}(X),Jt=le,so=b,Mn=me,ch=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!1,t}return e.prototype.init=function(){this.group.add(this._contentGroup=new Mn),this.group.add(this._selectorGroup=new Mn),this._isFirstRender=!0},e.prototype.getContentGroup=function(){return this._contentGroup},e.prototype.getSelectorGroup=function(){return this._selectorGroup},e.prototype.render=function(t,n,a){var i=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!t.get("show",!0)){var o=t.get("align"),s=t.get("orient");(!o||o==="auto")&&(o=t.get("left")==="right"&&s==="vertical"?"right":"left");var l=t.get("selector",!0),u=t.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,t,n,a,l,s,u);var f=t.getBoxLayoutParams(),c={width:a.getWidth(),height:a.getHeight()},h=t.get("padding"),v=$r(f,c,h),d=this.layoutInner(t,o,v,i,l,u),p=$r(ee({width:d.width,height:d.height},f),c,h);this.group.x=p.x-d.x,this.group.y=p.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=YS(d,t))}},e.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},e.prototype.renderInner=function(t,n,a,i,o,s,l){var u=this.getContentGroup(),f=W(),c=n.get("selectedMode"),h=[];a.eachRawSeries(function(v){!v.get("legendHoverLink")&&h.push(v.id)}),so(n.getData(),function(v,d){var p=v.get("name");if(!this.newlineDisabled&&(p===""||p===`
`)){var g=new Mn;g.newline=!0,u.add(g);return}var m=a.getSeriesByName(p)[0];if(!f.get(p))if(m){var y=m.getData(),_=y.getVisual("legendLineStyle")||{},S=y.getVisual("legendIcon"),w=y.getVisual("style"),x=this._createItem(m,p,d,v,n,t,_,w,S,c,i);x.on("click",Jt(Uu,p,null,i,h)).on("mouseover",Jt(lo,m.name,null,i,h)).on("mouseout",Jt(uo,m.name,null,i,h)),a.ssr&&x.eachChild(function(T){var A=Y(T);A.seriesIndex=m.seriesIndex,A.dataIndex=d,A.ssrType="legend"}),f.set(p,!0)}else a.eachRawSeries(function(T){if(!f.get(p)&&T.legendVisualProvider){var A=T.legendVisualProvider;if(!A.containName(p))return;var M=A.indexOfName(p),D=A.getItemVisual(M,"style"),C=A.getItemVisual(M,"legendIcon"),L=qh(D.fill);L&&L[3]===0&&(L[3]=.2,D=N(N({},D),{fill:jh(L,"rgba")}));var I=this._createItem(T,p,d,v,n,t,{},D,C,c,i);I.on("click",Jt(Uu,null,p,i,h)).on("mouseover",Jt(lo,null,p,i,h)).on("mouseout",Jt(uo,null,p,i,h)),a.ssr&&I.eachChild(function(P){var k=Y(P);k.seriesIndex=T.seriesIndex,k.dataIndex=d,k.ssrType="legend"}),f.set(p,!0)}},this)},this),o&&this._createSelector(o,n,i,s,l)},e.prototype._createSelector=function(t,n,a,i,o){var s=this.getSelectorGroup();so(t,function(u){var f=u.type,c=new we({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){a.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:n.id})}});s.add(c);var h=n.getModel("selectorLabel"),v=n.getModel(["emphasis","selectorLabel"]);da(c,{normal:h,emphasis:v},{defaultText:u.title}),Vn(c)})},e.prototype._createItem=function(t,n,a,i,o,s,l,u,f,c,h){var v=t.visualDrawType,d=o.get("itemWidth"),p=o.get("itemHeight"),g=o.isSelected(n),m=i.get("symbolRotate"),y=i.get("symbolKeepAspect"),_=i.get("icon");f=_||f||"roundRect";var S=cx(f,i,l,u,v,g,h),w=new Mn,x=i.getModel("textStyle");if(H(t.getLegendIcon)&&(!_||_==="inherit"))w.add(t.getLegendIcon({itemWidth:d,itemHeight:p,icon:f,iconRotate:m,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:y}));else{var T=_==="inherit"&&t.getData().getVisual("symbol")?m==="inherit"?t.getData().getVisual("symbolRotate"):m:0;w.add(vx({itemWidth:d,itemHeight:p,icon:f,iconRotate:T,itemStyle:S.itemStyle,symbolKeepAspect:y}))}var A=s==="left"?d+5:-5,M=s,D=o.get("formatter"),C=n;B(D)&&D?C=D.replace("{name}",n??""):H(D)&&(C=D(n));var L=g?x.getTextColor():i.get("inactiveColor");w.add(new we({style:zt(x,{text:C,x:A,y:p/2,fill:L,align:M,verticalAlign:"middle"},{inheritColor:L})}));var I=new fe({shape:w.getBoundingRect(),style:{fill:"transparent"}}),P=i.getModel("tooltip");return P.get("show")&&ca({el:I,componentModel:o,itemName:n,itemTooltipOption:P.option}),w.add(I),w.eachChild(function(k){k.silent=!0}),I.silent=!c,this.getContentGroup().add(w),Vn(w),w.__legendDataIndex=a,w},e.prototype.layoutInner=function(t,n,a,i,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();Nr(t.get("orient"),l,t.get("itemGap"),a.width,a.height);var f=l.getBoundingRect(),c=[-f.x,-f.y];if(u.markRedraw(),l.markRedraw(),o){Nr("horizontal",u,t.get("selectorItemGap",!0));var h=u.getBoundingRect(),v=[-h.x,-h.y],d=t.get("selectorButtonGap",!0),p=t.getOrient().index,g=p===0?"width":"height",m=p===0?"height":"width",y=p===0?"y":"x";s==="end"?v[p]+=f[g]+d:c[p]+=h[g]+d,v[1-p]+=f[m]/2-h[m]/2,u.x=v[0],u.y=v[1],l.x=c[0],l.y=c[1];var _={x:0,y:0};return _[g]=f[g]+d+h[g],_[m]=Math.max(f[m],h[m]),_[y]=Math.min(0,h[y]+v[1-p]),_}else return l.x=c[0],l.y=c[1],this.group.getBoundingRect()},e.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},e.type="legend.plain",e}(Be);function cx(r,e,t,n,a,i,o){function s(g,m){g.lineWidth==="auto"&&(g.lineWidth=m.lineWidth>0?2:0),so(g,function(y,_){g[_]==="inherit"&&(g[_]=m[_])})}var l=e.getModel("itemStyle"),u=l.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",c=l.getShallow("decal");u.decal=!c||c==="inherit"?n.decal:Ui(c,o),u.fill==="inherit"&&(u.fill=n[a]),u.stroke==="inherit"&&(u.stroke=n[f]),u.opacity==="inherit"&&(u.opacity=(a==="fill"?n:t).opacity),s(u,n);var h=e.getModel("lineStyle"),v=h.getLineStyle();if(s(v,t),u.fill==="auto"&&(u.fill=n.fill),u.stroke==="auto"&&(u.stroke=n.fill),v.stroke==="auto"&&(v.stroke=n.fill),!i){var d=e.get("inactiveBorderWidth"),p=u[f];u.lineWidth=d==="auto"?n.lineWidth>0&&p?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),v.stroke=h.get("inactiveColor"),v.lineWidth=h.get("inactiveWidth")}return{itemStyle:u,lineStyle:v}}function vx(r){var e=r.icon||"roundRect",t=Wt(e,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return t.setStyle(r.itemStyle),t.rotation=(r.iconRotate||0)*Math.PI/180,t.setOrigin([r.itemWidth/2,r.itemHeight/2]),e.indexOf("empty")>-1&&(t.style.stroke=t.style.fill,t.style.fill="#fff",t.style.lineWidth=2),t}function Uu(r,e,t,n){uo(r,e,t,n),t.dispatchAction({type:"legendToggleSelect",name:r??e}),lo(r,e,t,n)}function vh(r){for(var e=r.getZr().storage.getDisplayList(),t,n=0,a=e.length;n<a&&!(t=e[n].states.emphasis);)n++;return t&&t.hoverLayer}function lo(r,e,t,n){vh(t)||t.dispatchAction({type:"highlight",seriesName:r,name:e,excludeSeriesId:n})}function uo(r,e,t,n){vh(t)||t.dispatchAction({type:"downplay",seriesName:r,name:e,excludeSeriesId:n})}function hx(r){var e=r.findComponents({mainType:"legend"});e&&e.length&&r.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})}function Mr(r,e,t){var n=r==="allSelect"||r==="inverseSelect",a={},i=[];t.eachComponent({mainType:"legend",query:e},function(s){n?s[r]():s[r](e.name),Yu(s,a),i.push(s.componentIndex)});var o={};return t.eachComponent("legend",function(s){b(a,function(l,u){s[l?"select":"unSelect"](u)}),Yu(s,o)}),n?{selected:o,legendIndex:i}:{name:e.name,selected:o}}function Yu(r,e){var t=e||{};return b(r.getData(),function(n){var a=n.get("name");if(!(a===`
`||a==="")){var i=r.isSelected(a);Gt(t,a)?t[a]=t[a]&&i:t[a]=i}}),t}function dx(r){r.registerAction("legendToggleSelect","legendselectchanged",le(Mr,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",le(Mr,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",le(Mr,"inverseSelect")),r.registerAction("legendSelect","legendselected",le(Mr,"select")),r.registerAction("legendUnSelect","legendunselected",le(Mr,"unSelect"))}function hh(r){r.registerComponentModel(oo),r.registerComponentView(ch),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,hx),r.registerSubTypeDefaulter("legend",function(){return"plain"}),dx(r)}var px=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},e.prototype.init=function(t,n,a){var i=xa(t);r.prototype.init.call(this,t,n,a),Xu(this,t,i)},e.prototype.mergeOption=function(t,n){r.prototype.mergeOption.call(this,t,n),Xu(this,this.option,t)},e.type="legend.scroll",e.defaultOption=Wf(oo.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),e}(oo);function Xu(r,e,t){var n=r.getOrient(),a=[1,1];a[n.index]=0,ir(e,t,{type:"box",ignoreSize:!!a})}var Zu=me,_i=["width","height"],Si=["x","y"],gx=function(r){z(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!0,t._currentIndex=0,t}return e.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new Zu),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Zu)},e.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},e.prototype.renderInner=function(t,n,a,i,o,s,l){var u=this;r.prototype.renderInner.call(this,t,n,a,i,o,s,l);var f=this._controllerGroup,c=n.get("pageIconSize",!0),h=R(c)?c:[c,c];d("pagePrev",0);var v=n.getModel("pageTextStyle");f.add(new we({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(p,g){var m=p+"DataIndex",y=fa(n.get("pageIcons",!0)[n.getOrient().name][g],{onclick:Q(u._pageGo,u,m,n,i)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});y.name=p,f.add(y)}},e.prototype.layoutInner=function(t,n,a,i,o,s){var l=this.getSelectorGroup(),u=t.getOrient().index,f=_i[u],c=Si[u],h=_i[1-u],v=Si[1-u];o&&Nr("horizontal",l,t.get("selectorItemGap",!0));var d=t.get("selectorButtonGap",!0),p=l.getBoundingRect(),g=[-p.x,-p.y],m=$(a);o&&(m[f]=a[f]-p[f]-d);var y=this._layoutContentAndController(t,i,m,u,f,h,v,c);if(o){if(s==="end")g[u]+=y[f]+d;else{var _=p[f]+d;g[u]-=_,y[c]-=_}y[f]+=p[f]+d,g[1-u]+=y[v]+y[h]/2-p[h]/2,y[h]=Math.max(y[h],p[h]),y[v]=Math.min(y[v],p[v]+g[1-u]),l.x=g[0],l.y=g[1],l.markRedraw()}return y},e.prototype._layoutContentAndController=function(t,n,a,i,o,s,l,u){var f=this.getContentGroup(),c=this._containerGroup,h=this._controllerGroup;Nr(t.get("orient"),f,t.get("itemGap"),i?a.width:null,i?null:a.height),Nr("horizontal",h,t.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),d=h.getBoundingRect(),p=this._showController=v[o]>a[o],g=[-v.x,-v.y];n||(g[i]=f[u]);var m=[0,0],y=[-d.x,-d.y],_=K(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(p){var S=t.get("pageButtonPosition",!0);S==="end"?y[i]+=a[o]-d[o]:m[i]+=d[o]+_}y[1-i]+=v[s]/2-d[s]/2,f.setPosition(g),c.setPosition(m),h.setPosition(y);var w={x:0,y:0};if(w[o]=p?a[o]:v[o],w[s]=Math.max(v[s],d[s]),w[l]=Math.min(0,d[l]+y[1-i]),c.__rectSize=a[o],p){var x={x:0,y:0};x[o]=Math.max(a[o]-d[o]-_,0),x[s]=w[s],c.setClipPath(new fe({shape:x})),c.__rectSize=x[o]}else h.eachChild(function(A){A.attr({invisible:!0,silent:!0})});var T=this._getPageInfo(t);return T.pageIndex!=null&&he(f,{x:T.contentPosition[0],y:T.contentPosition[1]},p?t:null),this._updatePageInfoView(t,T),w},e.prototype._pageGo=function(t,n,a){var i=this._getPageInfo(n)[t];i!=null&&a.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:n.id})},e.prototype._updatePageInfoView=function(t,n){var a=this._controllerGroup;b(["pagePrev","pageNext"],function(f){var c=f+"DataIndex",h=n[c]!=null,v=a.childOfName(f);v&&(v.setStyle("fill",h?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),v.cursor=h?"pointer":"default")});var i=a.childOfName("pageText"),o=t.get("pageFormatter"),s=n.pageIndex,l=s!=null?s+1:0,u=n.pageCount;i&&o&&i.setStyle("text",B(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},e.prototype._getPageInfo=function(t){var n=t.get("scrollDataIndex",!0),a=this.getContentGroup(),i=this._containerGroup.__rectSize,o=t.getOrient().index,s=_i[o],l=Si[o],u=this._findTargetItemIndex(n),f=a.children(),c=f[u],h=f.length,v=h?1:0,d={contentPosition:[a.x,a.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!c)return d;var p=S(c);d.contentPosition[o]=-p.s;for(var g=u+1,m=p,y=p,_=null;g<=h;++g)_=S(f[g]),(!_&&y.e>m.s+i||_&&!w(_,m.s))&&(y.i>m.i?m=y:m=_,m&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=m.i),++d.pageCount)),y=_;for(var g=u-1,m=p,y=p,_=null;g>=-1;--g)_=S(f[g]),(!_||!w(y,_.s))&&m.i<y.i&&(y=m,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=m.i),++d.pageCount,++d.pageIndex),m=_;return d;function S(x){if(x){var T=x.getBoundingRect(),A=T[l]+x[l];return{s:A,e:A+T[s],i:x.__legendDataIndex}}}function w(x,T){return x.e>=T&&x.s<=T+i}},e.prototype._findTargetItemIndex=function(t){if(!this._showController)return 0;var n,a=this.getContentGroup(),i;return a.eachChild(function(o,s){var l=o.__legendDataIndex;i==null&&l!=null&&(i=s),l===t&&(n=s)}),n??i},e.type="legend.scroll",e}(ch);function mx(r){r.registerAction("legendScroll","legendscroll",function(e,t){var n=e.scrollDataIndex;n!=null&&t.eachComponent({mainType:"legend",subType:"scroll",query:e},function(a){a.setScrollDataIndex(n)})})}function yx(r){Ct(hh),r.registerComponentModel(px),r.registerComponentView(gx),mx(r)}function Mx(r){Ct(hh),Ct(yx)}function Ax(r){r.registerPainter("canvas",Qh)}export{Mx as a,Cx as b,Tx as c,xx as d,Ax as e,wx as f,bx as g,iy as h,Dx as i,Ct as u};
