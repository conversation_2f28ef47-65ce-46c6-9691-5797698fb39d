> 一个可视化数据大屏案例

<h1>
  <img src="./screenshots/screenshot01.png" title="screenshot">
</h1>

#### Layout

1. 实现大屏项目的整体布局，设置布局容器，留出插槽
2. 两侧图表容器使用 absolute 定位

#### LayoutHeader

1. 将图片为背景图片而不是 img 元素，可以放置子元素
1. 伪元素的使用
1. CSS 的背景图片属性使用
1. 文字滚动 CSS 动画

#### LayoutPanel

1. 封装图表容器组件
2. 图表容器的 header 和背景都是图片

#### WidgetPanel02 / useEcharts

1. 封装 echarts 使用的 hook，监听窗口 resize 事件。这里还可以用 echarts-react 库

#### Widgetpanel06

1. CSS 列表循环滚动动画

- [] 字体优化：字体文件分包
- [] 图片优化
- [] 项目打包资源压缩

- [] 其他大屏项目寻找问题
